var G1=Object.defineProperty,q1=Object.defineProperties;var W1=Object.getOwnPropertyDescriptors;var Ao=Object.getOwnPropertySymbols;var Jf=Object.prototype.hasOwnProperty,eh=Object.prototype.propertyIsEnumerable;var Xf=(e,t,n)=>t in e?G1(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,_=(e,t)=>{for(var n in t||={})Jf.call(t,n)&&Xf(e,n,t[n]);if(Ao)for(var n of Ao(t))eh.call(t,n)&&Xf(e,n,t[n]);return e},Q=(e,t)=>q1(e,W1(t));var Ol=(e,t)=>{var n={};for(var r in e)Jf.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&Ao)for(var r of Ao(e))t.indexOf(r)<0&&eh.call(e,r)&&(n[r]=e[r]);return n};var No=(e,t,n)=>new Promise((r,i)=>{var o=l=>{try{a(n.next(l))}catch(c){i(c)}},s=l=>{try{a(n.throw(l))}catch(c){i(c)}},a=l=>l.done?r(l.value):Promise.resolve(l.value).then(o,s);a((n=n.apply(e,t)).next())});function th(e,t){return Object.is(e,t)}var Ce=null,Oo=!1,Po=1,dn=Symbol("SIGNAL");function Y(e){let t=Ce;return Ce=e,t}function nh(){return Ce}var vi={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Rl(e){if(Oo)throw new Error("");if(Ce===null)return;Ce.consumerOnSignalRead(e);let t=Ce.nextProducerIndex++;if(Lo(Ce),t<Ce.producerNode.length&&Ce.producerNode[t]!==e&&yi(Ce)){let n=Ce.producerNode[t];Fo(n,Ce.producerIndexOfThis[t])}Ce.producerNode[t]!==e&&(Ce.producerNode[t]=e,Ce.producerIndexOfThis[t]=yi(Ce)?sh(e,Ce,t):0),Ce.producerLastReadVersion[t]=e.version}function Z1(){Po++}function rh(e){if(!(yi(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Po)){if(!e.producerMustRecompute(e)&&!Ll(e)){e.dirty=!1,e.lastCleanEpoch=Po;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Po}}function ih(e){if(e.liveConsumerNode===void 0)return;let t=Oo;Oo=!0;try{for(let n of e.liveConsumerNode)n.dirty||Q1(n)}finally{Oo=t}}function oh(){return Ce?.consumerAllowSignalWrites!==!1}function Q1(e){e.dirty=!0,ih(e),e.consumerMarkedDirty?.(e)}function Ro(e){return e&&(e.nextProducerIndex=0),Y(e)}function Fl(e,t){if(Y(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(yi(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Fo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Ll(e){Lo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(rh(n),r!==n.version))return!0}return!1}function Vl(e){if(Lo(e),yi(e))for(let t=0;t<e.producerNode.length;t++)Fo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function sh(e,t,n){if(ah(e),e.liveConsumerNode.length===0&&lh(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=sh(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Fo(e,t){if(ah(e),e.liveConsumerNode.length===1&&lh(e))for(let r=0;r<e.producerNode.length;r++)Fo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],i=e.liveConsumerNode[t];Lo(i),i.producerIndexOfThis[r]=t}}function yi(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Lo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function ah(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function lh(e){return e.producerNode!==void 0}function ch(e){let t=Object.create(Y1);t.computation=e;let n=()=>{if(rh(t),Rl(t),t.value===ko)throw t.error;return t.value};return n[dn]=t,n}var Pl=Symbol("UNSET"),kl=Symbol("COMPUTING"),ko=Symbol("ERRORED"),Y1=Q(_({},vi),{value:Pl,dirty:!0,error:null,equal:th,producerMustRecompute(e){return e.value===Pl||e.value===kl},producerRecomputeValue(e){if(e.value===kl)throw new Error("Detected cycle in computations.");let t=e.value;e.value=kl;let n=Ro(e),r;try{r=e.computation()}catch(i){r=ko,e.error=i}finally{Fl(e,n)}if(t!==Pl&&t!==ko&&r!==ko&&e.equal(t,r)){e.value=t;return}e.value=r,e.version++}});function K1(){throw new Error}var uh=K1;function dh(){uh()}function fh(e){uh=e}var X1=null;function hh(e){let t=Object.create(gh);t.value=e;let n=()=>(Rl(t),t.value);return n[dn]=t,n}function jl(e,t){oh()||dh(),e.equal(e.value,t)||(e.value=t,J1(e))}function ph(e,t){oh()||dh(),jl(e,t(e.value))}var gh=Q(_({},vi),{equal:th,value:void 0});function J1(e){e.version++,Z1(),ih(e),X1?.()}function k(e){return typeof e=="function"}function Cr(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Vo=Cr(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,i)=>`${i+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Ci(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var ce=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let o of n)o.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(k(r))try{r()}catch(o){t=o instanceof Vo?o.errors:[o]}let{_finalizers:i}=this;if(i){this._finalizers=null;for(let o of i)try{mh(o)}catch(s){t=t??[],s instanceof Vo?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Vo(t)}}add(t){var n;if(t&&t!==this)if(this.closed)mh(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Ci(n,t)}remove(t){let{_finalizers:n}=this;n&&Ci(n,t),t instanceof e&&t._removeParent(this)}};ce.EMPTY=(()=>{let e=new ce;return e.closed=!0,e})();var Bl=ce.EMPTY;function jo(e){return e instanceof ce||e&&"closed"in e&&k(e.remove)&&k(e.add)&&k(e.unsubscribe)}function mh(e){k(e)?e():e.unsubscribe()}var pt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Dr={setTimeout(e,t,...n){let{delegate:r}=Dr;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Dr;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Bo(e){Dr.setTimeout(()=>{let{onUnhandledError:t}=pt;if(t)t(e);else throw e})}function Di(){}var yh=Ul("C",void 0,void 0);function vh(e){return Ul("E",void 0,e)}function Ch(e){return Ul("N",e,void 0)}function Ul(e,t,n){return{kind:e,value:t,error:n}}var Un=null;function wr(e){if(pt.useDeprecatedSynchronousErrorHandling){let t=!Un;if(t&&(Un={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Un;if(Un=null,n)throw r}}else e()}function Dh(e){pt.useDeprecatedSynchronousErrorHandling&&Un&&(Un.errorThrown=!0,Un.error=e)}var $n=class extends ce{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,jo(t)&&t.add(this)):this.destination=nv}static create(t,n,r){return new _r(t,n,r)}next(t){this.isStopped?Hl(Ch(t),this):this._next(t)}error(t){this.isStopped?Hl(vh(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Hl(yh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},ev=Function.prototype.bind;function $l(e,t){return ev.call(e,t)}var zl=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Uo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Uo(r)}else Uo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Uo(n)}}},_r=class extends $n{constructor(t,n,r){super();let i;if(k(t)||!t)i={next:t??void 0,error:n??void 0,complete:r??void 0};else{let o;this&&pt.useDeprecatedNextContext?(o=Object.create(t),o.unsubscribe=()=>this.unsubscribe(),i={next:t.next&&$l(t.next,o),error:t.error&&$l(t.error,o),complete:t.complete&&$l(t.complete,o)}):i=t}this.destination=new zl(i)}};function Uo(e){pt.useDeprecatedSynchronousErrorHandling?Dh(e):Bo(e)}function tv(e){throw e}function Hl(e,t){let{onStoppedNotification:n}=pt;n&&Dr.setTimeout(()=>n(e,t))}var nv={closed:!0,next:Di,error:tv,complete:Di};var br=typeof Symbol=="function"&&Symbol.observable||"@@observable";function We(e){return e}function Gl(...e){return ql(e)}function ql(e){return e.length===0?We:e.length===1?e[0]:function(n){return e.reduce((r,i)=>i(r),n)}}var G=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,i){let o=iv(n)?n:new _r(n,r,i);return wr(()=>{let{operator:s,source:a}=this;o.add(s?s.call(o,a):a?this._subscribe(o):this._trySubscribe(o))}),o}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=wh(r),new r((i,o)=>{let s=new _r({next:a=>{try{n(a)}catch(l){o(l),s.unsubscribe()}},error:o,complete:i});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[br](){return this}pipe(...n){return ql(n)(this)}toPromise(n){return n=wh(n),new n((r,i)=>{let o;this.subscribe(s=>o=s,s=>i(s),()=>r(o))})}}return e.create=t=>new e(t),e})();function wh(e){var t;return(t=e??pt.Promise)!==null&&t!==void 0?t:Promise}function rv(e){return e&&k(e.next)&&k(e.error)&&k(e.complete)}function iv(e){return e&&e instanceof $n||rv(e)&&jo(e)}function Wl(e){return k(e?.lift)}function q(e){return t=>{if(Wl(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function H(e,t,n,r,i){return new Zl(e,t,n,r,i)}var Zl=class extends $n{constructor(t,n,r,i,o,s){super(t),this.onFinalize=o,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){t.error(l)}}:super._next,this._error=i?function(a){try{i(a)}catch(l){t.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Er(){return q((e,t)=>{let n=null;e._refCount++;let r=H(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let i=e._connection,o=n;n=null,i&&(!o||i===o)&&i.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Mr=class extends G{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Wl(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new ce;let n=this.getSubject();t.add(this.source.subscribe(H(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=ce.EMPTY)}return t}refCount(){return Er()(this)}};var _h=Cr(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Ie=(()=>{class e extends G{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new $o(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new _h}next(n){wr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){wr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){wr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:i,observers:o}=this;return r||i?Bl:(this.currentObservers=null,o.push(n),new ce(()=>{this.currentObservers=null,Ci(o,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:i,isStopped:o}=this;r?n.error(i):o&&n.complete()}asObservable(){let n=new G;return n.source=this,n}}return e.create=(t,n)=>new $o(t,n),e})(),$o=class extends Ie{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Bl}};var De=class extends Ie{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Ze=new G(e=>e.complete());function bh(e){return e&&k(e.schedule)}function Eh(e){return e[e.length-1]}function Ho(e){return k(Eh(e))?e.pop():void 0}function fn(e){return bh(Eh(e))?e.pop():void 0}function Ih(e,t,n,r){function i(o){return o instanceof n?o:new n(function(s){s(o)})}return new(n||(n=Promise))(function(o,s){function a(u){try{c(r.next(u))}catch(d){s(d)}}function l(u){try{c(r.throw(u))}catch(d){s(d)}}function c(u){u.done?o(u.value):i(u.value).then(a,l)}c((r=r.apply(e,t||[])).next())})}function Mh(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Hn(e){return this instanceof Hn?(this.v=e,this):new Hn(e)}function Sh(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),i,o=[];return i={},a("next"),a("throw"),a("return",s),i[Symbol.asyncIterator]=function(){return this},i;function s(f){return function(m){return Promise.resolve(m).then(f,d)}}function a(f,m){r[f]&&(i[f]=function(v){return new Promise(function(w,S){o.push([f,v,w,S])>1||l(f,v)})},m&&(i[f]=m(i[f])))}function l(f,m){try{c(r[f](m))}catch(v){g(o[0][3],v)}}function c(f){f.value instanceof Hn?Promise.resolve(f.value.v).then(u,d):g(o[0][2],f)}function u(f){l("next",f)}function d(f){l("throw",f)}function g(f,m){f(m),o.shift(),o.length&&l(o[0][0],o[0][1])}}function xh(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Mh=="function"?Mh(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(o){n[o]=e[o]&&function(s){return new Promise(function(a,l){s=e[o](s),i(a,l,s.done,s.value)})}}function i(o,s,a,l){Promise.resolve(l).then(function(c){o({value:c,done:a})},s)}}var zo=e=>e&&typeof e.length=="number"&&typeof e!="function";function Go(e){return k(e?.then)}function qo(e){return k(e[br])}function Wo(e){return Symbol.asyncIterator&&k(e?.[Symbol.asyncIterator])}function Zo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function ov(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Qo=ov();function Yo(e){return k(e?.[Qo])}function Ko(e){return Sh(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:i}=yield Hn(n.read());if(i)return yield Hn(void 0);yield yield Hn(r)}}finally{n.releaseLock()}})}function Xo(e){return k(e?.getReader)}function ge(e){if(e instanceof G)return e;if(e!=null){if(qo(e))return sv(e);if(zo(e))return av(e);if(Go(e))return lv(e);if(Wo(e))return Th(e);if(Yo(e))return cv(e);if(Xo(e))return uv(e)}throw Zo(e)}function sv(e){return new G(t=>{let n=e[br]();if(k(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function av(e){return new G(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function lv(e){return new G(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Bo)})}function cv(e){return new G(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Th(e){return new G(t=>{dv(e,t).catch(n=>t.error(n))})}function uv(e){return Th(Ko(e))}function dv(e,t){var n,r,i,o;return Ih(this,void 0,void 0,function*(){try{for(n=xh(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=n.return)&&(yield o.call(n))}finally{if(i)throw i.error}}t.complete()})}function Ve(e,t,n,r=0,i=!1){let o=t.schedule(function(){n(),i?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(o),!i)return o}function Jo(e,t=0){return q((n,r)=>{n.subscribe(H(r,i=>Ve(r,e,()=>r.next(i),t),()=>Ve(r,e,()=>r.complete(),t),i=>Ve(r,e,()=>r.error(i),t)))})}function es(e,t=0){return q((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Ah(e,t){return ge(e).pipe(es(t),Jo(t))}function Nh(e,t){return ge(e).pipe(es(t),Jo(t))}function Oh(e,t){return new G(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Ph(e,t){return new G(n=>{let r;return Ve(n,t,()=>{r=e[Qo](),Ve(n,t,()=>{let i,o;try{({value:i,done:o}=r.next())}catch(s){n.error(s);return}o?n.complete():n.next(i)},0,!0)}),()=>k(r?.return)&&r.return()})}function ts(e,t){if(!e)throw new Error("Iterable cannot be null");return new G(n=>{Ve(n,t,()=>{let r=e[Symbol.asyncIterator]();Ve(n,t,()=>{r.next().then(i=>{i.done?n.complete():n.next(i.value)})},0,!0)})})}function kh(e,t){return ts(Ko(e),t)}function Rh(e,t){if(e!=null){if(qo(e))return Ah(e,t);if(zo(e))return Oh(e,t);if(Go(e))return Nh(e,t);if(Wo(e))return ts(e,t);if(Yo(e))return Ph(e,t);if(Xo(e))return kh(e,t)}throw Zo(e)}function le(e,t){return t?Rh(e,t):ge(e)}function N(...e){let t=fn(e);return le(e,t)}function Ir(e,t){let n=k(e)?e:()=>e,r=i=>i.error(n());return new G(t?i=>t.schedule(r,0,i):r)}function Ql(e){return!!e&&(e instanceof G||k(e.lift)&&k(e.subscribe))}var Bt=Cr(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function R(e,t){return q((n,r)=>{let i=0;n.subscribe(H(r,o=>{r.next(e.call(t,o,i++))}))})}var{isArray:fv}=Array;function hv(e,t){return fv(t)?e(...t):e(t)}function ns(e){return R(t=>hv(e,t))}var{isArray:pv}=Array,{getPrototypeOf:gv,prototype:mv,keys:yv}=Object;function rs(e){if(e.length===1){let t=e[0];if(pv(t))return{args:t,keys:null};if(vv(t)){let n=yv(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function vv(e){return e&&typeof e=="object"&&gv(e)===mv}function is(e,t){return e.reduce((n,r,i)=>(n[r]=t[i],n),{})}function wi(...e){let t=fn(e),n=Ho(e),{args:r,keys:i}=rs(e);if(r.length===0)return le([],t);let o=new G(Cv(r,t,i?s=>is(i,s):We));return n?o.pipe(ns(n)):o}function Cv(e,t,n=We){return r=>{Fh(t,()=>{let{length:i}=e,o=new Array(i),s=i,a=i;for(let l=0;l<i;l++)Fh(t,()=>{let c=le(e[l],t),u=!1;c.subscribe(H(r,d=>{o[l]=d,u||(u=!0,a--),a||r.next(n(o.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Fh(e,t,n){e?Ve(n,e,t):t()}function Lh(e,t,n,r,i,o,s,a){let l=[],c=0,u=0,d=!1,g=()=>{d&&!l.length&&!c&&t.complete()},f=v=>c<r?m(v):l.push(v),m=v=>{o&&t.next(v),c++;let w=!1;ge(n(v,u++)).subscribe(H(t,S=>{i?.(S),o?f(S):t.next(S)},()=>{w=!0},void 0,()=>{if(w)try{for(c--;l.length&&c<r;){let S=l.shift();s?Ve(t,s,()=>m(S)):m(S)}g()}catch(S){t.error(S)}}))};return e.subscribe(H(t,f,()=>{d=!0,g()})),()=>{a?.()}}function me(e,t,n=1/0){return k(t)?me((r,i)=>R((o,s)=>t(r,o,i,s))(ge(e(r,i))),n):(typeof t=="number"&&(n=t),q((r,i)=>Lh(r,i,e,n)))}function Sr(e=1/0){return me(We,e)}function Vh(){return Sr(1)}function xr(...e){return Vh()(le(e,fn(e)))}function os(e){return new G(t=>{ge(e()).subscribe(t)})}function Yl(...e){let t=Ho(e),{args:n,keys:r}=rs(e),i=new G(o=>{let{length:s}=n;if(!s){o.complete();return}let a=new Array(s),l=s,c=s;for(let u=0;u<s;u++){let d=!1;ge(n[u]).subscribe(H(o,g=>{d||(d=!0,c--),a[u]=g},()=>l--,void 0,()=>{(!l||!d)&&(c||o.next(r?is(r,a):a),o.complete())}))}});return t?i.pipe(ns(t)):i}function je(e,t){return q((n,r)=>{let i=0;n.subscribe(H(r,o=>e.call(t,o,i++)&&r.next(o)))})}function hn(e){return q((t,n)=>{let r=null,i=!1,o;r=t.subscribe(H(n,void 0,void 0,s=>{o=ge(e(s,hn(e)(t))),r?(r.unsubscribe(),r=null,o.subscribe(n)):i=!0})),i&&(r.unsubscribe(),r=null,o.subscribe(n))})}function jh(e,t,n,r,i){return(o,s)=>{let a=n,l=t,c=0;o.subscribe(H(s,u=>{let d=c++;l=a?e(l,u,d):(a=!0,u),r&&s.next(l)},i&&(()=>{a&&s.next(l),s.complete()})))}}function Ut(e,t){return k(t)?me(e,t,1):me(e,1)}function pn(e){return q((t,n)=>{let r=!1;t.subscribe(H(n,i=>{r=!0,n.next(i)},()=>{r||n.next(e),n.complete()}))})}function $t(e){return e<=0?()=>Ze:q((t,n)=>{let r=0;t.subscribe(H(n,i=>{++r<=e&&(n.next(i),e<=r&&n.complete())}))})}function Kl(e){return R(()=>e)}function ss(e=Dv){return q((t,n)=>{let r=!1;t.subscribe(H(n,i=>{r=!0,n.next(i)},()=>r?n.complete():n.error(e())))})}function Dv(){return new Bt}function gn(e){return q((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Mt(e,t){let n=arguments.length>=2;return r=>r.pipe(e?je((i,o)=>e(i,o,r)):We,$t(1),n?pn(t):ss(()=>new Bt))}function Tr(e){return e<=0?()=>Ze:q((t,n)=>{let r=[];t.subscribe(H(n,i=>{r.push(i),e<r.length&&r.shift()},()=>{for(let i of r)n.next(i);n.complete()},void 0,()=>{r=null}))})}function Xl(e,t){let n=arguments.length>=2;return r=>r.pipe(e?je((i,o)=>e(i,o,r)):We,Tr(1),n?pn(t):ss(()=>new Bt))}function Jl(e,t){return q(jh(e,t,arguments.length>=2,!0))}function ec(...e){let t=fn(e);return q((n,r)=>{(t?xr(e,n,t):xr(e,n)).subscribe(r)})}function Be(e,t){return q((n,r)=>{let i=null,o=0,s=!1,a=()=>s&&!i&&r.complete();n.subscribe(H(r,l=>{i?.unsubscribe();let c=0,u=o++;ge(e(l,u)).subscribe(i=H(r,d=>r.next(t?t(l,d,u,c++):d),()=>{i=null,a()}))},()=>{s=!0,a()}))})}function tc(e){return q((t,n)=>{ge(e).subscribe(H(n,()=>n.complete(),Di)),!n.closed&&t.subscribe(n)})}function Se(e,t,n){let r=k(e)||t||n?{next:e,error:t,complete:n}:e;return r?q((i,o)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;i.subscribe(H(o,l=>{var c;(c=r.next)===null||c===void 0||c.call(r,l),o.next(l)},()=>{var l;a=!1,(l=r.complete)===null||l===void 0||l.call(r),o.complete()},l=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,l),o.error(l)},()=>{var l,c;a&&((l=r.unsubscribe)===null||l===void 0||l.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):We}var Ip="https://g.co/ng/security#xss",C=class extends Error{constructor(t,n){super(zs(t,n)),this.code=t}};function zs(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function Oi(e){return{toString:e}.toString()}var as="__parameters__";function wv(e){return function(...n){if(e){let r=e(...n);for(let i in r)this[i]=r[i]}}}function Sp(e,t,n){return Oi(()=>{let r=wv(t);function i(...o){if(this instanceof i)return r.apply(this,o),this;let s=new i(...o);return a.annotation=s,a;function a(l,c,u){let d=l.hasOwnProperty(as)?l[as]:Object.defineProperty(l,as,{value:[]})[as];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}var Te=globalThis;function re(e){for(let t in e)if(e[t]===re)return t;throw Error("Could not find renamed property on target object.")}function _v(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Re(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(Re).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Bh(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var bv=re({__forward_ref__:re});function Gs(e){return e.__forward_ref__=Gs,e.toString=function(){return Re(this())},e}function ke(e){return xp(e)?e():e}function xp(e){return typeof e=="function"&&e.hasOwnProperty(bv)&&e.__forward_ref__===Gs}function b(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function we(e){return{providers:e.providers||[],imports:e.imports||[]}}function qs(e){return Uh(e,Ap)||Uh(e,Np)}function Tp(e){return qs(e)!==null}function Uh(e,t){return e.hasOwnProperty(t)?e[t]:null}function Ev(e){let t=e&&(e[Ap]||e[Np]);return t||null}function $h(e){return e&&(e.hasOwnProperty(Hh)||e.hasOwnProperty(Mv))?e[Hh]:null}var Ap=re({\u0275prov:re}),Hh=re({\u0275inj:re}),Np=re({ngInjectableDef:re}),Mv=re({ngInjectorDef:re}),M=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=b({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Op(e){return e&&!!e.\u0275providers}var Iv=re({\u0275cmp:re}),Sv=re({\u0275dir:re}),xv=re({\u0275pipe:re}),Tv=re({\u0275mod:re}),vs=re({\u0275fac:re}),bi=re({__NG_ELEMENT_ID__:re}),zh=re({__NG_ENV_ID__:re});function Rr(e){return typeof e=="string"?e:e==null?"":String(e)}function Av(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Rr(e)}function Nv(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new C(-200,e)}function cu(e,t){throw new C(-201,!1)}var V=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(V||{}),mc;function Pp(){return mc}function Ue(e){let t=mc;return mc=e,t}function kp(e,t,n){let r=qs(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&V.Optional)return null;if(t!==void 0)return t;cu(e,"Injector")}var Ov={},Mi=Ov,yc="__NG_DI_FLAG__",Cs="ngTempTokenPath",Pv="ngTokenPath",kv=/\n/gm,Rv="\u0275",Gh="__source",Pr;function Fv(){return Pr}function mn(e){let t=Pr;return Pr=e,t}function Lv(e,t=V.Default){if(Pr===void 0)throw new C(-203,!1);return Pr===null?kp(e,void 0,t):Pr.get(e,t&V.Optional?null:void 0,t)}function E(e,t=V.Default){return(Pp()||Lv)(ke(e),t)}function D(e,t=V.Default){return E(e,Ws(t))}function Ws(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function vc(e){let t=[];for(let n=0;n<e.length;n++){let r=ke(e[n]);if(Array.isArray(r)){if(r.length===0)throw new C(900,!1);let i,o=V.Default;for(let s=0;s<r.length;s++){let a=r[s],l=Vv(a);typeof l=="number"?l===-1?i=a.token:o|=l:i=a}t.push(E(i,o))}else t.push(E(r))}return t}function Rp(e,t){return e[yc]=t,e.prototype[yc]=t,e}function Vv(e){return e[yc]}function jv(e,t,n,r){let i=e[Cs];throw t[Gh]&&i.unshift(t[Gh]),e.message=Bv(`
`+e.message,i,n,r),e[Pv]=i,e[Cs]=null,e}function Bv(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Rv?e.slice(2):e;let i=Re(t);if(Array.isArray(t))i=t.map(Re).join(" -> ");else if(typeof t=="object"){let o=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];o.push(s+":"+(typeof a=="string"?JSON.stringify(a):Re(a)))}i=`{${o.join(", ")}}`}return`${n}${r?"("+r+")":""}[${i}]: ${e.replace(kv,`
  `)}`}var Zs=Rp(Sp("Optional"),8);var uu=Rp(Sp("SkipSelf"),4);function qn(e,t){let n=e.hasOwnProperty(vs);return n?e[vs]:null}function Uv(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let i=e[r],o=t[r];if(n&&(i=n(i),o=n(o)),o!==i)return!1}return!0}function $v(e){return e.flat(Number.POSITIVE_INFINITY)}function du(e,t){e.forEach(n=>Array.isArray(n)?du(n,t):t(n))}function Fp(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ds(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Hv(e,t,n,r){let i=e.length;if(i==t)e.push(n,r);else if(i===1)e.push(r,e[0]),e[0]=n;else{for(i--,e.push(e[i-1],e[i]);i>t;){let o=i-2;e[i]=e[o],i--}e[t]=n,e[t+1]=r}}function zv(e,t,n){let r=Pi(e,t);return r>=0?e[r|1]=n:(r=~r,Hv(e,r,t,n)),r}function nc(e,t){let n=Pi(e,t);if(n>=0)return e[n|1]}function Pi(e,t){return Gv(e,t,1)}function Gv(e,t,n){let r=0,i=e.length>>n;for(;i!==r;){let o=r+(i-r>>1),s=e[o<<n];if(t===s)return o<<n;s>t?i=o:r=o+1}return~(i<<n)}var Fr={},it=[],Lr=new M(""),Lp=new M("",-1),Vp=new M(""),ws=class{get(t,n=Mi){if(n===Mi){let r=new Error(`NullInjectorError: No provider for ${Re(t)}!`);throw r.name="NullInjectorError",r}return n}},jp=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(jp||{}),xt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(xt||{}),Cn=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Cn||{});function qv(e,t,n){let r=e.length;for(;;){let i=e.indexOf(t,n);if(i===-1)return i;if(i===0||e.charCodeAt(i-1)<=32){let o=t.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}n=i+1}}function Cc(e,t,n){let r=0;for(;r<n.length;){let i=n[r];if(typeof i=="number"){if(i!==0)break;r++;let o=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,o)}else{let o=i,s=n[++r];Wv(o)?e.setProperty(t,o,s):e.setAttribute(t,o,s),r++}}return r}function Bp(e){return e===3||e===4||e===6}function Wv(e){return e.charCodeAt(0)===64}function Ii(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let i=t[r];typeof i=="number"?n=i:n===0||(n===-1||n===2?qh(e,n,i,null,t[++r]):qh(e,n,i,null,null))}}return e}function qh(e,t,n,r,i){let o=0,s=e.length;if(t===-1)s=-1;else for(;o<e.length;){let a=e[o++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=o-1;break}}}for(;o<e.length;){let a=e[o];if(typeof a=="number")break;if(a===n){if(r===null){i!==null&&(e[o+1]=i);return}else if(r===e[o+1]){e[o+2]=i;return}}o++,r!==null&&o++,i!==null&&o++}s!==-1&&(e.splice(s,0,t),o=s+1),e.splice(o++,0,n),r!==null&&e.splice(o++,0,r),i!==null&&e.splice(o++,0,i)}var Up="ng-template";function Zv(e,t,n,r){let i=0;if(r){for(;i<t.length&&typeof t[i]=="string";i+=2)if(t[i]==="class"&&qv(t[i+1].toLowerCase(),n,0)!==-1)return!0}else if(fu(e))return!1;if(i=t.indexOf(1,i),i>-1){let o;for(;++i<t.length&&typeof(o=t[i])=="string";)if(o.toLowerCase()===n)return!0}return!1}function fu(e){return e.type===4&&e.value!==Up}function Qv(e,t,n){let r=e.type===4&&!n?Up:e.value;return t===r}function Yv(e,t,n){let r=4,i=e.attrs,o=i!==null?Jv(i):0,s=!1;for(let a=0;a<t.length;a++){let l=t[a];if(typeof l=="number"){if(!s&&!gt(r)&&!gt(l))return!1;if(s&&gt(l))continue;s=!1,r=l|r&1;continue}if(!s)if(r&4){if(r=2|r&1,l!==""&&!Qv(e,l,n)||l===""&&t.length===1){if(gt(r))return!1;s=!0}}else if(r&8){if(i===null||!Zv(e,i,l,n)){if(gt(r))return!1;s=!0}}else{let c=t[++a],u=Kv(l,i,fu(e),n);if(u===-1){if(gt(r))return!1;s=!0;continue}if(c!==""){let d;if(u>o?d="":d=i[u+1].toLowerCase(),r&2&&c!==d){if(gt(r))return!1;s=!0}}}}return gt(r)||s}function gt(e){return(e&1)===0}function Kv(e,t,n,r){if(t===null)return-1;let i=0;if(r||!n){let o=!1;for(;i<t.length;){let s=t[i];if(s===e)return i;if(s===3||s===6)o=!0;else if(s===1||s===2){let a=t[++i];for(;typeof a=="string";)a=t[++i];continue}else{if(s===4)break;if(s===0){i+=4;continue}}i+=o?1:2}return-1}else return eC(t,e)}function Xv(e,t,n=!1){for(let r=0;r<t.length;r++)if(Yv(e,t[r],n))return!0;return!1}function Jv(e){for(let t=0;t<e.length;t++){let n=e[t];if(Bp(n))return t}return e.length}function eC(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Wh(e,t){return e?":not("+t.trim()+")":t}function tC(e){let t=e[0],n=1,r=2,i="",o=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];i+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?i+="."+s:r&4&&(i+=" "+s);else i!==""&&!gt(s)&&(t+=Wh(o,i),i=""),r=s,o=o||!gt(r);n++}return i!==""&&(t+=Wh(o,i)),t}function nC(e){return e.map(tC).join(",")}function rC(e){let t=[],n=[],r=1,i=2;for(;r<e.length;){let o=e[r];if(typeof o=="string")i===2?o!==""&&t.push(o,e[++r]):i===8&&n.push(o);else{if(!gt(i))break;i=o}r++}return{attrs:t,classes:n}}function fe(e){return Oi(()=>{let t=Wp(e),n=Q(_({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===jp.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||xt.Emulated,styles:e.styles||it,_:null,schemas:e.schemas||null,tView:null,id:""});Zp(n);let r=e.dependencies;return n.directiveDefs=Qh(r,!1),n.pipeDefs=Qh(r,!0),n.id=sC(n),n})}function iC(e){return Dn(e)||Hp(e)}function oC(e){return e!==null}function _e(e){return Oi(()=>({type:e.type,bootstrap:e.bootstrap||it,declarations:e.declarations||it,imports:e.imports||it,exports:e.exports||it,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Zh(e,t){if(e==null)return Fr;let n={};for(let r in e)if(e.hasOwnProperty(r)){let i=e[r],o,s,a=Cn.None;Array.isArray(i)?(a=i[0],o=i[1],s=i[2]??o):(o=i,s=i),t?(n[o]=a!==Cn.None?[r,a]:r,t[o]=s):n[o]=r}return n}function yt(e){return Oi(()=>{let t=Wp(e);return Zp(t),t})}function $p(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Dn(e){return e[Iv]||null}function Hp(e){return e[Sv]||null}function zp(e){return e[xv]||null}function Gp(e){let t=Dn(e)||Hp(e)||zp(e);return t!==null?t.standalone:!1}function qp(e,t){let n=e[Tv]||null;if(!n&&t===!0)throw new Error(`Type ${Re(e)} does not have '\u0275mod' property.`);return n}function Wp(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Fr,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||it,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Zh(e.inputs,t),outputs:Zh(e.outputs),debugInfo:null}}function Zp(e){e.features?.forEach(t=>t(e))}function Qh(e,t){if(!e)return null;let n=t?zp:iC;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(oC)}function sC(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let i of n)t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=**********,"c"+t}function Qs(e){return{\u0275providers:e}}function aC(...e){return{\u0275providers:Qp(!0,e),\u0275fromNgModule:!0}}function Qp(e,...t){let n=[],r=new Set,i,o=s=>{n.push(s)};return du(t,s=>{let a=s;Dc(a,o,[],r)&&(i||=[],i.push(a))}),i!==void 0&&Yp(i,o),n}function Yp(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:i}=e[n];hu(i,o=>{t(o,r)})}}function Dc(e,t,n,r){if(e=ke(e),!e)return!1;let i=null,o=$h(e),s=!o&&Dn(e);if(!o&&!s){let l=e.ngModule;if(o=$h(l),o)i=l;else return!1}else{if(s&&!s.standalone)return!1;i=e}let a=r.has(i);if(s){if(a)return!1;if(r.add(i),s.dependencies){let l=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of l)Dc(c,t,n,r)}}else if(o){if(o.imports!=null&&!a){r.add(i);let c;try{du(o.imports,u=>{Dc(u,t,n,r)&&(c||=[],c.push(u))})}finally{}c!==void 0&&Yp(c,t)}if(!a){let c=qn(i)||(()=>new i);t({provide:i,useFactory:c,deps:it},i),t({provide:Vp,useValue:i,multi:!0},i),t({provide:Lr,useValue:()=>E(i),multi:!0},i)}let l=o.providers;if(l!=null&&!a){let c=e;hu(l,u=>{t(u,c)})}}else return!1;return i!==e&&e.providers!==void 0}function hu(e,t){for(let n of e)Op(n)&&(n=n.\u0275providers),Array.isArray(n)?hu(n,t):t(n)}var lC=re({provide:String,useValue:re});function Kp(e){return e!==null&&typeof e=="object"&&lC in e}function cC(e){return!!(e&&e.useExisting)}function uC(e){return!!(e&&e.useFactory)}function Vr(e){return typeof e=="function"}function dC(e){return!!e.useClass}var Ys=new M(""),hs={},fC={},rc;function pu(){return rc===void 0&&(rc=new ws),rc}var Fe=class{},Si=class extends Fe{get destroyed(){return this._destroyed}constructor(t,n,r,i){super(),this.parent=n,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,_c(t,s=>this.processProvider(s)),this.records.set(Lp,Ar(void 0,this)),i.has("environment")&&this.records.set(Fe,Ar(void 0,this));let o=this.records.get(Ys);o!=null&&typeof o.value=="string"&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(Vp,it,V.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=Y(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),Y(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=mn(this),r=Ue(void 0),i;try{return t()}finally{mn(n),Ue(r)}}get(t,n=Mi,r=V.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(zh))return t[zh](this);r=Ws(r);let i,o=mn(this),s=Ue(void 0);try{if(!(r&V.SkipSelf)){let l=this.records.get(t);if(l===void 0){let c=yC(t)&&qs(t);c&&this.injectableDefInScope(c)?l=Ar(wc(t),hs):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l)}let a=r&V.Self?pu():this.parent;return n=r&V.Optional&&n===Mi?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Cs]=a[Cs]||[]).unshift(Re(t)),o)throw a;return jv(a,t,"R3InjectorError",this.source)}else throw a}finally{Ue(s),mn(o)}}resolveInjectorInitializers(){let t=Y(null),n=mn(this),r=Ue(void 0),i;try{let o=this.get(Lr,it,V.Self);for(let s of o)s()}finally{mn(n),Ue(r),Y(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Re(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new C(205,!1)}processProvider(t){t=ke(t);let n=Vr(t)?t:ke(t&&t.provide),r=pC(t);if(!Vr(t)&&t.multi===!0){let i=this.records.get(n);i||(i=Ar(void 0,hs,!0),i.factory=()=>vc(i.multi),this.records.set(n,i)),n=t,i.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=Y(null);try{return n.value===hs&&(n.value=fC,n.value=n.factory()),typeof n.value=="object"&&n.value&&mC(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{Y(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ke(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function wc(e){let t=qs(e),n=t!==null?t.factory:qn(e);if(n!==null)return n;if(e instanceof M)throw new C(204,!1);if(e instanceof Function)return hC(e);throw new C(204,!1)}function hC(e){if(e.length>0)throw new C(204,!1);let n=Ev(e);return n!==null?()=>n.factory(e):()=>new e}function pC(e){if(Kp(e))return Ar(void 0,e.useValue);{let t=Xp(e);return Ar(t,hs)}}function Xp(e,t,n){let r;if(Vr(e)){let i=ke(e);return qn(i)||wc(i)}else if(Kp(e))r=()=>ke(e.useValue);else if(uC(e))r=()=>e.useFactory(...vc(e.deps||[]));else if(cC(e))r=()=>E(ke(e.useExisting));else{let i=ke(e&&(e.useClass||e.provide));if(gC(e))r=()=>new i(...vc(e.deps));else return qn(i)||wc(i)}return r}function Ar(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function gC(e){return!!e.deps}function mC(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function yC(e){return typeof e=="function"||typeof e=="object"&&e instanceof M}function _c(e,t){for(let n of e)Array.isArray(n)?_c(n,t):n&&Op(n)?_c(n.\u0275providers,t):t(n)}function Ke(e,t){e instanceof Si&&e.assertNotDestroyed();let n,r=mn(e),i=Ue(void 0);try{return t()}finally{mn(r),Ue(i)}}function Jp(){return Pp()!==void 0||Fv()!=null}function vC(e){if(!Jp())throw new C(-203,!1)}function CC(e){let t=Te.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function DC(e){return typeof e=="function"}var qt=0,F=1,O=2,Pe=3,mt=4,vt=5,_s=6,bs=7,Tt=8,jr=9,At=10,Ae=11,xi=12,Yh=13,Gr=14,Nt=15,Wn=16,Nr=17,Ht=18,Ks=19,eg=20,yn=21,ic=22,ot=23,Ot=25,tg=1;var Zn=7,Es=8,Br=9,Qe=10,Ms=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(Ms||{});function vn(e){return Array.isArray(e)&&typeof e[tg]=="object"}function Wt(e){return Array.isArray(e)&&e[tg]===!0}function ng(e){return(e.flags&4)!==0}function Xs(e){return e.componentOffset>-1}function gu(e){return(e.flags&1)===1}function wn(e){return!!e.template}function bc(e){return(e[O]&512)!==0}var Ec=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function rg(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function qr(){return ig}function ig(e){return e.type.prototype.ngOnChanges&&(e.setInput=_C),wC}qr.ngInherit=!0;function wC(){let e=sg(this),t=e?.current;if(t){let n=e.previous;if(n===Fr)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function _C(e,t,n,r,i){let o=this.declaredInputs[r],s=sg(e)||bC(e,{previous:Fr,current:null}),a=s.current||(s.current={}),l=s.previous,c=l[o];a[o]=new Ec(c&&c.currentValue,n,l===Fr),rg(e,t,i,n)}var og="__ngSimpleChanges__";function sg(e){return e[og]||null}function bC(e,t){return e[og]=t}var Kh=null;var It=function(e,t,n){Kh?.(e,t,n)},ag="svg",EC="math";function Pt(e){for(;Array.isArray(e);)e=e[qt];return e}function lg(e,t){return Pt(t[e])}function st(e,t){return Pt(t[e.index])}function cg(e,t){return e.data[t]}function MC(e,t){return e[t]}function En(e,t){let n=t[e];return vn(n)?n:n[qt]}function IC(e){return(e[O]&4)===4}function mu(e){return(e[O]&128)===128}function SC(e){return Wt(e[Pe])}function Is(e,t){return t==null?null:e[t]}function ug(e){e[Nr]=0}function dg(e){e[O]&1024||(e[O]|=1024,mu(e)&&Js(e))}function xC(e,t){for(;e>0;)t=t[Gr],e--;return t}function Ti(e){return!!(e[O]&9216||e[ot]?.dirty)}function Mc(e){e[At].changeDetectionScheduler?.notify(7),e[O]&64&&(e[O]|=1024),Ti(e)&&Js(e)}function Js(e){e[At].changeDetectionScheduler?.notify(0);let t=Qn(e);for(;t!==null&&!(t[O]&8192||(t[O]|=8192,!mu(t)));)t=Qn(t)}function fg(e,t){if((e[O]&256)===256)throw new C(911,!1);e[yn]===null&&(e[yn]=[]),e[yn].push(t)}function TC(e,t){if(e[yn]===null)return;let n=e[yn].indexOf(t);n!==-1&&e[yn].splice(n,1)}function Qn(e){let t=e[Pe];return Wt(t)?t[Pe]:t}var j={lFrame:_g(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var hg=!1;function AC(){return j.lFrame.elementDepthCount}function NC(){j.lFrame.elementDepthCount++}function OC(){j.lFrame.elementDepthCount--}function pg(){return j.bindingsEnabled}function PC(){return j.skipHydrationRootTNode!==null}function kC(e){return j.skipHydrationRootTNode===e}function RC(){j.skipHydrationRootTNode=null}function ee(){return j.lFrame.lView}function He(){return j.lFrame.tView}function ze(){let e=gg();for(;e!==null&&e.type===64;)e=e.parent;return e}function gg(){return j.lFrame.currentTNode}function FC(){let e=j.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function ki(e,t){let n=j.lFrame;n.currentTNode=e,n.isParent=t}function mg(){return j.lFrame.isParent}function LC(){j.lFrame.isParent=!1}function yg(){return hg}function Xh(e){hg=e}function yu(){let e=j.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function VC(){return j.lFrame.bindingIndex}function jC(e){return j.lFrame.bindingIndex=e}function vu(){return j.lFrame.bindingIndex++}function vg(e){let t=j.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function BC(){return j.lFrame.inI18n}function UC(e,t){let n=j.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ic(t)}function $C(){return j.lFrame.currentDirectiveIndex}function Ic(e){j.lFrame.currentDirectiveIndex=e}function HC(e){let t=j.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Cg(){return j.lFrame.currentQueryIndex}function Cu(e){j.lFrame.currentQueryIndex=e}function zC(e){let t=e[F];return t.type===2?t.declTNode:t.type===1?e[vt]:null}function Dg(e,t,n){if(n&V.SkipSelf){let i=t,o=e;for(;i=i.parent,i===null&&!(n&V.Host);)if(i=zC(o),i===null||(o=o[Gr],i.type&10))break;if(i===null)return!1;t=i,e=o}let r=j.lFrame=wg();return r.currentTNode=t,r.lView=e,!0}function Du(e){let t=wg(),n=e[F];j.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function wg(){let e=j.lFrame,t=e===null?null:e.child;return t===null?_g(e):t}function _g(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function bg(){let e=j.lFrame;return j.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Eg=bg;function wu(){let e=bg();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function GC(e){return(j.lFrame.contextLView=xC(e,j.lFrame.contextLView))[Tt]}function ir(){return j.lFrame.selectedIndex}function Yn(e){j.lFrame.selectedIndex=e}function Mg(){let e=j.lFrame;return cg(e.tView,e.selectedIndex)}function Ct(){j.lFrame.currentNamespace=ag}function ea(){qC()}function qC(){j.lFrame.currentNamespace=null}function WC(){return j.lFrame.currentNamespace}var Ig=!0;function _u(){return Ig}function bu(e){Ig=e}function ZC(e,t,n){let{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=t.type.prototype;if(r){let s=ig(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}i&&(n.preOrderHooks??=[]).push(0-e,i),o&&((n.preOrderHooks??=[]).push(e,o),(n.preOrderCheckHooks??=[]).push(e,o))}function Eu(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let o=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=o;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),l&&(e.viewHooks??=[]).push(-n,l),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function ps(e,t,n){Sg(e,t,3,n)}function gs(e,t,n,r){(e[O]&3)===n&&Sg(e,t,n,r)}function oc(e,t){let n=e[O];(n&3)===t&&(n&=16383,n+=1,e[O]=n)}function Sg(e,t,n,r){let i=r!==void 0?e[Nr]&65535:0,o=r??-1,s=t.length-1,a=0;for(let l=i;l<s;l++)if(typeof t[l+1]=="number"){if(a=t[l],r!=null&&a>=r)break}else t[l]<0&&(e[Nr]+=65536),(a<o||o==-1)&&(QC(e,n,t,l),e[Nr]=(e[Nr]&**********)+l+2),l++}function Jh(e,t){It(4,e,t);let n=Y(null);try{t.call(e)}finally{Y(n),It(5,e,t)}}function QC(e,t,n,r){let i=n[r]<0,o=n[r+1],s=i?-n[r]:n[r],a=e[s];i?e[O]>>14<e[Nr]>>16&&(e[O]&3)===t&&(e[O]+=16384,Jh(a,o)):Jh(a,o)}var kr=-1,Kn=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function YC(e){return e instanceof Kn}function KC(e){return(e.flags&8)!==0}function XC(e){return(e.flags&16)!==0}var sc={},Sc=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Ws(r);let i=this.injector.get(t,sc,r);return i!==sc||n===sc?i:this.parentInjector.get(t,n,r)}};function xg(e){return e!==kr}function Ss(e){return e&32767}function JC(e){return e>>16}function xs(e,t){let n=JC(e),r=t;for(;n>0;)r=r[Gr],n--;return r}var xc=!0;function Ts(e){let t=xc;return xc=e,t}var eD=256,Tg=eD-1,Ag=5,tD=0,St={};function nD(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(bi)&&(r=n[bi]),r==null&&(r=n[bi]=tD++);let i=r&Tg,o=1<<i;t.data[e+(i>>Ag)]|=o}function As(e,t){let n=Ng(e,t);if(n!==-1)return n;let r=t[F];r.firstCreatePass&&(e.injectorIndex=t.length,ac(r.data,e),ac(t,null),ac(r.blueprint,null));let i=Mu(e,t),o=e.injectorIndex;if(xg(i)){let s=Ss(i),a=xs(i,t),l=a[F].data;for(let c=0;c<8;c++)t[o+c]=a[s+c]|l[s+c]}return t[o+8]=i,o}function ac(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Ng(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Mu(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,i=t;for(;i!==null;){if(r=Fg(i),r===null)return kr;if(n++,i=i[Gr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return kr}function Tc(e,t,n){nD(e,t,n)}function rD(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,i=0;for(;i<r;){let o=n[i];if(Bp(o))break;if(o===0)i=i+2;else if(typeof o=="number")for(i++;i<r&&typeof n[i]=="string";)i++;else{if(o===t)return n[i+1];i=i+2}}}return null}function Og(e,t,n){if(n&V.Optional||e!==void 0)return e;cu(t,"NodeInjector")}function Pg(e,t,n,r){if(n&V.Optional&&r===void 0&&(r=null),!(n&(V.Self|V.Host))){let i=e[jr],o=Ue(void 0);try{return i?i.get(t,r,n&V.Optional):kp(t,r,n&V.Optional)}finally{Ue(o)}}return Og(r,t,n)}function kg(e,t,n,r=V.Default,i){if(e!==null){if(t[O]&2048&&!(r&V.Self)){let s=aD(e,t,n,r,St);if(s!==St)return s}let o=Rg(e,t,n,r,St);if(o!==St)return o}return Pg(t,n,r,i)}function Rg(e,t,n,r,i){let o=oD(n);if(typeof o=="function"){if(!Dg(t,e,r))return r&V.Host?Og(i,n,r):Pg(t,n,r,i);try{let s;if(s=o(r),s==null&&!(r&V.Optional))cu(n);else return s}finally{Eg()}}else if(typeof o=="number"){let s=null,a=Ng(e,t),l=kr,c=r&V.Host?t[Nt][vt]:null;for((a===-1||r&V.SkipSelf)&&(l=a===-1?Mu(e,t):t[a+8],l===kr||!tp(r,!1)?a=-1:(s=t[F],a=Ss(l),t=xs(l,t)));a!==-1;){let u=t[F];if(ep(o,a,u.data)){let d=iD(a,t,n,s,r,c);if(d!==St)return d}l=t[a+8],l!==kr&&tp(r,t[F].data[a+8]===c)&&ep(o,a,t)?(s=u,a=Ss(l),t=xs(l,t)):a=-1}}return i}function iD(e,t,n,r,i,o){let s=t[F],a=s.data[e+8],l=r==null?Xs(a)&&xc:r!=s&&(a.type&3)!==0,c=i&V.Host&&o===a,u=ms(a,s,n,l,c);return u!==null?Xn(t,s,u,a):St}function ms(e,t,n,r,i){let o=e.providerIndexes,s=t.data,a=o&1048575,l=e.directiveStart,c=e.directiveEnd,u=o>>20,d=r?a:a+u,g=i?a+u:c;for(let f=d;f<g;f++){let m=s[f];if(f<l&&n===m||f>=l&&m.type===n)return f}if(i){let f=s[l];if(f&&wn(f)&&f.type===n)return l}return null}function Xn(e,t,n,r){let i=e[n],o=t.data;if(YC(i)){let s=i;s.resolving&&Nv(Av(o[n]));let a=Ts(s.canSeeViewProviders);s.resolving=!0;let l,c=s.injectImpl?Ue(s.injectImpl):null,u=Dg(e,r,V.Default);try{i=e[n]=s.factory(void 0,o,e,r),t.firstCreatePass&&n>=r.directiveStart&&ZC(n,o[n],t)}finally{c!==null&&Ue(c),Ts(a),s.resolving=!1,Eg()}}return i}function oD(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(bi)?e[bi]:void 0;return typeof t=="number"?t>=0?t&Tg:sD:t}function ep(e,t,n){let r=1<<e;return!!(n[t+(e>>Ag)]&r)}function tp(e,t){return!(e&V.Self)&&!(e&V.Host&&t)}var Gn=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return kg(this._tNode,this._lView,t,Ws(r),n)}};function sD(){return new Gn(ze(),ee())}function ta(e){return Oi(()=>{let t=e.prototype.constructor,n=t[vs]||Ac(t),r=Object.prototype,i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){let o=i[vs]||Ac(i);if(o&&o!==n)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function Ac(e){return xp(e)?()=>{let t=Ac(ke(e));return t&&t()}:qn(e)}function aD(e,t,n,r,i){let o=e,s=t;for(;o!==null&&s!==null&&s[O]&2048&&!(s[O]&512);){let a=Rg(o,s,n,r|V.Self,St);if(a!==St)return a;let l=o.parent;if(!l){let c=s[eg];if(c){let u=c.get(n,St,r);if(u!==St)return u}l=Fg(s),s=s[Gr]}o=l}return i}function Fg(e){let t=e[F],n=t.type;return n===2?t.declTNode:n===1?e[vt]:null}function Iu(e){return rD(ze(),e)}function np(e,t=null,n=null,r){let i=Lg(e,t,n,r);return i.resolveInjectorInitializers(),i}function Lg(e,t=null,n=null,r,i=new Set){let o=[n||it,aC(e)];return r=r||(typeof e=="object"?void 0:Re(e)),new Si(o,t||pu(),r||null,i)}var Ye=class e{static{this.THROW_IF_NOT_FOUND=Mi}static{this.NULL=new ws}static create(t,n){if(Array.isArray(t))return np({name:""},n,t,"");{let r=t.name??"";return np({name:r},t.parent,t.providers,r)}}static{this.\u0275prov=b({token:e,providedIn:"any",factory:()=>E(Lp)})}static{this.__NG_ELEMENT_ID__=-1}};var lD=new M("");lD.__NG_ELEMENT_ID__=e=>{let t=ze();if(t===null)throw new C(204,!1);if(t.type&2)return t.value;if(e&V.Optional)return null;throw new C(204,!1)};var cD="ngOriginalError";function lc(e){return e[cD]}var Su=(()=>{class e{static{this.__NG_ELEMENT_ID__=uD}static{this.__NG_ENV_ID__=n=>n}}return e})(),Nc=class extends Su{constructor(t){super(),this._lView=t}onDestroy(t){return fg(this._lView,t),()=>TC(this._lView,t)}};function uD(){return new Nc(ee())}var Zt=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new De(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}}return e})();var Oc=class extends Ie{constructor(t=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=t,Jp()&&(this.destroyRef=D(Su,{optional:!0})??void 0,this.pendingTasks=D(Zt,{optional:!0})??void 0)}emit(t){let n=Y(null);try{super.next(t)}finally{Y(n)}}subscribe(t,n,r){let i=t,o=n||(()=>null),s=r;if(t&&typeof t=="object"){let l=t;i=l.next?.bind(l),o=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(o=this.wrapInTimeout(o),i&&(i=this.wrapInTimeout(i)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:i,error:o,complete:s});return t instanceof ce&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},ye=Oc;function Ns(...e){}function Vg(e){let t,n;function r(){e=Ns;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function rp(e){return queueMicrotask(()=>e()),()=>{e=Ns}}var J=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new ye(!1),this.onMicrotaskEmpty=new ye(!1),this.onStable=new ye(!1),this.onError=new ye(!1),typeof Zone>"u")throw new C(908,!1);Zone.assertZonePatched();let i=this;i._nesting=0,i._outer=i._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(i._inner=i._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(i._inner=i._inner.fork(Zone.longStackTraceZoneSpec)),i.shouldCoalesceEventChangeDetection=!r&&n,i.shouldCoalesceRunChangeDetection=r,i.callbackScheduled=!1,hD(i)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new C(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new C(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,i){let o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,t,dD,Ns,Ns);try{return o.runTask(s,n,r)}finally{o.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},dD={};function xu(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function fD(e){e.isCheckStableRunning||e.callbackScheduled||(e.callbackScheduled=!0,Zone.root.run(()=>{Vg(()=>{e.callbackScheduled=!1,Pc(e),e.isCheckStableRunning=!0,xu(e),e.isCheckStableRunning=!1})}),Pc(e))}function hD(e){let t=()=>{fD(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,i,o,s,a)=>{if(pD(a))return n.invokeTask(i,o,s,a);try{return ip(e),n.invokeTask(i,o,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&o.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),op(e)}},onInvoke:(n,r,i,o,s,a,l)=>{try{return ip(e),n.invoke(i,o,s,a,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!gD(a)&&t(),op(e)}},onHasTask:(n,r,i,o)=>{n.hasTask(i,o),r===i&&(o.change=="microTask"?(e._hasPendingMicrotasks=o.microTask,Pc(e),xu(e)):o.change=="macroTask"&&(e.hasPendingMacrotasks=o.macroTask))},onHandleError:(n,r,i,o)=>(n.handleError(i,o),e.runOutsideAngular(()=>e.onError.emit(o)),!1)})}function Pc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function ip(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function op(e){e._nesting--,xu(e)}var Os=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new ye,this.onMicrotaskEmpty=new ye,this.onStable=new ye,this.onError=new ye}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,i){return t.apply(n,r)}};function pD(e){return jg(e,"__ignore_ng_zone__")}function gD(e){return jg(e,"__scheduler_tick__")}function jg(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function mD(e="zone.js",t){return e==="noop"?new Os:e==="zone.js"?new J(t):e}var zt=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&lc(t);for(;n&&lc(n);)n=lc(n);return n||null}},yD=new M("",{providedIn:"root",factory:()=>{let e=D(J),t=D(zt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function vD(){return Wr(ze(),ee())}function Wr(e,t){return new at(st(e,t))}var at=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=vD}}return e})();function CD(e){return e instanceof at?e.nativeElement:e}function DD(){return this._results[Symbol.iterator]()}var kc=class e{get changes(){return this._changes??=new ye}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=e.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=DD)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=$v(t);(this._changesDetected=!Uv(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function Bg(e){return(e.flags&128)===128}var Ug=new Map,wD=0;function _D(){return wD++}function bD(e){Ug.set(e[Ks],e)}function ED(e){Ug.delete(e[Ks])}var sp="__ngContext__";function Jn(e,t){vn(t)?(e[sp]=t[Ks],bD(t)):e[sp]=t}function $g(e){return zg(e[xi])}function Hg(e){return zg(e[mt])}function zg(e){for(;e!==null&&!Wt(e);)e=e[mt];return e}var Rc;function Gg(e){Rc=e}function MD(){if(Rc!==void 0)return Rc;if(typeof document<"u")return document;throw new C(210,!1)}var na=new M("",{providedIn:"root",factory:()=>ID}),ID="ng",Tu=new M(""),kt=new M("",{providedIn:"platform",factory:()=>"unknown"});var Au=new M(""),Nu=new M("",{providedIn:"root",factory:()=>MD().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var SD="h",xD="b";var TD=()=>null;function Ou(e,t,n=!1){return TD(e,t,n)}var qg=!1,AD=new M("",{providedIn:"root",factory:()=>qg});var ls;function ND(){if(ls===void 0&&(ls=null,Te.trustedTypes))try{ls=Te.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ls}function OD(e){return ND()?.createScriptURL(e)||e}var cs;function PD(){if(cs===void 0&&(cs=null,Te.trustedTypes))try{cs=Te.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return cs}function ap(e){return PD()?.createScriptURL(e)||e}var Ps=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Ip})`}};function Ri(e){return e instanceof Ps?e.changingThisBreaksApplicationSecurity:e}function Pu(e,t){let n=kD(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Ip})`)}return n===t}function kD(e){return e instanceof Ps&&e.getTypeName()||null}var RD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Wg(e){return e=String(e),e.match(RD)?e:"unsafe:"+e}var ra=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ra||{});function or(e){let t=Yg();return t?t.sanitize(ra.URL,e)||"":Pu(e,"URL")?Ri(e):Wg(Rr(e))}function FD(e){let t=Yg();if(t)return ap(t.sanitize(ra.RESOURCE_URL,e)||"");if(Pu(e,"ResourceURL"))return ap(Ri(e));throw new C(904,!1)}function Zg(e){return OD(e[0])}function LD(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?FD:or}function Qg(e,t,n){return LD(t,n)(e)}function Yg(){let e=ee();return e&&e[At].sanitizer}function Kg(e){return e instanceof Function?e():e}function VD(e){return(e??D(Ye)).get(kt)==="browser"}var Gt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Gt||{}),jD;function ku(e,t){return jD(e,t)}function Or(e,t,n,r,i){if(r!=null){let o,s=!1;Wt(r)?o=r:vn(r)&&(s=!0,r=r[qt]);let a=Pt(r);e===0&&n!==null?i==null?nm(t,n,a):ks(t,n,a,i||null,!0):e===1&&n!==null?ks(t,n,a,i||null,!0):e===2?e2(t,a,s):e===3&&t.destroyNode(a),o!=null&&n2(t,e,o,n,i)}}function BD(e,t){return e.createText(t)}function UD(e,t,n){e.setValue(t,n)}function Xg(e,t,n){return e.createElement(t,n)}function $D(e,t){Jg(e,t),t[qt]=null,t[vt]=null}function HD(e,t,n,r,i,o){r[qt]=i,r[vt]=t,ia(e,r,n,1,i,o)}function Jg(e,t){t[At].changeDetectionScheduler?.notify(8),ia(e,t,t[Ae],2,null,null)}function zD(e){let t=e[xi];if(!t)return cc(e[F],e);for(;t;){let n=null;if(vn(t))n=t[xi];else{let r=t[Qe];r&&(n=r)}if(!n){for(;t&&!t[mt]&&t!==e;)vn(t)&&cc(t[F],t),t=t[Pe];t===null&&(t=e),vn(t)&&cc(t[F],t),n=t&&t[mt]}t=n}}function GD(e,t,n,r){let i=Qe+r,o=n.length;r>0&&(n[i-1][mt]=t),r<o-Qe?(t[mt]=n[i],Fp(n,Qe+r,t)):(n.push(t),t[mt]=null),t[Pe]=n;let s=t[Wn];s!==null&&n!==s&&em(s,t);let a=t[Ht];a!==null&&a.insertView(e),Mc(t),t[O]|=128}function em(e,t){let n=e[Br],r=t[Pe];if(vn(r))e[O]|=Ms.HasTransplantedViews;else{let i=r[Pe][Nt];t[Nt]!==i&&(e[O]|=Ms.HasTransplantedViews)}n===null?e[Br]=[t]:n.push(t)}function Ru(e,t){let n=e[Br],r=n.indexOf(t);n.splice(r,1)}function Fc(e,t){if(e.length<=Qe)return;let n=Qe+t,r=e[n];if(r){let i=r[Wn];i!==null&&i!==e&&Ru(i,r),t>0&&(e[n-1][mt]=r[mt]);let o=Ds(e,Qe+t);$D(r[F],r);let s=o[Ht];s!==null&&s.detachView(o[F]),r[Pe]=null,r[mt]=null,r[O]&=-129}return r}function tm(e,t){if(!(t[O]&256)){let n=t[Ae];n.destroyNode&&ia(e,t,n,3,null,null),zD(t)}}function cc(e,t){if(t[O]&256)return;let n=Y(null);try{t[O]&=-129,t[O]|=256,t[ot]&&Vl(t[ot]),WD(e,t),qD(e,t),t[F].type===1&&t[Ae].destroy();let r=t[Wn];if(r!==null&&Wt(t[Pe])){r!==t[Pe]&&Ru(r,t);let i=t[Ht];i!==null&&i.detachView(e)}ED(t)}finally{Y(n)}}function qD(e,t){let n=e.cleanup,r=t[bs];if(n!==null)for(let o=0;o<n.length-1;o+=2)if(typeof n[o]=="string"){let s=n[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else{let s=r[n[o+1]];n[o].call(s)}r!==null&&(t[bs]=null);let i=t[yn];if(i!==null){t[yn]=null;for(let o=0;o<i.length;o++){let s=i[o];s()}}}function WD(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let i=t[n[r]];if(!(i instanceof Kn)){let o=n[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){let a=i[o[s]],l=o[s+1];It(4,a,l);try{l.call(a)}finally{It(5,a,l)}}else{It(4,i,o);try{o.call(i)}finally{It(5,i,o)}}}}}function ZD(e,t,n){return QD(e,t.parent,n)}function QD(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[qt];{let{componentOffset:i}=r;if(i>-1){let{encapsulation:o}=e.data[r.directiveStart+i];if(o===xt.None||o===xt.Emulated)return null}return st(r,n)}}function ks(e,t,n,r,i){e.insertBefore(t,n,r,i)}function nm(e,t,n){e.appendChild(t,n)}function lp(e,t,n,r,i){r!==null?ks(e,t,n,r,i):nm(e,t,n)}function rm(e,t){return e.parentNode(t)}function YD(e,t){return e.nextSibling(t)}function KD(e,t,n){return JD(e,t,n)}function XD(e,t,n){return e.type&40?st(e,n):null}var JD=XD,cp;function Fu(e,t,n,r){let i=ZD(e,r,t),o=t[Ae],s=r.parent||t[vt],a=KD(s,r,t);if(i!=null)if(Array.isArray(n))for(let l=0;l<n.length;l++)lp(o,i,n[l],a,!1);else lp(o,i,n,a,!1);cp!==void 0&&cp(o,r,t,n,i)}function _i(e,t){if(t!==null){let n=t.type;if(n&3)return st(t,e);if(n&4)return Lc(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return _i(e,r);{let i=e[t.index];return Wt(i)?Lc(-1,i):Pt(i)}}else{if(n&128)return _i(e,t.next);if(n&32)return ku(t,e)()||Pt(e[t.index]);{let r=im(e,t);if(r!==null){if(Array.isArray(r))return r[0];let i=Qn(e[Nt]);return _i(i,r)}else return _i(e,t.next)}}}return null}function im(e,t){if(t!==null){let r=e[Nt][vt],i=t.projection;return r.projection[i]}return null}function Lc(e,t){let n=Qe+e+1;if(n<t.length){let r=t[n],i=r[F].firstChild;if(i!==null)return _i(r,i)}return t[Zn]}function e2(e,t,n){e.removeChild(null,t,n)}function Lu(e,t,n,r,i,o,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],l=n.type;if(s&&t===0&&(a&&Jn(Pt(a),r),n.flags|=2),(n.flags&32)!==32)if(l&8)Lu(e,t,n.child,r,i,o,!1),Or(t,e,i,a,o);else if(l&32){let c=ku(n,r),u;for(;u=c();)Or(t,e,i,u,o);Or(t,e,i,a,o)}else l&16?t2(e,t,r,n,i,o):Or(t,e,i,a,o);n=s?n.projectionNext:n.next}}function ia(e,t,n,r,i,o){Lu(n,r,e.firstChild,t,i,o,!1)}function t2(e,t,n,r,i,o){let s=n[Nt],l=s[vt].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++){let u=l[c];Or(t,e,i,u,o)}else{let c=l,u=s[Pe];Bg(r)&&(c.flags|=128),Lu(e,t,c,u,i,o,!0)}}function n2(e,t,n,r,i){let o=n[Zn],s=Pt(n);o!==s&&Or(t,e,r,o,i);for(let a=Qe;a<n.length;a++){let l=n[a];ia(l[F],l,e,t,r,o)}}function r2(e,t,n,r,i){if(t)i?e.addClass(n,r):e.removeClass(n,r);else{let o=r.indexOf("-")===-1?void 0:Gt.DashCase;i==null?e.removeStyle(n,r,o):(typeof i=="string"&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=Gt.Important),e.setStyle(n,r,i,o))}}function i2(e,t,n){e.setAttribute(t,"style",n)}function om(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function sm(e,t,n){let{mergedAttrs:r,classes:i,styles:o}=n;r!==null&&Cc(e,t,r),i!==null&&om(e,t,i),o!==null&&i2(e,t,o)}var Qt={};function $(e=1){am(He(),ee(),ir()+e,!1)}function am(e,t,n,r){if(!r)if((t[O]&3)===3){let o=e.preOrderCheckHooks;o!==null&&ps(t,o,n)}else{let o=e.preOrderHooks;o!==null&&gs(t,o,0,n)}Yn(n)}function B(e,t=V.Default){let n=ee();if(n===null)return E(e,t);let r=ze();return kg(r,n,ke(e),t)}function lm(){let e="invalid";throw new Error(e)}function cm(e,t,n,r,i,o){let s=Y(null);try{let a=null;i&Cn.SignalBased&&(a=t[r][dn]),a!==null&&a.transformFn!==void 0&&(o=a.transformFn(o)),i&Cn.HasDecoratorInputTransform&&(o=e.inputTransforms[r].call(t,o)),e.setInput!==null?e.setInput(t,a,o,n,r):rg(t,a,r,o)}finally{Y(s)}}function o2(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let i=n[r];if(i<0)Yn(~i);else{let o=i,s=n[++r],a=n[++r];UC(s,o);let l=t[o];a(2,l)}}}finally{Yn(-1)}}function oa(e,t,n,r,i,o,s,a,l,c,u){let d=t.blueprint.slice();return d[qt]=i,d[O]=r|4|128|8|64,(c!==null||e&&e[O]&2048)&&(d[O]|=2048),ug(d),d[Pe]=d[Gr]=e,d[Tt]=n,d[At]=s||e&&e[At],d[Ae]=a||e&&e[Ae],d[jr]=l||e&&e[jr]||null,d[vt]=o,d[Ks]=_D(),d[_s]=u,d[eg]=c,d[Nt]=t.type==2?e[Nt]:d,d}function sa(e,t,n,r,i){let o=e.data[t];if(o===null)o=s2(e,t,n,r,i),BC()&&(o.flags|=32);else if(o.type&64){o.type=n,o.value=r,o.attrs=i;let s=FC();o.injectorIndex=s===null?-1:s.injectorIndex}return ki(o,!0),o}function s2(e,t,n,r,i){let o=gg(),s=mg(),a=s?o:o&&o.parent,l=e.data[t]=f2(e,a,n,t,r,i);return e.firstChild===null&&(e.firstChild=l),o!==null&&(s?o.child==null&&l.parent!==null&&(o.child=l):o.next===null&&(o.next=l,l.prev=o)),l}function um(e,t,n,r){if(n===0)return-1;let i=t.length;for(let o=0;o<n;o++)t.push(r),e.blueprint.push(r),e.data.push(null);return i}function dm(e,t,n,r,i){let o=ir(),s=r&2;try{Yn(-1),s&&t.length>Ot&&am(e,t,Ot,!1),It(s?2:0,i),n(r,i)}finally{Yn(o),It(s?3:1,i)}}function fm(e,t,n){if(ng(t)){let r=Y(null);try{let i=t.directiveStart,o=t.directiveEnd;for(let s=i;s<o;s++){let a=e.data[s];if(a.contentQueries){let l=n[s];a.contentQueries(1,l,s)}}}finally{Y(r)}}}function hm(e,t,n){pg()&&(C2(e,t,n,st(n,t)),(n.flags&64)===64&&vm(e,t,n))}function pm(e,t,n=st){let r=t.localNames;if(r!==null){let i=t.index+1;for(let o=0;o<r.length;o+=2){let s=r[o+1],a=s===-1?n(t,e):e[s];e[i++]=a}}}function gm(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Vu(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Vu(e,t,n,r,i,o,s,a,l,c,u){let d=Ot+r,g=d+i,f=a2(d,g),m=typeof c=="function"?c():c;return f[F]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:g,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof o=="function"?o():o,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:l,consts:m,incompleteFirstPass:!1,ssrId:u}}function a2(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Qt);return n}function l2(e,t,n,r){let o=r.get(AD,qg)||n===xt.ShadowDom,s=e.selectRootElement(t,o);return c2(s),s}function c2(e){u2(e)}var u2=()=>null;function d2(e,t,n,r){let i=wm(t);i.push(n),e.firstCreatePass&&_m(e).push(r,i.length-1)}function f2(e,t,n,r,i,o){let s=t?t.injectorIndex:-1,a=0;return PC()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function up(e,t,n,r,i){for(let o in t){if(!t.hasOwnProperty(o))continue;let s=t[o];if(s===void 0)continue;r??={};let a,l=Cn.None;Array.isArray(s)?(a=s[0],l=s[1]):a=s;let c=o;if(i!==null){if(!i.hasOwnProperty(o))continue;c=i[o]}e===0?dp(r,n,c,a,l):dp(r,n,c,a)}return r}function dp(e,t,n,r,i){let o;e.hasOwnProperty(n)?(o=e[n]).push(t,r):o=e[n]=[t,r],i!==void 0&&o.push(i)}function h2(e,t,n){let r=t.directiveStart,i=t.directiveEnd,o=e.data,s=t.attrs,a=[],l=null,c=null;for(let u=r;u<i;u++){let d=o[u],g=n?n.get(d):null,f=g?g.inputs:null,m=g?g.outputs:null;l=up(0,d.inputs,u,l,f),c=up(1,d.outputs,u,c,m);let v=l!==null&&s!==null&&!fu(t)?A2(l,u,s):null;a.push(v)}l!==null&&(l.hasOwnProperty("class")&&(t.flags|=8),l.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=l,t.outputs=c}function p2(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function g2(e,t,n,r,i,o,s,a){let l=st(t,n),c=t.inputs,u;!a&&c!=null&&(u=c[r])?(ju(e,n,u,r,i),Xs(t)&&m2(n,t.index)):t.type&3?(r=p2(r),i=s!=null?s(i,t.value||"",r):i,o.setProperty(l,r,i)):t.type&12}function m2(e,t){let n=En(t,e);n[O]&16||(n[O]|=64)}function mm(e,t,n,r){if(pg()){let i=r===null?null:{"":-1},o=w2(e,n),s,a;o===null?s=a=null:[s,a]=o,s!==null&&ym(e,t,n,s,i,a),i&&_2(n,r,i)}n.mergedAttrs=Ii(n.mergedAttrs,n.attrs)}function ym(e,t,n,r,i,o){for(let c=0;c<r.length;c++)Tc(As(n,t),e,r[c].type);E2(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let u=r[c];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,l=um(e,t,r.length,null);for(let c=0;c<r.length;c++){let u=r[c];n.mergedAttrs=Ii(n.mergedAttrs,u.hostAttrs),M2(e,n,t,l,u),b2(l,u,i),u.contentQueries!==null&&(n.flags|=4),(u.hostBindings!==null||u.hostAttrs!==null||u.hostVars!==0)&&(n.flags|=64);let d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}h2(e,n,o)}function y2(e,t,n,r,i){let o=i.hostBindings;if(o){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;v2(s)!=a&&s.push(a),s.push(n,r,o)}}function v2(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function C2(e,t,n,r){let i=n.directiveStart,o=n.directiveEnd;Xs(n)&&I2(t,n,e.data[i+n.componentOffset]),e.firstCreatePass||As(n,t),Jn(r,t);let s=n.initialInputs;for(let a=i;a<o;a++){let l=e.data[a],c=Xn(t,e,a,n);if(Jn(c,t),s!==null&&T2(t,a-i,c,l,n,s),wn(l)){let u=En(n.index,t);u[Tt]=Xn(t,e,a,n)}}}function vm(e,t,n){let r=n.directiveStart,i=n.directiveEnd,o=n.index,s=$C();try{Yn(o);for(let a=r;a<i;a++){let l=e.data[a],c=t[a];Ic(a),(l.hostBindings!==null||l.hostVars!==0||l.hostAttrs!==null)&&D2(l,c)}}finally{Yn(-1),Ic(s)}}function D2(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function w2(e,t){let n=e.directiveRegistry,r=null,i=null;if(n)for(let o=0;o<n.length;o++){let s=n[o];if(Xv(t,s.selectors,!1))if(r||(r=[]),wn(s))if(s.findHostDirectiveDefs!==null){let a=[];i=i||new Map,s.findHostDirectiveDefs(s,a,i),r.unshift(...a,s);let l=a.length;Vc(e,t,l)}else r.unshift(s),Vc(e,t,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return r===null?null:[r,i]}function Vc(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function _2(e,t,n){if(t){let r=e.localNames=[];for(let i=0;i<t.length;i+=2){let o=n[t[i+1]];if(o==null)throw new C(-301,!1);r.push(t[i],o)}}}function b2(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;wn(t)&&(n[""]=e)}}function E2(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function M2(e,t,n,r,i){e.data[r]=i;let o=i.factory||(i.factory=qn(i.type,!0)),s=new Kn(o,wn(i),B);e.blueprint[r]=s,n[r]=s,y2(e,t,r,um(e,n,i.hostVars,Qt),i)}function I2(e,t,n){let r=st(t,e),i=gm(n),o=e[At].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=aa(e,oa(e,i,null,s,r,t,null,o.createRenderer(r,n),null,null,null));e[t.index]=a}function S2(e,t,n,r,i,o){let s=st(e,t);x2(t[Ae],s,o,e.value,n,r,i)}function x2(e,t,n,r,i,o,s){if(o==null)e.removeAttribute(t,i,n);else{let a=s==null?Rr(o):s(o,r||"",i);e.setAttribute(t,i,a,n)}}function T2(e,t,n,r,i,o){let s=o[t];if(s!==null)for(let a=0;a<s.length;){let l=s[a++],c=s[a++],u=s[a++],d=s[a++];cm(r,n,l,c,u,d)}}function A2(e,t,n){let r=null,i=0;for(;i<n.length;){let o=n[i];if(o===0){i+=4;continue}else if(o===5){i+=2;continue}if(typeof o=="number")break;if(e.hasOwnProperty(o)){r===null&&(r=[]);let s=e[o];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(o,s[a+1],s[a+2],n[i+1]);break}}i+=2}return r}function Cm(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Dm(e,t){let n=e.contentQueries;if(n!==null){let r=Y(null);try{for(let i=0;i<n.length;i+=2){let o=n[i],s=n[i+1];if(s!==-1){let a=e.data[s];Cu(o),a.contentQueries(2,t[s],s)}}}finally{Y(r)}}}function aa(e,t){return e[xi]?e[Yh][mt]=t:e[xi]=t,e[Yh]=t,t}function jc(e,t,n){Cu(0);let r=Y(null);try{t(e,n)}finally{Y(r)}}function wm(e){return e[bs]??=[]}function _m(e){return e.cleanup??=[]}function bm(e,t){let n=e[jr],r=n?n.get(zt,null):null;r&&r.handleError(t)}function ju(e,t,n,r,i){for(let o=0;o<n.length;){let s=n[o++],a=n[o++],l=n[o++],c=t[s],u=e.data[s];cm(u,c,r,a,l,i)}}function Em(e,t,n){let r=lg(t,e);UD(e[Ae],r,n)}function N2(e,t){let n=En(t,e),r=n[F];O2(r,n);let i=n[qt];i!==null&&n[_s]===null&&(n[_s]=Ou(i,n[jr])),Bu(r,n,n[Tt])}function O2(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Bu(e,t,n){Du(t);try{let r=e.viewQuery;r!==null&&jc(1,r,n);let i=e.template;i!==null&&dm(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Ht]?.finishViewCreation(e),e.staticContentQueries&&Dm(e,t),e.staticViewQueries&&jc(2,e.viewQuery,n);let o=e.components;o!==null&&P2(t,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[O]&=-5,wu()}}function P2(e,t){for(let n=0;n<t.length;n++)N2(e,t[n])}function k2(e,t,n,r){let i=Y(null);try{let o=t.tView,a=e[O]&4096?4096:16,l=oa(e,o,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];l[Wn]=c;let u=e[Ht];return u!==null&&(l[Ht]=u.createEmbeddedView(o)),Bu(o,l,n),l}finally{Y(i)}}function fp(e,t){return!t||t.firstChild===null||Bg(e)}function R2(e,t,n,r=!0){let i=t[F];if(GD(i,t,e,n),r){let s=Lc(n,e),a=t[Ae],l=rm(a,e[Zn]);l!==null&&HD(i,e[vt],a,t,l,s)}let o=t[_s];o!==null&&o.firstChild!==null&&(o.firstChild=null)}function Rs(e,t,n,r,i=!1){for(;n!==null;){if(n.type===128){n=i?n.projectionNext:n.next;continue}let o=t[n.index];o!==null&&r.push(Pt(o)),Wt(o)&&F2(o,r);let s=n.type;if(s&8)Rs(e,t,n.child,r);else if(s&32){let a=ku(n,t),l;for(;l=a();)r.push(l)}else if(s&16){let a=im(t,n);if(Array.isArray(a))r.push(...a);else{let l=Qn(t[Nt]);Rs(l[F],l,a,r,!0)}}n=i?n.projectionNext:n.next}return r}function F2(e,t){for(let n=Qe;n<e.length;n++){let r=e[n],i=r[F].firstChild;i!==null&&Rs(r[F],r,i,t)}e[Zn]!==e[qt]&&t.push(e[Zn])}var Mm=[];function L2(e){return e[ot]??V2(e)}function V2(e){let t=Mm.pop()??Object.create(B2);return t.lView=e,t}function j2(e){e.lView[ot]!==e&&(e.lView=null,Mm.push(e))}var B2=Q(_({},vi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Js(e.lView)},consumerOnSignalRead(){this.lView[ot]=this}});function U2(e){let t=e[ot]??Object.create($2);return t.lView=e,t}var $2=Q(_({},vi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let t=Qn(e.lView);for(;t&&!Im(t[F]);)t=Qn(t);t&&dg(t)},consumerOnSignalRead(){this.lView[ot]=this}});function Im(e){return e.type!==2}var H2=100;function Sm(e,t=!0,n=0){let r=e[At],i=r.rendererFactory,o=!1;o||i.begin?.();try{z2(e,n)}catch(s){throw t&&bm(e,s),s}finally{o||(i.end?.(),r.inlineEffectRunner?.flush())}}function z2(e,t){let n=yg();try{Xh(!0),Bc(e,t);let r=0;for(;Ti(e);){if(r===H2)throw new C(103,!1);r++,Bc(e,1)}}finally{Xh(n)}}function G2(e,t,n,r){let i=t[O];if((i&256)===256)return;let o=!1,s=!1;!o&&t[At].inlineEffectRunner?.flush(),Du(t);let a=!0,l=null,c=null;o||(Im(e)?(c=L2(t),l=Ro(c)):nh()===null?(a=!1,c=U2(t),l=Ro(c)):t[ot]&&(Vl(t[ot]),t[ot]=null));try{ug(t),jC(e.bindingStartIndex),n!==null&&dm(e,t,n,2,r);let u=(i&3)===3;if(!o)if(u){let f=e.preOrderCheckHooks;f!==null&&ps(t,f,null)}else{let f=e.preOrderHooks;f!==null&&gs(t,f,0,null),oc(t,0)}if(s||q2(t),xm(t,0),e.contentQueries!==null&&Dm(e,t),!o)if(u){let f=e.contentCheckHooks;f!==null&&ps(t,f)}else{let f=e.contentHooks;f!==null&&gs(t,f,1),oc(t,1)}o2(e,t);let d=e.components;d!==null&&Am(t,d,0);let g=e.viewQuery;if(g!==null&&jc(2,g,r),!o)if(u){let f=e.viewCheckHooks;f!==null&&ps(t,f)}else{let f=e.viewHooks;f!==null&&gs(t,f,2),oc(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[ic]){for(let f of t[ic])f();t[ic]=null}o||(t[O]&=-73)}catch(u){throw o||Js(t),u}finally{c!==null&&(Fl(c,l),a&&j2(c)),wu()}}function xm(e,t){for(let n=$g(e);n!==null;n=Hg(n))for(let r=Qe;r<n.length;r++){let i=n[r];Tm(i,t)}}function q2(e){for(let t=$g(e);t!==null;t=Hg(t)){if(!(t[O]&Ms.HasTransplantedViews))continue;let n=t[Br];for(let r=0;r<n.length;r++){let i=n[r];dg(i)}}}function W2(e,t,n){let r=En(t,e);Tm(r,n)}function Tm(e,t){mu(e)&&Bc(e,t)}function Bc(e,t){let r=e[F],i=e[O],o=e[ot],s=!!(t===0&&i&16);if(s||=!!(i&64&&t===0),s||=!!(i&1024),s||=!!(o?.dirty&&Ll(o)),s||=!1,o&&(o.dirty=!1),e[O]&=-9217,s)G2(r,e,r.template,e[Tt]);else if(i&8192){xm(e,1);let a=r.components;a!==null&&Am(e,a,1)}}function Am(e,t,n){for(let r=0;r<t.length;r++)W2(e,t[r],n)}function Uu(e,t){let n=yg()?64:1088;for(e[At].changeDetectionScheduler?.notify(t);e;){e[O]|=n;let r=Qn(e);if(bc(e)&&!r)return e;e=r}return null}var er=class{get rootNodes(){let t=this._lView,n=t[F];return Rs(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[Tt]}set context(t){this._lView[Tt]=t}get destroyed(){return(this._lView[O]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Pe];if(Wt(t)){let n=t[Es],r=n?n.indexOf(this):-1;r>-1&&(Fc(t,r),Ds(n,r))}this._attachedToViewContainer=!1}tm(this._lView[F],this._lView)}onDestroy(t){fg(this._lView,t)}markForCheck(){Uu(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[O]&=-129}reattach(){Mc(this._lView),this._lView[O]|=128}detectChanges(){this._lView[O]|=1024,Sm(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new C(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=bc(this._lView),n=this._lView[Wn];n!==null&&!t&&Ru(n,this._lView),Jg(this._lView[F],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new C(902,!1);this._appRef=t;let n=bc(this._lView),r=this._lView[Wn];r!==null&&!n&&em(r,this._lView),Mc(this._lView)}},tr=(()=>{class e{static{this.__NG_ELEMENT_ID__=Y2}}return e})(),Z2=tr,Q2=class extends Z2{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let i=k2(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new er(i)}};function Y2(){return $u(ze(),ee())}function $u(e,t){return e.type&4?new Q2(t,e,Wr(e,t)):null}var W8=new RegExp(`^(\\d+)*(${xD}|${SD})*(.*)`);var K2=()=>null;function hp(e,t){return K2(e,t)}var Ur=class{},Nm=new M("",{providedIn:"root",factory:()=>!1});var Om=new M(""),Uc=class{},Fs=class{};function X2(e){let t=Error(`No component factory found for ${Re(e)}.`);return t[J2]=e,t}var J2="ngComponent";var $c=class{resolveComponentFactory(t){throw X2(t)}},$r=class{static{this.NULL=new $c}},_n=class{},Zr=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>ew()}}return e})();function ew(){let e=ee(),t=ze(),n=En(t.index,e);return(vn(n)?n:e)[Ae]}var tw=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>null})}}return e})();var pp=new Set;function sr(e){pp.has(e)||(pp.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var $e=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}($e||{}),nw={destroy(){}};function Hu(e,t){!t&&vC(Hu);let n=t?.injector??D(Ye);return VD(n)?(sr("NgAfterNextRender"),iw(e,n,!0,t?.phase??$e.MixedReadWrite)):nw}function rw(e,t){if(e instanceof Function)switch(t){case $e.EarlyRead:return{earlyRead:e};case $e.Write:return{write:e};case $e.MixedReadWrite:return{mixedReadWrite:e};case $e.Read:return{read:e}}return e}function iw(e,t,n,r){let i=rw(e,r),o=t.get(zu),s=o.handler??=new zc,a=[],l=[],c=()=>{for(let f of l)s.unregister(f);u()},u=t.get(Su).onDestroy(c),d=0,g=(f,m)=>{if(!m)return;let v=n?(...S)=>(d--,d<1&&c(),m(...S)):m,w=Ke(t,()=>new Hc(f,a,v));s.register(w),l.push(w),d++};return g($e.EarlyRead,i.earlyRead),g($e.Write,i.write),g($e.MixedReadWrite,i.mixedReadWrite),g($e.Read,i.read),{destroy:c}}var Hc=class{constructor(t,n,r){this.phase=t,this.pipelinedArgs=n,this.callbackFn=r,this.zone=D(J),this.errorHandler=D(zt,{optional:!0}),D(Ur,{optional:!0})?.notify(6)}invoke(){try{let t=this.zone.runOutsideAngular(()=>this.callbackFn.apply(null,this.pipelinedArgs));this.pipelinedArgs.splice(0,this.pipelinedArgs.length,t)}catch(t){this.errorHandler?.handleError(t)}}},zc=class{constructor(){this.executingCallbacks=!1,this.buckets={[$e.EarlyRead]:new Set,[$e.Write]:new Set,[$e.MixedReadWrite]:new Set,[$e.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(let t of Object.values(this.buckets))for(let n of t)n.invoke();this.executingCallbacks=!1;for(let t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(let t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}},zu=(()=>{class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let r of n)r()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}}return e})();function Gc(e,t,n){let r=n?e.styles:null,i=n?e.classes:null,o=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")o=a;else if(o==1)i=Bh(i,a);else if(o==2){let l=a,c=t[++s];r=Bh(r,l+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=i:e.classesWithoutHost=i}var Ls=class extends $r{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Dn(t);return new Hr(n,this.ngModule)}};function gp(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let i=e[r];if(i===void 0)continue;let o=Array.isArray(i),s=o?i[0]:i,a=o?i[1]:Cn.None;t?n.push({propName:s,templateName:r,isSignal:(a&Cn.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function ow(e){let t=e.toLowerCase();return t==="svg"?ag:t==="math"?EC:null}var Hr=class extends Fs{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=gp(t.inputs,!0);if(n!==null)for(let i of r)n.hasOwnProperty(i.propName)&&(i.transform=n[i.propName]);return r}get outputs(){return gp(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=nC(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,i){let o=Y(null);try{i=i||this.ngModule;let s=i instanceof Fe?i:i?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Sc(t,s):t,l=a.get(_n,null);if(l===null)throw new C(407,!1);let c=a.get(tw,null),u=a.get(zu,null),d=a.get(Ur,null),g={rendererFactory:l,sanitizer:c,inlineEffectRunner:null,afterRenderEventManager:u,changeDetectionScheduler:d},f=l.createRenderer(null,this.componentDef),m=this.componentDef.selectors[0][0]||"div",v=r?l2(f,r,this.componentDef.encapsulation,a):Xg(f,m,ow(m)),w=512;this.componentDef.signals?w|=4096:this.componentDef.onPush||(w|=16);let S=null;v!==null&&(S=Ou(v,a,!0));let z=Vu(0,null,null,1,0,null,null,null,null,null,null),x=oa(null,z,null,w,null,null,g,f,a,null,S);Du(x);let W,he;try{let ne=this.componentDef,ae,Ee=null;ne.findHostDirectiveDefs?(ae=[],Ee=new Map,ne.findHostDirectiveDefs(ne,ae,Ee),ae.push(ne)):ae=[ne];let jt=sw(x,v),cn=aw(jt,v,ne,ae,x,g,f);he=cg(z,Ot),v&&uw(f,ne,v,r),n!==void 0&&dw(he,this.ngContentSelectors,n),W=cw(cn,ne,ae,Ee,x,[fw]),Bu(z,x,null)}finally{wu()}return new qc(this.componentType,W,Wr(he,x),x,he)}finally{Y(o)}}},qc=class extends Uc{constructor(t,n,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new er(i,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,i;if(r!==null&&(i=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView;ju(o[F],o,i,t,n),this.previousInputValues.set(t,n);let s=En(this._tNode.index,o);Uu(s,1)}}get injector(){return new Gn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function sw(e,t){let n=e[F],r=Ot;return e[r]=t,sa(n,r,2,"#host",null)}function aw(e,t,n,r,i,o,s){let a=i[F];lw(r,e,t,s);let l=null;t!==null&&(l=Ou(t,i[jr]));let c=o.rendererFactory.createRenderer(t,n),u=16;n.signals?u=4096:n.onPush&&(u=64);let d=oa(i,gm(n),null,u,i[e.index],e,o,c,null,null,l);return a.firstCreatePass&&Vc(a,e,r.length-1),aa(i,d),i[e.index]=d}function lw(e,t,n,r){for(let i of e)t.mergedAttrs=Ii(t.mergedAttrs,i.hostAttrs);t.mergedAttrs!==null&&(Gc(t,t.mergedAttrs,!0),n!==null&&sm(r,n,t))}function cw(e,t,n,r,i,o){let s=ze(),a=i[F],l=st(s,i);ym(a,i,s,n,null,r);for(let u=0;u<n.length;u++){let d=s.directiveStart+u,g=Xn(i,a,d,s);Jn(g,i)}vm(a,i,s),l&&Jn(l,i);let c=Xn(i,a,s.directiveStart+s.componentOffset,s);if(e[Tt]=i[Tt]=c,o!==null)for(let u of o)u(c,t);return fm(a,s,i),c}function uw(e,t,n,r){if(r)Cc(e,n,["ng-version","18.1.4"]);else{let{attrs:i,classes:o}=rC(t.selectors[0]);i&&Cc(e,n,i),o&&o.length>0&&om(e,n,o.join(" "))}}function dw(e,t,n){let r=e.projection=[];for(let i=0;i<t.length;i++){let o=n[i];r.push(o!=null?Array.from(o):null)}}function fw(){let e=ze();Eu(ee()[F],e)}var Mn=(()=>{class e{static{this.__NG_ELEMENT_ID__=hw}}return e})();function hw(){let e=ze();return km(e,ee())}var pw=Mn,Pm=class extends pw{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Wr(this._hostTNode,this._hostLView)}get injector(){return new Gn(this._hostTNode,this._hostLView)}get parentInjector(){let t=Mu(this._hostTNode,this._hostLView);if(xg(t)){let n=xs(t,this._hostLView),r=Ss(t),i=n[F].data[r+8];return new Gn(i,n)}else return new Gn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=mp(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Qe}createEmbeddedView(t,n,r){let i,o;typeof r=="number"?i=r:r!=null&&(i=r.index,o=r.injector);let s=hp(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},o,s);return this.insertImpl(a,i,fp(this._hostTNode,s)),a}createComponent(t,n,r,i,o){let s=t&&!DC(t),a;if(s)a=n;else{let m=n||{};a=m.index,r=m.injector,i=m.projectableNodes,o=m.environmentInjector||m.ngModuleRef}let l=s?t:new Hr(Dn(t)),c=r||this.parentInjector;if(!o&&l.ngModule==null){let v=(s?c:this.parentInjector).get(Fe,null);v&&(o=v)}let u=Dn(l.componentType??{}),d=hp(this._lContainer,u?.id??null),g=d?.firstChild??null,f=l.create(c,i,g,o);return this.insertImpl(f.hostView,a,fp(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let i=t._lView;if(SC(i)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let l=i[Pe],c=new Pm(l,l[vt],l[Pe]);c.detach(c.indexOf(t))}}let o=this._adjustIndex(n),s=this._lContainer;return R2(s,i,o,r),t.attachToViewContainerRef(),Fp(uc(s),o,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=mp(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Fc(this._lContainer,n);r&&(Ds(uc(this._lContainer),n),tm(r[F],r))}detach(t){let n=this._adjustIndex(t,-1),r=Fc(this._lContainer,n);return r&&Ds(uc(this._lContainer),n)!=null?new er(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function mp(e){return e[Es]}function uc(e){return e[Es]||(e[Es]=[])}function km(e,t){let n,r=t[e.index];return Wt(r)?n=r:(n=Cm(r,t,null,e),t[e.index]=n,aa(t,n)),mw(n,t,e,r),new Pm(n,e,t)}function gw(e,t){let n=e[Ae],r=n.createComment(""),i=st(t,e),o=rm(n,i);return ks(n,o,r,YD(n,i),!1),r}var mw=Cw,yw=()=>!1;function vw(e,t,n){return yw(e,t,n)}function Cw(e,t,n,r){if(e[Zn])return;let i;n.type&8?i=Pt(r):i=gw(t,n),e[Zn]=i}var Wc=class e{constructor(t){this.queryList=t,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Zc=class e{constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,i=[];for(let o=0;o<r;o++){let s=n.getByIndex(o),a=this.queries[s.indexInDeclarationView];i.push(a.clone())}return new e(i)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Gu(t,n).matches!==null&&this.queries[n].setDirty()}},Qc=class{constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=Sw(t):this.predicate=t}},Yc=class e{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let i=n!==null?n.length:0,o=this.getByIndex(r).embeddedTView(t,i);o&&(o.indexInDeclarationView=r,n!==null?n.push(o):n=[o])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Kc=class e{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){let o=r[i];this.matchTNodeWithReadOption(t,n,Dw(n,o)),this.matchTNodeWithReadOption(t,n,ms(n,t,o,!1,!1))}else r===tr?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,ms(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let i=this.metadata.read;if(i!==null)if(i===at||i===Mn||i===tr&&n.type&4)this.addMatch(n.index,-2);else{let o=ms(n,t,i,!1,!1);o!==null&&this.addMatch(n.index,o)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function Dw(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function ww(e,t){return e.type&11?Wr(e,t):e.type&4?$u(e,t):null}function _w(e,t,n,r){return n===-1?ww(t,e):n===-2?bw(e,t,r):Xn(e,e[F],n,t)}function bw(e,t,n){if(n===at)return Wr(t,e);if(n===tr)return $u(t,e);if(n===Mn)return km(t,e)}function Rm(e,t,n,r){let i=t[Ht].queries[r];if(i.matches===null){let o=e.data,s=n.matches,a=[];for(let l=0;s!==null&&l<s.length;l+=2){let c=s[l];if(c<0)a.push(null);else{let u=o[c];a.push(_w(t,u,s[l+1],n.metadata.read))}}i.matches=a}return i.matches}function Xc(e,t,n,r){let i=e.queries.getByIndex(n),o=i.matches;if(o!==null){let s=Rm(e,t,i,n);for(let a=0;a<o.length;a+=2){let l=o[a];if(l>0)r.push(s[a/2]);else{let c=o[a+1],u=t[-l];for(let d=Qe;d<u.length;d++){let g=u[d];g[Wn]===g[Pe]&&Xc(g[F],g,c,r)}if(u[Br]!==null){let d=u[Br];for(let g=0;g<d.length;g++){let f=d[g];Xc(f[F],f,c,r)}}}}}return r}function Ew(e,t){return e[Ht].queries[t].queryList}function Mw(e,t,n){let r=new kc((n&4)===4);return d2(e,t,r,r.destroy),(t[Ht]??=new Zc).queries.push(new Wc(r))-1}function Iw(e,t,n){let r=He();return r.firstCreatePass&&(xw(r,new Qc(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Mw(r,ee(),t)}function Sw(e){return e.split(",").map(t=>t.trim())}function xw(e,t,n){e.queries===null&&(e.queries=new Yc),e.queries.track(new Kc(t,n))}function Gu(e,t){return e.queries.getByIndex(t)}function Tw(e,t){let n=e[F],r=Gu(n,t);return r.crossesNgTemplate?Xc(n,e,t,[]):Rm(n,e,r,t)}function Fi(e,t){sr("NgSignals");let n=hh(e),r=n[dn];return t?.equal&&(r.equal=t.equal),n.set=i=>jl(r,i),n.update=i=>ph(r,i),n.asReadonly=Aw.bind(n),n}function Aw(){let e=this[dn];if(e.readonlyFn===void 0){let t=()=>this();t[dn]=e,e.readonlyFn=t}return e.readonlyFn}function Nw(e){let t=[],n=new Map;function r(i){let o=n.get(i);if(!o){let s=e(i);n.set(i,o=s.then(Rw))}return o}return Vs.forEach((i,o)=>{let s=[];i.templateUrl&&s.push(r(i.templateUrl).then(c=>{i.template=c}));let a=typeof i.styles=="string"?[i.styles]:i.styles||[];if(i.styles=a,i.styleUrl&&i.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(i.styleUrls?.length){let c=i.styles.length,u=i.styleUrls;i.styleUrls.forEach((d,g)=>{a.push(""),s.push(r(d).then(f=>{a[c+g]=f,u.splice(u.indexOf(d),1),u.length==0&&(i.styleUrls=void 0)}))})}else i.styleUrl&&s.push(r(i.styleUrl).then(c=>{a.push(c),i.styleUrl=void 0}));let l=Promise.all(s).then(()=>Fw(o));t.push(l)}),Pw(),Promise.all(t).then(()=>{})}var Vs=new Map,Ow=new Set;function Pw(){let e=Vs;return Vs=new Map,e}function kw(){return Vs.size===0}function Rw(e){return typeof e=="string"?e:e.text()}function Fw(e){Ow.delete(e)}function Lw(e){return Object.getPrototypeOf(e.prototype).constructor}function la(e){let t=Lw(e.type),n=!0,r=[e];for(;t;){let i;if(wn(e))i=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new C(903,!1);i=t.\u0275dir}if(i){if(n){r.push(i);let s=e;s.inputs=us(e.inputs),s.inputTransforms=us(e.inputTransforms),s.declaredInputs=us(e.declaredInputs),s.outputs=us(e.outputs);let a=i.hostBindings;a&&$w(e,a);let l=i.viewQuery,c=i.contentQueries;if(l&&Bw(e,l),c&&Uw(e,c),Vw(e,i),_v(e.outputs,i.outputs),wn(i)&&i.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(i.data.animation)}}let o=i.features;if(o)for(let s=0;s<o.length;s++){let a=o[s];a&&a.ngInherit&&a(e),a===la&&(n=!1)}}t=Object.getPrototypeOf(t)}jw(r)}function Vw(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let i=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(i))continue;e.inputTransforms??={},e.inputTransforms[i]=t.inputTransforms[i]}}}function jw(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let i=e[r];i.hostVars=t+=i.hostVars,i.hostAttrs=Ii(i.hostAttrs,n=Ii(n,i.hostAttrs))}}function us(e){return e===Fr?{}:e===it?[]:e}function Bw(e,t){let n=e.viewQuery;n?e.viewQuery=(r,i)=>{t(r,i),n(r,i)}:e.viewQuery=t}function Uw(e,t){let n=e.contentQueries;n?e.contentQueries=(r,i,o)=>{t(r,i,o),n(r,i,o)}:e.contentQueries=t}function $w(e,t){let n=e.hostBindings;n?e.hostBindings=(r,i)=>{t(r,i),n(r,i)}:e.hostBindings=t}function qu(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let i=t[r];Array.isArray(i)&&i[3]&&(n[r]=i[3])}e.inputTransforms=n}var bn=class{},Ai=class{};var js=class extends bn{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Ls(this);let i=qp(t);this._bootstrapComponents=Kg(i.bootstrap),this._r3Injector=Lg(t,n,[{provide:bn,useValue:this},{provide:$r,useValue:this.componentFactoryResolver},...r],Re(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Bs=class extends Ai{constructor(t){super(),this.moduleType=t}create(t){return new js(this.moduleType,t,[])}};function Hw(e,t,n){return new js(e,t,n)}var Jc=class extends bn{constructor(t){super(),this.componentFactoryResolver=new Ls(this),this.instance=null;let n=new Si([...t.providers,{provide:bn,useValue:this},{provide:$r,useValue:this.componentFactoryResolver}],t.parent||pu(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function ca(e,t,n=null){return new Jc({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}function Fm(e){return Gw(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function zw(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Gw(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Lm(e,t,n){return e[t]=n}function nr(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Vm(e,t,n,r){let i=nr(e,t,n);return nr(e,t+1,r)||i}function qw(e){return(e.flags&32)===32}function Ww(e,t,n,r,i,o,s,a,l){let c=t.consts,u=sa(t,e,4,s||null,a||null);mm(t,n,u,Is(c,l)),Eu(t,u);let d=u.tView=Vu(2,u,r,i,o,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function Zw(e,t,n,r,i,o,s,a,l,c){let u=n+Ot,d=t.firstCreatePass?Ww(u,t,e,r,i,o,s,a,l):t.data[u];ki(d,!1);let g=Qw(t,e,d,n);_u()&&Fu(t,e,g,d),Jn(g,e);let f=Cm(g,e,g,d);return e[u]=f,aa(e,f),vw(f,d,e),gu(d)&&hm(t,e,d),l!=null&&pm(e,d,c),d}function Ne(e,t,n,r,i,o,s,a){let l=ee(),c=He(),u=Is(c.consts,o);return Zw(l,c,e,t,n,r,i,u,s,a),Ne}var Qw=Yw;function Yw(e,t,n,r){return bu(!0),t[Ae].createComment("")}function Qr(e,t,n,r){let i=ee(),o=vu();if(nr(i,o,t)){let s=He(),a=Mg();S2(a,i,e,t,n,r)}return Qr}function Kw(e,t,n,r){return nr(e,vu(),n)?t+Rr(n)+r:Qt}function Xw(e,t,n,r,i,o){let s=VC(),a=Vm(e,s,n,i);return vg(2),a?t+Rr(n)+r+Rr(i)+o:Qt}function ds(e,t){return e<<17|t<<2}function rr(e){return e>>17&32767}function Jw(e){return(e&2)==2}function e_(e,t){return e&131071|t<<17}function eu(e){return e|2}function zr(e){return(e&131068)>>2}function dc(e,t){return e&-131069|t<<2}function t_(e){return(e&1)===1}function tu(e){return e|1}function n_(e,t,n,r,i,o){let s=o?t.classBindings:t.styleBindings,a=rr(s),l=zr(s);e[r]=n;let c=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Pi(d,u)>0)&&(c=!0)}else u=n;if(i)if(l!==0){let g=rr(e[a+1]);e[r+1]=ds(g,a),g!==0&&(e[g+1]=dc(e[g+1],r)),e[a+1]=e_(e[a+1],r)}else e[r+1]=ds(a,0),a!==0&&(e[a+1]=dc(e[a+1],r)),a=r;else e[r+1]=ds(l,0),a===0?a=r:e[l+1]=dc(e[l+1],r),l=r;c&&(e[r+1]=eu(e[r+1])),yp(e,u,r,!0),yp(e,u,r,!1),r_(t,u,e,r,o),s=ds(a,l),o?t.classBindings=s:t.styleBindings=s}function r_(e,t,n,r,i){let o=i?e.residualClasses:e.residualStyles;o!=null&&typeof t=="string"&&Pi(o,t)>=0&&(n[r+1]=tu(n[r+1]))}function yp(e,t,n,r){let i=e[n+1],o=t===null,s=r?rr(i):zr(i),a=!1;for(;s!==0&&(a===!1||o);){let l=e[s],c=e[s+1];i_(l,t)&&(a=!0,e[s+1]=r?tu(c):eu(c)),s=r?rr(c):zr(c)}a&&(e[n+1]=r?eu(i):tu(i))}function i_(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Pi(e,t)>=0:!1}function ie(e,t,n){let r=ee(),i=vu();if(nr(r,i,t)){let o=He(),s=Mg();g2(o,s,r,e,t,r[Ae],n,!1)}return ie}function vp(e,t,n,r,i){let o=t.inputs,s=i?"class":"style";ju(e,n,o[s],s,r)}function Dt(e,t){return o_(e,t,null,!0),Dt}function o_(e,t,n,r){let i=ee(),o=He(),s=vg(2);if(o.firstUpdatePass&&a_(o,e,s,r),t!==Qt&&nr(i,s,t)){let a=o.data[ir()];f_(o,a,i,i[Ae],e,i[s+1]=h_(t,n),r,s)}}function s_(e,t){return t>=e.expandoStartIndex}function a_(e,t,n,r){let i=e.data;if(i[n+1]===null){let o=i[ir()],s=s_(e,n);p_(o,r)&&t===null&&!s&&(t=!1),t=l_(i,o,t,r),n_(i,o,t,n,s,r)}}function l_(e,t,n,r){let i=HC(e),o=r?t.residualClasses:t.residualStyles;if(i===null)(r?t.classBindings:t.styleBindings)===0&&(n=fc(null,e,t,n,r),n=Ni(n,t.attrs,r),o=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==i)if(n=fc(i,e,t,n,r),o===null){let l=c_(e,t,r);l!==void 0&&Array.isArray(l)&&(l=fc(null,e,t,l[1],r),l=Ni(l,t.attrs,r),u_(e,t,r,l))}else o=d_(e,t,r)}return o!==void 0&&(r?t.residualClasses=o:t.residualStyles=o),n}function c_(e,t,n){let r=n?t.classBindings:t.styleBindings;if(zr(r)!==0)return e[rr(r)]}function u_(e,t,n,r){let i=n?t.classBindings:t.styleBindings;e[rr(i)]=r}function d_(e,t,n){let r,i=t.directiveEnd;for(let o=1+t.directiveStylingLast;o<i;o++){let s=e[o].hostAttrs;r=Ni(r,s,n)}return Ni(r,t.attrs,n)}function fc(e,t,n,r,i){let o=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(o=t[a],r=Ni(r,o.hostAttrs,i),o!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Ni(e,t,n){let r=n?1:2,i=-1;if(t!==null)for(let o=0;o<t.length;o++){let s=t[o];typeof s=="number"?i=s:i===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),zv(e,s,n?!0:t[++o]))}return e===void 0?null:e}function f_(e,t,n,r,i,o,s,a){if(!(t.type&3))return;let l=e.data,c=l[a+1],u=t_(c)?Cp(l,t,n,i,zr(c),s):void 0;if(!Us(u)){Us(o)||Jw(c)&&(o=Cp(l,null,n,i,a,s));let d=lg(ir(),n);r2(r,s,d,i,o)}}function Cp(e,t,n,r,i,o){let s=t===null,a;for(;i>0;){let l=e[i],c=Array.isArray(l),u=c?l[1]:l,d=u===null,g=n[i+1];g===Qt&&(g=d?it:void 0);let f=d?nc(g,r):u===r?g:void 0;if(c&&!Us(f)&&(f=nc(l,r)),Us(f)&&(a=f,s))return a;let m=e[i+1];i=s?rr(m):zr(m)}if(t!==null){let l=o?t.residualClasses:t.residualStyles;l!=null&&(a=nc(l,r))}return a}function Us(e){return e!==void 0}function h_(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Re(Ri(e)))),e}function p_(e,t){return(e.flags&(t?8:16))!==0}function g_(e,t,n,r,i,o){let s=t.consts,a=Is(s,i),l=sa(t,e,2,r,a);return mm(t,n,l,Is(s,o)),l.attrs!==null&&Gc(l,l.attrs,!1),l.mergedAttrs!==null&&Gc(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function h(e,t,n,r){let i=ee(),o=He(),s=Ot+e,a=i[Ae],l=o.firstCreatePass?g_(s,o,i,t,n,r):o.data[s],c=m_(o,i,l,a,t,e);i[s]=c;let u=gu(l);return ki(l,!0),sm(a,c,l),!qw(l)&&_u()&&Fu(o,i,c,l),AC()===0&&Jn(c,i),NC(),u&&(hm(o,i,l),fm(o,l,i)),r!==null&&pm(i,l),h}function p(){let e=ze();mg()?LC():(e=e.parent,ki(e,!1));let t=e;kC(t)&&RC(),OC();let n=He();return n.firstCreatePass&&(Eu(n,e),ng(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&KC(t)&&vp(n,t,ee(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&XC(t)&&vp(n,t,ee(),t.stylesWithoutHost,!1),p}function y(e,t,n,r){return h(e,t,n,r),p(),y}var m_=(e,t,n,r,i,o)=>(bu(!0),Xg(r,i,WC()));var zn=void 0;function y_(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var v_=["en",[["a","p"],["AM","PM"],zn],[["AM","PM"],zn,zn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],zn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],zn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",zn,"{1} 'at' {0}",zn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",y_],hc={};function lt(e){let t=C_(e),n=Dp(t);if(n)return n;let r=t.split("-")[0];if(n=Dp(r),n)return n;if(r==="en")return v_;throw new C(701,!1)}function Dp(e){return e in hc||(hc[e]=Te.ng&&Te.ng.common&&Te.ng.common.locales&&Te.ng.common.locales[e]),hc[e]}var pe=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(pe||{});function C_(e){return e.toLowerCase().replace(/_/g,"-")}var $s="en-US";var D_=$s;function w_(e){typeof e=="string"&&(D_=e.toLowerCase().replace(/_/g,"-"))}var __=(e,t,n)=>{};function ct(e,t,n,r){let i=ee(),o=He(),s=ze();return E_(o,i,i[Ae],s,e,t,r),ct}function b_(e,t,n,r){let i=e.cleanup;if(i!=null)for(let o=0;o<i.length-1;o+=2){let s=i[o];if(s===n&&i[o+1]===r){let a=t[bs],l=i[o+2];return a.length>l?a[l]:null}typeof s=="string"&&(o+=2)}return null}function E_(e,t,n,r,i,o,s){let a=gu(r),c=e.firstCreatePass&&_m(e),u=t[Tt],d=wm(t),g=!0;if(r.type&3||s){let v=st(r,t),w=s?s(v):v,S=d.length,z=s?W=>s(Pt(W[r.index])):r.index,x=null;if(!s&&a&&(x=b_(e,t,i,r.index)),x!==null){let W=x.__ngLastListenerFn__||x;W.__ngNextListenerFn__=o,x.__ngLastListenerFn__=o,g=!1}else{o=_p(r,t,u,o),__(v,i,o);let W=n.listen(w,i,o);d.push(o,W),c&&c.push(i,z,S,S+1)}}else o=_p(r,t,u,o);let f=r.outputs,m;if(g&&f!==null&&(m=f[i])){let v=m.length;if(v)for(let w=0;w<v;w+=2){let S=m[w],z=m[w+1],he=t[S][z].subscribe(o),ne=d.length;d.push(o,he),c&&c.push(i,r.index,ne,-(ne+1))}}}function wp(e,t,n,r){let i=Y(null);try{return It(6,t,n),n(r)!==!1}catch(o){return bm(e,o),!1}finally{It(7,t,n),Y(i)}}function _p(e,t,n,r){return function i(o){if(o===Function)return r;let s=e.componentOffset>-1?En(e.index,t):t;Uu(s,5);let a=wp(t,n,r,o),l=i.__ngNextListenerFn__;for(;l;)a=wp(t,n,l,o)&&a,l=l.__ngNextListenerFn__;return a}}function Ge(e=1){return GC(e)}function In(e,t,n){Iw(e,t,n)}function Yt(e){let t=ee(),n=He(),r=Cg();Cu(r+1);let i=Gu(n,r);if(e.dirty&&IC(t)===((i.metadata.flags&2)===2)){if(i.matches===null)e.reset([]);else{let o=Tw(t,r);e.reset(o,CD),e.notifyOnChanges()}return!0}return!1}function Kt(){return Ew(ee(),Cg())}function M_(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function P(e,t=""){let n=ee(),r=He(),i=e+Ot,o=r.firstCreatePass?sa(r,i,1,t,null):r.data[i],s=I_(r,n,o,t,e);n[i]=s,_u()&&Fu(r,n,s,o),ki(o,!1)}var I_=(e,t,n,r,i)=>(bu(!0),BD(t[Ae],r));function ut(e){return Sn("",e,""),ut}function Sn(e,t,n){let r=ee(),i=Kw(r,e,t,n);return i!==Qt&&Em(r,ir(),i),Sn}function ua(e,t,n,r,i){let o=ee(),s=Xw(o,e,t,n,r,i);return s!==Qt&&Em(o,ir(),s),ua}function S_(e,t,n){let r=He();if(r.firstCreatePass){let i=wn(e);nu(n,r.data,r.blueprint,i,!0),nu(t,r.data,r.blueprint,i,!1)}}function nu(e,t,n,r,i){if(e=ke(e),Array.isArray(e))for(let o=0;o<e.length;o++)nu(e[o],t,n,r,i);else{let o=He(),s=ee(),a=ze(),l=Vr(e)?e:ke(e.provide),c=Xp(e),u=a.providerIndexes&1048575,d=a.directiveStart,g=a.providerIndexes>>20;if(Vr(e)||!e.multi){let f=new Kn(c,i,B),m=gc(l,t,i?u:u+g,d);m===-1?(Tc(As(a,s),o,l),pc(o,e,t.length),t.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[m]=f,s[m]=f)}else{let f=gc(l,t,u+g,d),m=gc(l,t,u,u+g),v=f>=0&&n[f],w=m>=0&&n[m];if(i&&!w||!i&&!v){Tc(As(a,s),o,l);let S=A_(i?T_:x_,n.length,i,r,c);!i&&w&&(n[m].providerFactory=S),pc(o,e,t.length,0),t.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),n.push(S),s.push(S)}else{let S=jm(n[i?m:f],c,!i&&r);pc(o,e,f>-1?f:m,S)}!i&&r&&w&&n[m].componentProviders++}}}function pc(e,t,n,r){let i=Vr(t),o=dC(t);if(i||o){let l=(o?ke(t.useClass):t).prototype.ngOnDestroy;if(l){let c=e.destroyHooks||(e.destroyHooks=[]);if(!i&&t.multi){let u=c.indexOf(n);u===-1?c.push(n,[r,l]):c[u+1].push(r,l)}else c.push(n,l)}}}function jm(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function gc(e,t,n,r){for(let i=n;i<r;i++)if(t[i]===e)return i;return-1}function x_(e,t,n,r){return ru(this.multi,[])}function T_(e,t,n,r){let i=this.multi,o;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Xn(n,n[F],this.providerFactory.index,r);o=a.slice(0,s),ru(i,o);for(let l=s;l<a.length;l++)o.push(a[l])}else o=[],ru(i,o);return o}function ru(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function A_(e,t,n,r,i){let o=new Kn(e,n,B);return o.multi=[],o.index=t,o.componentProviders=0,jm(o,i,r&&!n),o}function Bm(e,t=[]){return n=>{n.providersResolver=(r,i)=>S_(r,i?i(e):e,t)}}var N_=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Qp(!1,n.type),i=r.length>0?ca([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=b({token:e,providedIn:"environment",factory:()=>new e(E(Fe))})}}return e})();function Um(e){sr("NgStandalone"),e.getStandaloneInjector=t=>t.get(N_).getOrCreateStandaloneInjector(e)}function $m(e,t,n,r){return O_(ee(),yu(),e,t,n,r)}function Hm(e,t,n,r,i){return Gm(ee(),yu(),e,t,n,r,i)}function zm(e,t){let n=e[t];return n===Qt?void 0:n}function O_(e,t,n,r,i,o){let s=t+n;return nr(e,s,i)?Lm(e,s+1,o?r.call(o,i):r(i)):zm(e,s+1)}function Gm(e,t,n,r,i,o,s){let a=t+n;return Vm(e,a,i,o)?Lm(e,a+2,s?r.call(s,i,o):r(i,o)):zm(e,a+2)}function Li(e,t){let n=He(),r,i=e+Ot;n.firstCreatePass?(r=P_(t,n.pipeRegistry),n.data[i]=r,r.onDestroy&&(n.destroyHooks??=[]).push(i,r.onDestroy)):r=n.data[i];let o=r.factory||(r.factory=qn(r.type,!0)),s,a=Ue(B);try{let l=Ts(!1),c=o();return Ts(l),M_(n,ee(),i,c),c}finally{Ue(a)}}function P_(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function Vi(e,t,n,r){let i=e+Ot,o=ee(),s=MC(o,i);return k_(o,i)?Gm(o,yu(),t,s.transform,n,r,s):s.transform(n,r)}function k_(e,t){return e[F].data[t].pure}var fs=null;function R_(e){fs!==null&&(e.defaultEncapsulation!==fs.defaultEncapsulation||e.preserveWhitespaces!==fs.preserveWhitespaces)||(fs=e)}var da=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var Wu=new M(""),ji=new M(""),fa=(()=>{class e{constructor(n,r,i){this._ngZone=n,this.registry=r,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,Zu||(F_(i),i.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{J.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,i){let o=-1;r&&r>0&&(o=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==o),n()},r)),this._callbacks.push({doneCb:n,timeoutId:o,updateCb:i})}whenStable(n,r,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,i),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,i){return[]}static{this.\u0275fac=function(r){return new(r||e)(E(J),E(ha),E(ji))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),ha=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Zu?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function F_(e){Zu=e}var Zu;function ar(e){return!!e&&typeof e.then=="function"}function qm(e){return!!e&&typeof e.subscribe=="function"}var pa=new M(""),Wm=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=D(pa,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let i of this.appInits){let o=i();if(ar(o))n.push(o);else if(qm(o)){let s=new Promise((a,l)=>{o.subscribe({complete:a,error:l})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(i=>{this.reject(i)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),ga=new M("");function L_(){fh(()=>{throw new C(600,!1)})}function V_(e){return e.isBoundToModule}var j_=10;function B_(e,t,n){try{let r=n();return ar(r)?r.catch(i=>{throw t.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function Zm(e,t){return Array.isArray(t)?t.reduce(Zm,e):_(_({},e),t)}var xn=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=D(yD),this.afterRenderEffectManager=D(zu),this.zonelessEnabled=D(Nm),this.externalTestViews=new Set,this.beforeRender=new Ie,this.afterTick=new Ie,this.componentTypes=[],this.components=[],this.isStable=D(Zt).hasPendingTasks.pipe(R(n=>!n)),this._injector=D(Fe)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){let i=n instanceof Fs;if(!this._injector.get(Wm).done){let g=!i&&Gp(n),f=!1;throw new C(405,f)}let s;i?s=n:s=this._injector.get($r).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=V_(s)?void 0:this._injector.get(bn),l=r||s.selector,c=s.create(Ye.NULL,[],l,a),u=c.location.nativeElement,d=c.injector.get(Wu,null);return d?.registerApplication(u),c.onDestroy(()=>{this.detachView(c.hostView),ys(this.components,c),d?.unregisterApplication(u)}),this._loadComponent(c),c}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new C(101,!1);let r=Y(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(i){this.internalErrorHandler(i)}finally{this._runningTick=!1,Y(r),this.afterTick.next()}}detectChangesInAttachedViews(n){let r=null;this._injector.destroyed||(r=this._injector.get(_n,null,{optional:!0}));let i=0,o=this.afterRenderEffectManager;for(;i<j_;){let s=i===0;if(n||!s){this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:l}of this._views)U_(a,l,s,this.zonelessEnabled)}else r?.begin?.(),r?.end?.();if(i++,o.executeInternalCallbacks(),!this.allViews.some(({_lView:a})=>Ti(a))&&(o.execute(),!this.allViews.some(({_lView:a})=>Ti(a))))break}}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;ys(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(ga,[]);[...this._bootstrapListeners,...r].forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ys(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new C(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function ys(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function U_(e,t,n,r){if(!n&&!Ti(e))return;Sm(e,t,n&&!r?0:1)}var iu=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},ma=(()=>{class e{compileModuleSync(n){return new Bs(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),i=qp(n),o=Kg(i.declarations).reduce((s,a)=>{let l=Dn(a);return l&&s.push(new Hr(l)),s},[]);return new iu(r,o)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),$_=new M("");function H_(e,t,n){let r=new Bs(n);return Promise.resolve(r)}function bp(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var z_=(()=>{class e{constructor(){this.zone=D(J),this.changeDetectionScheduler=D(Ur),this.applicationRef=D(xn)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function G_({ngZoneFactory:e,ignoreChangesOutsideZone:t}){return e??=()=>new J(Qm()),[{provide:J,useFactory:e},{provide:Lr,multi:!0,useFactory:()=>{let n=D(z_,{optional:!0});return()=>n.initialize()}},{provide:Lr,multi:!0,useFactory:()=>{let n=D(q_);return()=>{n.initialize()}}},t===!0?{provide:Om,useValue:!0}:[]]}function Qm(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var q_=(()=>{class e{constructor(){this.subscription=new ce,this.initialized=!1,this.zone=D(J),this.pendingTasks=D(Zt)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{J.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{J.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var W_=(()=>{class e{constructor(){this.appRef=D(xn),this.taskService=D(Zt),this.ngZone=D(J),this.zonelessEnabled=D(Nm),this.disableScheduling=D(Om,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new ce,this.cancelScheduledCallback=null,this.shouldRefreshViews=!1,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Os||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;switch(n){case 3:case 2:case 0:case 4:case 5:case 1:{this.shouldRefreshViews=!0;break}case 8:case 7:case 6:case 9:default:}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?rp:Vg;this.pendingRenderTaskId=this.taskService.add(),this.zoneIsDefined?Zone.root.run(()=>{this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}):this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&J.isInAngularZone())}tick(n){if(this.runningTick||this.appRef.destroyed)return;let r=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick(n)},void 0,this.schedulerTickApplyArgs)}catch(i){throw this.taskService.remove(r),i}finally{this.cleanup()}this.useMicrotaskScheduler=!0,rp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(r)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.shouldRefreshViews=!1,this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Z_(){return typeof $localize<"u"&&$localize.locale||$s}var ya=new M("",{providedIn:"root",factory:()=>D(ya,V.Optional|V.SkipSelf)||Z_()});var Ym=new M(""),Km=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){let i=mD(r?.ngZone,Qm({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return i.run(()=>{let o=r?.ignoreChangesOutsideZone,s=Hw(n.moduleType,this.injector,[...G_({ngZoneFactory:()=>i,ignoreChangesOutsideZone:o}),{provide:Ur,useExisting:W_}]),a=s.injector.get(zt,null);return i.runOutsideAngular(()=>{let l=i.onError.subscribe({next:c=>{a.handleError(c)}});s.onDestroy(()=>{ys(this._modules,s),l.unsubscribe()})}),B_(a,i,()=>{let l=s.injector.get(Wm);return l.runInitializers(),l.donePromise.then(()=>{let c=s.injector.get(ya,$s);return w_(c||$s),this._moduleDoBootstrap(s),s})})})}bootstrapModule(n,r=[]){let i=Zm({},r);return H_(this.injector,i,n).then(o=>this.bootstrapModuleFactory(o,i))}_moduleDoBootstrap(n){let r=n.injector.get(xn);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(i=>r.bootstrap(i));else if(n.instance.ngDoBootstrap)n.instance.ngDoBootstrap(r);else throw new C(-403,!1);this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new C(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(Ym,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(E(Ye))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),Ei=null,Xm=new M("");function Q_(e){if(Ei&&!Ei.get(Xm,!1))throw new C(400,!1);L_(),Ei=e;let t=e.get(Km);return X_(e),t}function Qu(e,t,n=[]){let r=`Platform: ${t}`,i=new M(r);return(o=[])=>{let s=Jm();if(!s||s.injector.get(Xm,!1)){let a=[...n,...o,{provide:i,useValue:!0}];e?e(a):Q_(Y_(a,r))}return K_(i)}}function Y_(e=[],t){return Ye.create({name:t,providers:[{provide:Ys,useValue:"platform"},{provide:Ym,useValue:new Set([()=>Ei=null])},...e]})}function K_(e){let t=Jm();if(!t)throw new C(401,!1);return t}function Jm(){return Ei?.get(Km)??null}function X_(e){e.get(Tu,null)?.forEach(n=>n())}var Yr=(()=>{class e{static{this.__NG_ELEMENT_ID__=J_}}return e})();function J_(e){return eb(ze(),ee(),(e&16)===16)}function eb(e,t,n){if(Xs(e)&&!n){let r=En(e.index,t);return new er(r,r)}else if(e.type&175){let r=t[Nt];return new er(r,t)}return null}var ou=class{constructor(){}supports(t){return Fm(t)}create(t){return new su(t)}},tb=(e,t)=>t,su=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||tb}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,i=0,o=null;for(;n||r;){let s=!r||n&&n.currentIndex<Ep(r,i,o)?n:r,a=Ep(s,i,o),l=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)i++;else{o||(o=[]);let c=a-i,u=l-i;if(c!=u){for(let g=0;g<c;g++){let f=g<o.length?o[g]:o[g]=0,m=f+g;u<=m&&m<c&&(o[g]=f+1)}let d=s.previousIndex;o[d]=u-c}}a!==l&&t(s,a,l)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Fm(t))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,i,o,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)o=t[a],s=this._trackByFn(a,o),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,o,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,o,s,a)),Object.is(n.item,o)||this._addIdentityChange(n,o)),n=n._next}else i=0,zw(t,a=>{s=this._trackByFn(i,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,i),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,i)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,i){let o;return t===null?o=this._itTail:(o=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,o,i)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,i),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,o,i)):t=this._addAfter(new au(n,r),o,i)),t}_verifyReinsertion(t,n,r,i){let o=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return o!==null?t=this._reinsertAfter(o,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let i=t._prevRemoved,o=t._nextRemoved;return i===null?this._removalsHead=o:i._nextRemoved=o,o===null?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let i=n===null?this._itHead:n._next;return t._next=i,t._prev=n,i===null?this._itTail=t:i._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Hs),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Hs),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},au=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},lu=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Hs=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new lu,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,i=this.map.get(r);return i?i.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Ep(e,t,n){let r=e.previousIndex;if(r===null)return r;let i=0;return n&&r<n.length&&(i=n[r]),r+t+i}function Mp(){return new Yu([new ou])}var Yu=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:Mp})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Mp()),deps:[[e,new uu,new Zs]]}}find(n){let r=this.factories.find(i=>i.supports(n));if(r!=null)return r;throw new C(901,!1)}}return e})();var e0=Qu(null,"core",[]),t0=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(E(xn))}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({})}}return e})();var n0=new M("");function Kr(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Bi(e,t){sr("NgSignals");let n=ch(e);return t?.equal&&(n[dn].equal=t.equal),n}function Xt(e){let t=Y(null);try{return e()}finally{Y(t)}}function r0(e){let t=Dn(e);if(!t)return null;let n=new Hr(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var u0=null;function lr(){return u0}function d0(e){u0??=e}var Ia=class{};var be=new M(""),sd=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>D(rb),providedIn:"platform"})}}return e})(),f0=new M(""),rb=(()=>{class e extends sd{constructor(){super(),this._doc=D(be),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return lr().getBaseHref(this._doc)}onPopState(n){let r=lr().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=lr().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,i){this._history.pushState(n,r,i)}replaceState(n,r,i){this._history.replaceState(n,r,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function ad(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function i0(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function en(e){return e&&e[0]!=="?"?"?"+e:e}var nn=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>D(ld),providedIn:"root"})}}return e})(),h0=new M(""),ld=(()=>{class e extends nn{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??D(be).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return ad(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+en(this._platformLocation.search),i=this._platformLocation.hash;return i&&n?`${r}${i}`:r}pushState(n,r,i,o){let s=this.prepareExternalUrl(i+en(o));this._platformLocation.pushState(n,r,s)}replaceState(n,r,i,o){let s=this.prepareExternalUrl(i+en(o));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(E(sd),E(h0,8))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),p0=(()=>{class e extends nn{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=ad(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,i,o){let s=this.prepareExternalUrl(i+en(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,i,o){let s=this.prepareExternalUrl(i+en(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(E(sd),E(h0,8))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Jr=(()=>{class e{constructor(n){this._subject=new ye,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=sb(i0(o0(r))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+en(r))}normalize(n){return e.stripTrailingSlash(ob(this._basePath,o0(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",i=null){this._locationStrategy.pushState(i,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+en(r)),i)}replaceState(n,r="",i=null){this._locationStrategy.replaceState(i,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+en(r)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(i=>i(n,r))}subscribe(n,r,i){return this._subject.subscribe({next:n,error:r,complete:i})}static{this.normalizeQueryParams=en}static{this.joinWithSlash=ad}static{this.stripTrailingSlash=i0}static{this.\u0275fac=function(r){return new(r||e)(E(nn))}}static{this.\u0275prov=b({token:e,factory:()=>ib(),providedIn:"root"})}}return e})();function ib(){return new Jr(E(nn))}function ob(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function o0(e){return e.replace(/\/index.html$/,"")}function sb(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Le=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(Le||{}),oe=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(oe||{}),Xe=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Xe||{}),Tn={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function ab(e){return lt(e)[pe.LocaleId]}function lb(e,t,n){let r=lt(e),i=[r[pe.DayPeriodsFormat],r[pe.DayPeriodsStandalone]],o=dt(i,t);return dt(o,n)}function cb(e,t,n){let r=lt(e),i=[r[pe.DaysFormat],r[pe.DaysStandalone]],o=dt(i,t);return dt(o,n)}function ub(e,t,n){let r=lt(e),i=[r[pe.MonthsFormat],r[pe.MonthsStandalone]],o=dt(i,t);return dt(o,n)}function db(e,t){let r=lt(e)[pe.Eras];return dt(r,t)}function va(e,t){let n=lt(e);return dt(n[pe.DateFormat],t)}function Ca(e,t){let n=lt(e);return dt(n[pe.TimeFormat],t)}function Da(e,t){let r=lt(e)[pe.DateTimeFormat];return dt(r,t)}function xa(e,t){let n=lt(e),r=n[pe.NumberSymbols][t];if(typeof r>"u"){if(t===Tn.CurrencyDecimal)return n[pe.NumberSymbols][Tn.Decimal];if(t===Tn.CurrencyGroup)return n[pe.NumberSymbols][Tn.Group]}return r}function g0(e){if(!e[pe.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[pe.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function fb(e){let t=lt(e);return g0(t),(t[pe.ExtraData][2]||[]).map(r=>typeof r=="string"?Ku(r):[Ku(r[0]),Ku(r[1])])}function hb(e,t,n){let r=lt(e);g0(r);let i=[r[pe.ExtraData][0],r[pe.ExtraData][1]],o=dt(i,t)||[];return dt(o,n)||[]}function dt(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function Ku(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var pb=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,wa={},gb=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,tn=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(tn||{}),X=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(X||{}),K=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(K||{});function mb(e,t,n,r){let i=Mb(e);t=Jt(n,t)||t;let s=[],a;for(;t;)if(a=gb.exec(t),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;t=u}else{s.push(t);break}let l=i.getTimezoneOffset();r&&(l=y0(r,l),i=Eb(i,r,!0));let c="";return s.forEach(u=>{let d=_b(u);c+=d?d(i,n,l):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function Sa(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Jt(e,t){let n=ab(e);if(wa[n]??={},wa[n][t])return wa[n][t];let r="";switch(t){case"shortDate":r=va(e,Xe.Short);break;case"mediumDate":r=va(e,Xe.Medium);break;case"longDate":r=va(e,Xe.Long);break;case"fullDate":r=va(e,Xe.Full);break;case"shortTime":r=Ca(e,Xe.Short);break;case"mediumTime":r=Ca(e,Xe.Medium);break;case"longTime":r=Ca(e,Xe.Long);break;case"fullTime":r=Ca(e,Xe.Full);break;case"short":let i=Jt(e,"shortTime"),o=Jt(e,"shortDate");r=_a(Da(e,Xe.Short),[i,o]);break;case"medium":let s=Jt(e,"mediumTime"),a=Jt(e,"mediumDate");r=_a(Da(e,Xe.Medium),[s,a]);break;case"long":let l=Jt(e,"longTime"),c=Jt(e,"longDate");r=_a(Da(e,Xe.Long),[l,c]);break;case"full":let u=Jt(e,"fullTime"),d=Jt(e,"fullDate");r=_a(Da(e,Xe.Full),[u,d]);break}return r&&(wa[n][t]=r),r}function _a(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function wt(e,t,n="-",r,i){let o="";(e<0||i&&e<=0)&&(i?e=-e+1:(e=-e,o=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),o+s}function yb(e,t){return wt(e,3).substring(0,t)}function ve(e,t,n=0,r=!1,i=!1){return function(o,s){let a=vb(e,o);if((n>0||a>-n)&&(a+=n),e===X.Hours)a===0&&n===-12&&(a=12);else if(e===X.FractionalSeconds)return yb(a,t);let l=xa(s,Tn.MinusSign);return wt(a,t,l,r,i)}}function vb(e,t){switch(e){case X.FullYear:return t.getFullYear();case X.Month:return t.getMonth();case X.Date:return t.getDate();case X.Hours:return t.getHours();case X.Minutes:return t.getMinutes();case X.Seconds:return t.getSeconds();case X.FractionalSeconds:return t.getMilliseconds();case X.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function se(e,t,n=Le.Format,r=!1){return function(i,o){return Cb(i,o,e,t,n,r)}}function Cb(e,t,n,r,i,o){switch(n){case K.Months:return ub(t,i,r)[e.getMonth()];case K.Days:return cb(t,i,r)[e.getDay()];case K.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(o){let c=fb(t),u=hb(t,i,r),d=c.findIndex(g=>{if(Array.isArray(g)){let[f,m]=g,v=s>=f.hours&&a>=f.minutes,w=s<m.hours||s===m.hours&&a<m.minutes;if(f.hours<m.hours){if(v&&w)return!0}else if(v||w)return!0}else if(g.hours===s&&g.minutes===a)return!0;return!1});if(d!==-1)return u[d]}return lb(t,i,r)[s<12?0:1];case K.Eras:return db(t,r)[e.getFullYear()<=0?0:1];default:let l=n;throw new Error(`unexpected translation type ${l}`)}}function ba(e){return function(t,n,r){let i=-1*r,o=xa(n,Tn.MinusSign),s=i>0?Math.floor(i/60):Math.ceil(i/60);switch(e){case tn.Short:return(i>=0?"+":"")+wt(s,2,o)+wt(Math.abs(i%60),2,o);case tn.ShortGMT:return"GMT"+(i>=0?"+":"")+wt(s,1,o);case tn.Long:return"GMT"+(i>=0?"+":"")+wt(s,2,o)+":"+wt(Math.abs(i%60),2,o);case tn.Extended:return r===0?"Z":(i>=0?"+":"")+wt(s,2,o)+":"+wt(Math.abs(i%60),2,o);default:throw new Error(`Unknown zone width "${e}"`)}}}var Db=0,Ma=4;function wb(e){let t=Sa(e,Db,1).getDay();return Sa(e,0,1+(t<=Ma?Ma:Ma+7)-t)}function m0(e){let t=e.getDay(),n=t===0?-3:Ma-t;return Sa(e.getFullYear(),e.getMonth(),e.getDate()+n)}function Xu(e,t=!1){return function(n,r){let i;if(t){let o=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();i=1+Math.floor((s+o)/7)}else{let o=m0(n),s=wb(o.getFullYear()),a=o.getTime()-s.getTime();i=1+Math.round(a/6048e5)}return wt(i,e,xa(r,Tn.MinusSign))}}function Ea(e,t=!1){return function(n,r){let o=m0(n).getFullYear();return wt(o,e,xa(r,Tn.MinusSign),t)}}var Ju={};function _b(e){if(Ju[e])return Ju[e];let t;switch(e){case"G":case"GG":case"GGG":t=se(K.Eras,oe.Abbreviated);break;case"GGGG":t=se(K.Eras,oe.Wide);break;case"GGGGG":t=se(K.Eras,oe.Narrow);break;case"y":t=ve(X.FullYear,1,0,!1,!0);break;case"yy":t=ve(X.FullYear,2,0,!0,!0);break;case"yyy":t=ve(X.FullYear,3,0,!1,!0);break;case"yyyy":t=ve(X.FullYear,4,0,!1,!0);break;case"Y":t=Ea(1);break;case"YY":t=Ea(2,!0);break;case"YYY":t=Ea(3);break;case"YYYY":t=Ea(4);break;case"M":case"L":t=ve(X.Month,1,1);break;case"MM":case"LL":t=ve(X.Month,2,1);break;case"MMM":t=se(K.Months,oe.Abbreviated);break;case"MMMM":t=se(K.Months,oe.Wide);break;case"MMMMM":t=se(K.Months,oe.Narrow);break;case"LLL":t=se(K.Months,oe.Abbreviated,Le.Standalone);break;case"LLLL":t=se(K.Months,oe.Wide,Le.Standalone);break;case"LLLLL":t=se(K.Months,oe.Narrow,Le.Standalone);break;case"w":t=Xu(1);break;case"ww":t=Xu(2);break;case"W":t=Xu(1,!0);break;case"d":t=ve(X.Date,1);break;case"dd":t=ve(X.Date,2);break;case"c":case"cc":t=ve(X.Day,1);break;case"ccc":t=se(K.Days,oe.Abbreviated,Le.Standalone);break;case"cccc":t=se(K.Days,oe.Wide,Le.Standalone);break;case"ccccc":t=se(K.Days,oe.Narrow,Le.Standalone);break;case"cccccc":t=se(K.Days,oe.Short,Le.Standalone);break;case"E":case"EE":case"EEE":t=se(K.Days,oe.Abbreviated);break;case"EEEE":t=se(K.Days,oe.Wide);break;case"EEEEE":t=se(K.Days,oe.Narrow);break;case"EEEEEE":t=se(K.Days,oe.Short);break;case"a":case"aa":case"aaa":t=se(K.DayPeriods,oe.Abbreviated);break;case"aaaa":t=se(K.DayPeriods,oe.Wide);break;case"aaaaa":t=se(K.DayPeriods,oe.Narrow);break;case"b":case"bb":case"bbb":t=se(K.DayPeriods,oe.Abbreviated,Le.Standalone,!0);break;case"bbbb":t=se(K.DayPeriods,oe.Wide,Le.Standalone,!0);break;case"bbbbb":t=se(K.DayPeriods,oe.Narrow,Le.Standalone,!0);break;case"B":case"BB":case"BBB":t=se(K.DayPeriods,oe.Abbreviated,Le.Format,!0);break;case"BBBB":t=se(K.DayPeriods,oe.Wide,Le.Format,!0);break;case"BBBBB":t=se(K.DayPeriods,oe.Narrow,Le.Format,!0);break;case"h":t=ve(X.Hours,1,-12);break;case"hh":t=ve(X.Hours,2,-12);break;case"H":t=ve(X.Hours,1);break;case"HH":t=ve(X.Hours,2);break;case"m":t=ve(X.Minutes,1);break;case"mm":t=ve(X.Minutes,2);break;case"s":t=ve(X.Seconds,1);break;case"ss":t=ve(X.Seconds,2);break;case"S":t=ve(X.FractionalSeconds,1);break;case"SS":t=ve(X.FractionalSeconds,2);break;case"SSS":t=ve(X.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=ba(tn.Short);break;case"ZZZZZ":t=ba(tn.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=ba(tn.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=ba(tn.Long);break;default:return null}return Ju[e]=t,t}function y0(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function bb(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function Eb(e,t,n){let r=n?-1:1,i=e.getTimezoneOffset(),o=y0(t,i);return bb(e,r*(o-i))}function Mb(e){if(s0(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[i,o=1,s=1]=e.split("-").map(a=>+a);return Sa(i,o-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(pb))return Ib(r)}let t=new Date(e);if(!s0(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function Ib(e){let t=new Date(0),n=0,r=0,i=e[8]?t.setUTCFullYear:t.setFullYear,o=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),i.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,l=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return o.call(t,s,a,l,c),t}function s0(e){return e instanceof Date&&!isNaN(e.valueOf())}function Ta(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[i,o]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(i.trim()===t)return decodeURIComponent(o)}return null}var ed=/\s+/,a0=[],Aa=(()=>{class e{constructor(n,r){this._ngEl=n,this._renderer=r,this.initialClasses=a0,this.stateMap=new Map}set klass(n){this.initialClasses=n!=null?n.trim().split(ed):a0}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(ed):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let i=this.stateMap.get(n);i!==void 0?(i.enabled!==r&&(i.changed=!0,i.enabled=r),i.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],i=n[1];i.changed?(this._toggleClass(r,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),i.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(ed).forEach(i=>{r?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}static{this.\u0275fac=function(r){return new(r||e)(B(at),B(Zr))}}static{this.\u0275dir=yt({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return e})();var td=class{constructor(t,n,r,i){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=i}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},ei=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,i){this._viewContainer=n,this._template=r,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((i,o,s)=>{if(i.previousIndex==null)r.createEmbeddedView(this._template,new td(i.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(o===null?void 0:o);else if(o!==null){let a=r.get(o);r.move(a,s),l0(a,i)}});for(let i=0,o=r.length;i<o;i++){let a=r.get(i).context;a.index=i,a.count=o,a.ngForOf=this._ngForOf}n.forEachIdentityChange(i=>{let o=r.get(i.currentIndex);l0(o,i)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(B(Mn),B(tr),B(Yu))}}static{this.\u0275dir=yt({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function l0(e,t){e.context.$implicit=t.item}var An=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new nd,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){c0("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){c0("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(B(Mn),B(tr))}}static{this.\u0275dir=yt({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),nd=class{constructor(){this.$implicit=null,this.ngIf=null}};function c0(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${Re(t)}'.`)}function Sb(e,t){return new C(2100,!1)}var xb="mediumDate",Tb=new M(""),Ab=new M(""),v0=(()=>{class e{constructor(n,r,i){this.locale=n,this.defaultTimezone=r,this.defaultOptions=i}transform(n,r,i,o){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??xb,a=i??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return mb(n,s,o||this.locale,a)}catch(s){throw Sb(e,s.message)}}static{this.\u0275fac=function(r){return new(r||e)(B(ya,16),B(Tb,24),B(Ab,24))}}static{this.\u0275pipe=$p({name:"date",type:e,pure:!0,standalone:!0})}}return e})();var C0=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({})}}return e})(),cd="browser",Nb="server";function Ob(e){return e===cd}function Na(e){return e===Nb}var D0=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>Ob(D(kt))?new rd(D(be),window):new id})}}return e})(),rd=class{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=Pb(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,i=n.top+this.window.pageYOffset,o=this.offset();this.window.scrollTo(r-o[0],i-o[1])}};function Pb(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),i=r.currentNode;for(;i;){let o=i.shadowRoot;if(o){let s=o.getElementById(t)||o.querySelector(`[name="${t}"]`);if(s)return s}i=r.nextNode()}}return null}var id=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},Xr=class{};var $i=class{},Pa=class{},rn=class e{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let i=n.slice(0,r),o=i.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(i,o),this.headers.has(o)?this.headers.get(o).push(s):this.headers.set(o,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let i=(t.op==="a"?this.headers.get(n):void 0)||[];i.push(...r),this.headers.set(n,i);break;case"d":let o=t.value;if(!o)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>o.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(o=>o.toString()),i=t.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(t,i)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var dd=class{encodeKey(t){return w0(t)}encodeValue(t){return w0(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function kb(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(i=>{let o=i.indexOf("="),[s,a]=o==-1?[t.decodeKey(i),""]:[t.decodeKey(i.slice(0,o)),t.decodeValue(i.slice(o+1))],l=n.get(s)||[];l.push(a),n.set(s,l)}),n}var Rb=/%(\d[a-f0-9])/gi,Fb={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function w0(e){return encodeURIComponent(e).replace(Rb,(t,n)=>Fb[n]??t)}function Oa(e){return`${e}`}var On=class e{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new dd,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=kb(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],i=Array.isArray(r)?r.map(Oa):[Oa(r)];this.map.set(n,i)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let i=t[r];Array.isArray(i)?i.forEach(o=>{n.push({param:r,value:o,op:"a"})}):n.push({param:r,value:i,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(Oa(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],i=r.indexOf(Oa(t.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var fd=class{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function Lb(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function _0(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function b0(e){return typeof Blob<"u"&&e instanceof Blob}function E0(e){return typeof FormData<"u"&&e instanceof FormData}function Vb(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Ui=class e{constructor(t,n,r,i){this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase();let o;if(Lb(this.method)||i?(this.body=r!==void 0?r:null,o=i):o=r,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new rn,this.context??=new fd,!this.params)this.params=new On,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),l=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+l+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||_0(this.body)||b0(this.body)||E0(this.body)||Vb(this.body)?this.body:this.body instanceof On?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||E0(this.body)?null:b0(this.body)?this.body.type||null:_0(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof On?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,i=t.responseType||this.responseType,o=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,l=t.reportProgress??this.reportProgress,c=t.headers||this.headers,u=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(c=Object.keys(t.setHeaders).reduce((g,f)=>g.set(f,t.setHeaders[f]),c)),t.setParams&&(u=Object.keys(t.setParams).reduce((g,f)=>g.set(f,t.setParams[f]),u)),new e(n,r,s,{params:u,headers:c,context:d,reportProgress:l,responseType:i,withCredentials:a,transferCache:o})}},Pn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Pn||{}),Hi=class{constructor(t,n=200,r="OK"){this.headers=t.headers||new rn,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},ka=class e extends Hi{constructor(t={}){super(t),this.type=Pn.ResponseHeader}clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},zi=class e extends Hi{constructor(t={}){super(t),this.type=Pn.Response,this.body=t.body!==void 0?t.body:null}clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Nn=class extends Hi{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},T0=200,jb=204;function ud(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var on=(()=>{class e{constructor(n){this.handler=n}request(n,r,i={}){let o;if(n instanceof Ui)o=n;else{let l;i.headers instanceof rn?l=i.headers:l=new rn(i.headers);let c;i.params&&(i.params instanceof On?c=i.params:c=new On({fromObject:i.params})),o=new Ui(n,r,i.body!==void 0?i.body:null,{headers:l,context:i.context,params:c,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let s=N(o).pipe(Ut(l=>this.handler.handle(l)));if(n instanceof Ui||i.observe==="events")return s;let a=s.pipe(je(l=>l instanceof zi));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return a.pipe(R(l=>{if(l.body!==null&&!(l.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return l.body}));case"blob":return a.pipe(R(l=>{if(l.body!==null&&!(l.body instanceof Blob))throw new Error("Response is not a Blob.");return l.body}));case"text":return a.pipe(R(l=>{if(l.body!==null&&typeof l.body!="string")throw new Error("Response is not a string.");return l.body}));case"json":default:return a.pipe(R(l=>l.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new On().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,i={}){return this.request("PATCH",n,ud(i,r))}post(n,r,i={}){return this.request("POST",n,ud(i,r))}put(n,r,i={}){return this.request("PUT",n,ud(i,r))}static{this.\u0275fac=function(r){return new(r||e)(E($i))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Bb=/^\)\]\}',?\n/,Ub="X-Request-URL";function M0(e){if(e.url)return e.url;let t=Ub.toLocaleLowerCase();return e.headers.get(t)}var $b=(()=>{class e{constructor(){this.fetchImpl=D(hd,{optional:!0})?.fetch??fetch.bind(globalThis),this.ngZone=D(J)}handle(n){return new G(r=>{let i=new AbortController;return this.doRequest(n,i.signal,r).then(pd,o=>r.error(new Nn({error:o}))),()=>i.abort()})}doRequest(n,r,i){return No(this,null,function*(){let o=this.createRequestInit(n),s;try{let f=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,_({signal:r},o)));Hb(f),i.next({type:Pn.Sent}),s=yield f}catch(f){i.error(new Nn({error:f,status:f.status??0,statusText:f.statusText,url:n.urlWithParams,headers:f.headers}));return}let a=new rn(s.headers),l=s.statusText,c=M0(s)??n.urlWithParams,u=s.status,d=null;if(n.reportProgress&&i.next(new ka({headers:a,status:u,statusText:l,url:c})),s.body){let f=s.headers.get("content-length"),m=[],v=s.body.getReader(),w=0,S,z,x=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>No(this,null,function*(){for(;;){let{done:he,value:ne}=yield v.read();if(he)break;if(m.push(ne),w+=ne.length,n.reportProgress){z=n.responseType==="text"?(z??"")+(S??=new TextDecoder).decode(ne,{stream:!0}):void 0;let ae=()=>i.next({type:Pn.DownloadProgress,total:f?+f:void 0,loaded:w,partialText:z});x?x.run(ae):ae()}}}));let W=this.concatChunks(m,w);try{let he=s.headers.get("Content-Type")??"";d=this.parseBody(n,W,he)}catch(he){i.error(new Nn({error:he,headers:new rn(s.headers),status:s.status,statusText:s.statusText,url:M0(s)??n.urlWithParams}));return}}u===0&&(u=d?T0:0),u>=200&&u<300?(i.next(new zi({body:d,headers:a,status:u,statusText:l,url:c})),i.complete()):i.error(new Nn({error:d,headers:a,status:u,statusText:l,url:c}))})}parseBody(n,r,i){switch(n.responseType){case"json":let o=new TextDecoder().decode(r).replace(Bb,"");return o===""?null:JSON.parse(o);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:i});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},i=n.withCredentials?"include":void 0;if(n.headers.forEach((o,s)=>r[o]=s.join(",")),n.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!n.headers.has("Content-Type")){let o=n.detectContentTypeHeader();o!==null&&(r["Content-Type"]=o)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:i}}concatChunks(n,r){let i=new Uint8Array(r),o=0;for(let s of n)i.set(s,o),o+=s.length;return i}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),hd=class{};function pd(){}function Hb(e){e.then(pd,pd)}function A0(e,t){return t(e)}function zb(e,t){return(n,r)=>t.intercept(n,{handle:i=>e(i,r)})}function Gb(e,t,n){return(r,i)=>Ke(n,()=>t(r,o=>e(o,i)))}var qb=new M(""),gd=new M(""),Wb=new M(""),N0=new M("",{providedIn:"root",factory:()=>!0});function Zb(){let e=null;return(t,n)=>{e===null&&(e=(D(qb,{optional:!0})??[]).reduceRight(zb,A0));let r=D(Zt);if(D(N0)){let o=r.add();return e(t,n).pipe(gn(()=>r.remove(o)))}else return e(t,n)}}var I0=(()=>{class e extends $i{constructor(n,r){super(),this.backend=n,this.injector=r,this.chain=null,this.pendingTasks=D(Zt),this.contributeToStability=D(N0)}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(gd),...this.injector.get(Wb,[])]));this.chain=r.reduceRight((i,o)=>Gb(i,o,this.injector),A0)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,i=>this.backend.handle(i)).pipe(gn(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||e)(E(Pa),E(Fe))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();var Qb=/^\)\]\}',?\n/;function Yb(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var S0=(()=>{class e{constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new C(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?le(r.\u0275loadImpl()):N(null)).pipe(Be(()=>new G(o=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((v,w)=>s.setRequestHeader(v,w.join(","))),n.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){let v=n.detectContentTypeHeader();v!==null&&s.setRequestHeader("Content-Type",v)}if(n.responseType){let v=n.responseType.toLowerCase();s.responseType=v!=="json"?v:"text"}let a=n.serializeBody(),l=null,c=()=>{if(l!==null)return l;let v=s.statusText||"OK",w=new rn(s.getAllResponseHeaders()),S=Yb(s)||n.url;return l=new ka({headers:w,status:s.status,statusText:v,url:S}),l},u=()=>{let{headers:v,status:w,statusText:S,url:z}=c(),x=null;w!==jb&&(x=typeof s.response>"u"?s.responseText:s.response),w===0&&(w=x?T0:0);let W=w>=200&&w<300;if(n.responseType==="json"&&typeof x=="string"){let he=x;x=x.replace(Qb,"");try{x=x!==""?JSON.parse(x):null}catch(ne){x=he,W&&(W=!1,x={error:ne,text:x})}}W?(o.next(new zi({body:x,headers:v,status:w,statusText:S,url:z||void 0})),o.complete()):o.error(new Nn({error:x,headers:v,status:w,statusText:S,url:z||void 0}))},d=v=>{let{url:w}=c(),S=new Nn({error:v,status:s.status||0,statusText:s.statusText||"Unknown Error",url:w||void 0});o.error(S)},g=!1,f=v=>{g||(o.next(c()),g=!0);let w={type:Pn.DownloadProgress,loaded:v.loaded};v.lengthComputable&&(w.total=v.total),n.responseType==="text"&&s.responseText&&(w.partialText=s.responseText),o.next(w)},m=v=>{let w={type:Pn.UploadProgress,loaded:v.loaded};v.lengthComputable&&(w.total=v.total),o.next(w)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",m)),s.send(a),o.next({type:Pn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",m)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(E(Xr))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),O0=new M(""),Kb="XSRF-TOKEN",Xb=new M("",{providedIn:"root",factory:()=>Kb}),Jb="X-XSRF-TOKEN",eE=new M("",{providedIn:"root",factory:()=>Jb}),Ra=class{},tE=(()=>{class e{constructor(n,r,i){this.doc=n,this.platform=r,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Ta(n,this.cookieName),this.lastCookieString=n),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(E(be),E(kt),E(Xb))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function nE(e,t){let n=e.url.toLowerCase();if(!D(O0)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=D(Ra).getToken(),i=D(eE);return r!=null&&!e.headers.has(i)&&(e=e.clone({headers:e.headers.set(i,r)})),t(e)}var P0=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(P0||{});function rE(e,t){return{\u0275kind:e,\u0275providers:t}}function iE(...e){let t=[on,S0,I0,{provide:$i,useExisting:I0},{provide:Pa,useFactory:()=>D($b,{optional:!0})??D(S0)},{provide:gd,useValue:nE,multi:!0},{provide:O0,useValue:!0},{provide:Ra,useClass:tE}];for(let n of e)t.push(...n.\u0275providers);return Qs(t)}var x0=new M("");function oE(){return rE(P0.LegacyInterceptors,[{provide:x0,useFactory:Zb},{provide:gd,useExisting:x0,multi:!0}])}var k0=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({providers:[iE(oE())]})}}return e})();var vd=class extends Ia{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Cd=class e extends vd{static makeCurrent(){d0(new e)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=sE();return n==null?null:aE(n)}resetBaseElement(){Gi=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Ta(document.cookie,t)}},Gi=null;function sE(){return Gi=Gi||document.querySelector("base"),Gi?Gi.getAttribute("href"):null}function aE(e){return new URL(e,document.baseURI).pathname}var Dd=class{addToWindow(t){Te.getAngularTestability=(r,i=!0)=>{let o=t.findTestabilityInTree(r,i);if(o==null)throw new C(5103,!1);return o},Te.getAllAngularTestabilities=()=>t.getAllTestabilities(),Te.getAllAngularRootElements=()=>t.getAllRootElements();let n=r=>{let i=Te.getAllAngularTestabilities(),o=i.length,s=function(){o--,o==0&&r()};i.forEach(a=>{a.whenStable(s)})};Te.frameworkStabilizers||(Te.frameworkStabilizers=[]),Te.frameworkStabilizers.push(n)}findTestabilityInTree(t,n,r){if(n==null)return null;let i=t.getTestability(n);return i??(r?lr().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},lE=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),La=new M(""),L0=(()=>{class e{constructor(n,r){this._zone=r,this._eventNameToPlugin=new Map,n.forEach(i=>{i.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,i){return this._findPluginFor(r).addEventListener(n,r,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(o=>o.supports(n)),!r)throw new C(5101,!1);return this._eventNameToPlugin.set(n,r),r}static{this.\u0275fac=function(r){return new(r||e)(E(La),E(J))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),qi=class{constructor(t){this._doc=t}},md="ng-app-id",V0=(()=>{class e{constructor(n,r,i,o={}){this.doc=n,this.appId=r,this.nonce=i,this.platformId=o,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Na(o),this.resetHostNodes()}addStyles(n){for(let r of n)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(n){for(let r of n)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let n=this.styleNodesInDOM;n&&(n.forEach(r=>r.remove()),n.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(let r of this.getAllStyles())this.addStyleToHost(n,r)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(let r of this.hostNodes)this.addStyleToHost(r,n)}onStyleRemoved(n){let r=this.styleRef;r.get(n)?.elements?.forEach(i=>i.remove()),r.delete(n)}collectServerRenderedStyles(){let n=this.doc.head?.querySelectorAll(`style[${md}="${this.appId}"]`);if(n?.length){let r=new Map;return n.forEach(i=>{i.textContent!=null&&r.set(i.textContent,i)}),r}return null}changeUsageCount(n,r){let i=this.styleRef;if(i.has(n)){let o=i.get(n);return o.usage+=r,o.usage}return i.set(n,{usage:r,elements:[]}),r}getStyleElement(n,r){let i=this.styleNodesInDOM,o=i?.get(r);if(o?.parentNode===n)return i.delete(r),o.removeAttribute(md),o;{let s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(md,this.appId),n.appendChild(s),s}}addStyleToHost(n,r){let i=this.getStyleElement(n,r),o=this.styleRef,s=o.get(r)?.elements;s?s.push(i):o.set(r,{elements:[i],usage:1})}resetHostNodes(){let n=this.hostNodes;n.clear(),n.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(E(be),E(na),E(Nu,8),E(kt))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),yd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},_d=/%COMP%/g,j0="%COMP%",cE=`_nghost-${j0}`,uE=`_ngcontent-${j0}`,dE=!0,fE=new M("",{providedIn:"root",factory:()=>dE});function hE(e){return uE.replace(_d,e)}function pE(e){return cE.replace(_d,e)}function B0(e,t){return t.map(n=>n.replace(_d,e))}var Va=(()=>{class e{constructor(n,r,i,o,s,a,l,c=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.rendererByCompId=new Map,this.platformIsServer=Na(a),this.defaultRenderer=new Wi(n,s,l,this.platformIsServer)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===xt.ShadowDom&&(r=Q(_({},r),{encapsulation:xt.Emulated}));let i=this.getOrCreateRenderer(n,r);return i instanceof ja?i.applyToHost(n):i instanceof Zi&&i.applyStyles(),i}getOrCreateRenderer(n,r){let i=this.rendererByCompId,o=i.get(r.id);if(!o){let s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case xt.Emulated:o=new ja(l,c,r,this.appId,u,s,a,d);break;case xt.ShadowDom:return new wd(l,c,n,r,s,a,this.nonce,d);default:o=new Zi(l,c,r,u,s,a,d);break}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(E(L0),E(V0),E(na),E(fE),E(be),E(kt),E(J),E(Nu))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Wi=class{constructor(t,n,r,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=i,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(yd[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(R0(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(R0(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new C(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,i){if(i){n=i+":"+n;let o=yd[i];o?t.setAttributeNS(o,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let i=yd[r];i?t.removeAttributeNS(i,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,i){i&(Gt.DashCase|Gt.Important)?t.style.setProperty(n,r,i&Gt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&Gt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r){if(typeof t=="string"&&(t=lr().getGlobalEventTarget(this.doc,t),!t))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function R0(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var wd=class extends Wi{constructor(t,n,r,i,o,s,a,l){super(t,o,s,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let c=B0(i.id,i.styles);for(let u of c){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=u,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Zi=class extends Wi{constructor(t,n,r,i,o,s,a,l){super(t,o,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=i,this.styles=l?B0(l,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},ja=class extends Zi{constructor(t,n,r,i,o,s,a,l){let c=i+"-"+r.id;super(t,n,r,o,s,a,l,c),this.contentAttr=hE(c),this.hostAttr=pE(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}},gE=(()=>{class e extends qi{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,i){return n.addEventListener(r,i,!1),()=>this.removeEventListener(n,r,i)}removeEventListener(n,r,i){return n.removeEventListener(r,i)}static{this.\u0275fac=function(r){return new(r||e)(E(be))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),mE=(()=>{class e extends qi{constructor(n){super(n),this.delegate=D(n0,{optional:!0})}supports(n){return this.delegate?this.delegate.supports(n):!1}addEventListener(n,r,i){return this.delegate.addEventListener(n,r,i)}removeEventListener(n,r,i){return this.delegate.removeEventListener(n,r,i)}static{this.\u0275fac=function(r){return new(r||e)(E(be))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),F0=["alt","control","meta","shift"],yE={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},vE={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},CE=(()=>{class e extends qi{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,i){let o=e.parseEventName(r),s=e.eventCallback(o.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>lr().onAndCancel(n,o.domEventName,s))}static parseEventName(n){let r=n.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let o=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),F0.forEach(c=>{let u=r.indexOf(c);u>-1&&(r.splice(u,1),s+=c+".")}),s+=o,r.length!=0||o.length===0)return null;let l={};return l.domEventName=i,l.fullKey=s,l}static matchEventFullKeyCode(n,r){let i=yE[n.key]||n.key,o="";return r.indexOf("code.")>-1&&(i=n.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),F0.forEach(s=>{if(s!==i){let a=vE[s];a(n)&&(o+=s+".")}}),o+=i,o===r)}static eventCallback(n,r,i){return o=>{e.matchEventFullKeyCode(o,n)&&i.runGuarded(()=>r(o))}}static _normalizeKey(n){return n==="esc"?"escape":n}static{this.\u0275fac=function(r){return new(r||e)(E(be))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function DE(){Cd.makeCurrent()}function wE(){return new zt}function _E(){return Gg(document),document}var bE=[{provide:kt,useValue:cd},{provide:Tu,useValue:DE,multi:!0},{provide:be,useFactory:_E,deps:[]}],U0=Qu(e0,"browser",bE),EE=new M(""),ME=[{provide:ji,useClass:Dd,deps:[]},{provide:Wu,useClass:fa,deps:[J,ha,ji]},{provide:fa,useClass:fa,deps:[J,ha,ji]}],IE=[{provide:Ys,useValue:"root"},{provide:zt,useFactory:wE,deps:[]},{provide:La,useClass:gE,multi:!0,deps:[be,J,kt]},{provide:La,useClass:CE,multi:!0,deps:[be]},{provide:La,useClass:mE,multi:!0},Va,V0,L0,{provide:_n,useExisting:Va},{provide:Xr,useClass:lE,deps:[]},[]],Ba=(()=>{class e{constructor(n){}static withServerTransition(n){return{ngModule:e,providers:[{provide:na,useValue:n.appId}]}}static{this.\u0275fac=function(r){return new(r||e)(E(EE,12))}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({providers:[...IE,...ME],imports:[C0,t0]})}}return e})();var $0=(()=>{class e{constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static{this.\u0275fac=function(r){return new(r||e)(E(be))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var U=function(e){return e[e.State=0]="State",e[e.Transition=1]="Transition",e[e.Sequence=2]="Sequence",e[e.Group=3]="Group",e[e.Animate=4]="Animate",e[e.Keyframes=5]="Keyframes",e[e.Style=6]="Style",e[e.Trigger=7]="Trigger",e[e.Reference=8]="Reference",e[e.AnimateChild=9]="AnimateChild",e[e.AnimateRef=10]="AnimateRef",e[e.Query=11]="Query",e[e.Stagger=12]="Stagger",e}(U||{}),Rt="*";function z0(e,t=null){return{type:U.Sequence,steps:e,options:t}}function bd(e){return{type:U.Style,styles:e,offset:null}}var kn=class{constructor(t=0,n=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=t+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Qi=class{constructor(t){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=t;let n=0,r=0,i=0,o=this.players.length;o==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==o&&this._onFinish()}),s.onDestroy(()=>{++r==o&&this._onDestroy()}),s.onStart(()=>{++i==o&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let n=t*this.totalTime;this.players.forEach(r=>{let i=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(i)})}getPosition(){let t=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Ua="!";function G0(e){return new C(3e3,!1)}function SE(){return new C(3100,!1)}function xE(){return new C(3101,!1)}function TE(e){return new C(3001,!1)}function AE(e){return new C(3003,!1)}function NE(e){return new C(3004,!1)}function OE(e,t){return new C(3005,!1)}function PE(){return new C(3006,!1)}function kE(){return new C(3007,!1)}function RE(e,t){return new C(3008,!1)}function FE(e){return new C(3002,!1)}function LE(e,t,n,r,i){return new C(3010,!1)}function VE(){return new C(3011,!1)}function jE(){return new C(3012,!1)}function BE(){return new C(3200,!1)}function UE(){return new C(3202,!1)}function $E(){return new C(3013,!1)}function HE(e){return new C(3014,!1)}function zE(e){return new C(3015,!1)}function GE(e){return new C(3016,!1)}function qE(e,t){return new C(3404,!1)}function WE(e){return new C(3502,!1)}function ZE(e){return new C(3503,!1)}function QE(){return new C(3300,!1)}function YE(e){return new C(3504,!1)}function KE(e){return new C(3301,!1)}function XE(e,t){return new C(3302,!1)}function JE(e){return new C(3303,!1)}function eM(e,t){return new C(3400,!1)}function tM(e){return new C(3401,!1)}function nM(e){return new C(3402,!1)}function rM(e,t){return new C(3505,!1)}function Rn(e){switch(e.length){case 0:return new kn;case 1:return e[0];default:return new Qi(e)}}function oy(e,t,n=new Map,r=new Map){let i=[],o=[],s=-1,a=null;if(t.forEach(l=>{let c=l.get("offset"),u=c==s,d=u&&a||new Map;l.forEach((g,f)=>{let m=f,v=g;if(f!=="offset")switch(m=e.normalizePropertyName(m,i),v){case Ua:v=n.get(f);break;case Rt:v=r.get(f);break;default:v=e.normalizeStyleValue(f,m,v,i);break}d.set(m,v)}),u||o.push(d),a=d,s=c}),i.length)throw WE(i);return o}function qd(e,t,n,r){switch(t){case"start":e.onStart(()=>r(n&&Ed(n,"start",e)));break;case"done":e.onDone(()=>r(n&&Ed(n,"done",e)));break;case"destroy":e.onDestroy(()=>r(n&&Ed(n,"destroy",e)));break}}function Ed(e,t,n){let r=n.totalTime,i=!!n.disabled,o=Wd(e.element,e.triggerName,e.fromState,e.toState,t||e.phaseName,r??e.totalTime,i),s=e._data;return s!=null&&(o._data=s),o}function Wd(e,t,n,r,i="",o=0,s){return{element:e,triggerName:t,fromState:n,toState:r,phaseName:i,totalTime:o,disabled:!!s}}function et(e,t,n){let r=e.get(t);return r||e.set(t,r=n),r}function q0(e){let t=e.indexOf(":"),n=e.substring(1,t),r=e.slice(t+1);return[n,r]}var iM=typeof document>"u"?null:document.documentElement;function Zd(e){let t=e.parentNode||e.host||null;return t===iM?null:t}function oM(e){return e.substring(1,6)=="ebkit"}var ur=null,W0=!1;function sM(e){ur||(ur=aM()||{},W0=ur.style?"WebkitAppearance"in ur.style:!1);let t=!0;return ur.style&&!oM(e)&&(t=e in ur.style,!t&&W0&&(t="Webkit"+e.charAt(0).toUpperCase()+e.slice(1)in ur.style)),t}function aM(){return typeof document<"u"?document.body:null}function sy(e,t){for(;t;){if(t===e)return!0;t=Zd(t)}return!1}function ay(e,t,n){if(n)return Array.from(e.querySelectorAll(t));let r=e.querySelector(t);return r?[r]:[]}var Qd=(()=>{class e{validateStyleProperty(n){return sM(n)}containsElement(n,r){return sy(n,r)}getParentElement(n){return Zd(n)}query(n,r,i){return ay(n,r,i)}computeStyle(n,r,i){return i||""}animate(n,r,i,o,s,a=[],l){return new kn(i,o)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),hr=class{static{this.NOOP=new Qd}},pr=class{};var lM=1e3,ly="{{",cM="}}",cy="ng-enter",Ad="ng-leave",$a="ng-trigger",Wa=".ng-trigger",Z0="ng-animating",Nd=".ng-animating";function sn(e){if(typeof e=="number")return e;let t=e.match(/^(-?[\.\d]+)(m?s)/);return!t||t.length<2?0:Od(parseFloat(t[1]),t[2])}function Od(e,t){switch(t){case"s":return e*lM;default:return e}}function Za(e,t,n){return e.hasOwnProperty("duration")?e:uM(e,t,n)}function uM(e,t,n){let r=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,o=0,s="";if(typeof e=="string"){let a=e.match(r);if(a===null)return t.push(G0(e)),{duration:0,delay:0,easing:""};i=Od(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(o=Od(parseFloat(l),a[4]));let c=a[5];c&&(s=c)}else i=e;if(!n){let a=!1,l=t.length;i<0&&(t.push(SE()),a=!0),o<0&&(t.push(xE()),a=!0),a&&t.splice(l,0,G0(e))}return{duration:i,delay:o,easing:s}}function dM(e){return e.length?e[0]instanceof Map?e:e.map(t=>new Map(Object.entries(t))):[]}function Ft(e,t,n){t.forEach((r,i)=>{let o=Yd(i);n&&!n.has(i)&&n.set(i,e.style[o]),e.style[o]=r})}function fr(e,t){t.forEach((n,r)=>{let i=Yd(r);e.style[i]=""})}function Yi(e){return Array.isArray(e)?e.length==1?e[0]:z0(e):e}function fM(e,t,n){let r=t.params||{},i=uy(e);i.length&&i.forEach(o=>{r.hasOwnProperty(o)||n.push(TE(o))})}var Pd=new RegExp(`${ly}\\s*(.+?)\\s*${cM}`,"g");function uy(e){let t=[];if(typeof e=="string"){let n;for(;n=Pd.exec(e);)t.push(n[1]);Pd.lastIndex=0}return t}function Xi(e,t,n){let r=`${e}`,i=r.replace(Pd,(o,s)=>{let a=t[s];return a==null&&(n.push(AE(s)),a=""),a.toString()});return i==r?e:i}var hM=/-+([a-z0-9])/g;function Yd(e){return e.replace(hM,(...t)=>t[1].toUpperCase())}function pM(e,t){return e===0||t===0}function gM(e,t,n){if(n.size&&t.length){let r=t[0],i=[];if(n.forEach((o,s)=>{r.has(s)||i.push(s),r.set(s,o)}),i.length)for(let o=1;o<t.length;o++){let s=t[o];i.forEach(a=>s.set(a,Kd(e,a)))}}return t}function Je(e,t,n){switch(t.type){case U.Trigger:return e.visitTrigger(t,n);case U.State:return e.visitState(t,n);case U.Transition:return e.visitTransition(t,n);case U.Sequence:return e.visitSequence(t,n);case U.Group:return e.visitGroup(t,n);case U.Animate:return e.visitAnimate(t,n);case U.Keyframes:return e.visitKeyframes(t,n);case U.Style:return e.visitStyle(t,n);case U.Reference:return e.visitReference(t,n);case U.AnimateChild:return e.visitAnimateChild(t,n);case U.AnimateRef:return e.visitAnimateRef(t,n);case U.Query:return e.visitQuery(t,n);case U.Stagger:return e.visitStagger(t,n);default:throw NE(t.type)}}function Kd(e,t){return window.getComputedStyle(e)[t]}var mM=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Qa=class extends pr{normalizePropertyName(t,n){return Yd(t)}normalizeStyleValue(t,n,r,i){let o="",s=r.toString().trim();if(mM.has(n)&&r!==0&&r!=="0")if(typeof r=="number")o="px";else{let a=r.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&i.push(OE(t,r))}return s+o}};var Ya="*";function yM(e,t){let n=[];return typeof e=="string"?e.split(/\s*,\s*/).forEach(r=>vM(r,n,t)):n.push(e),n}function vM(e,t,n){if(e[0]==":"){let l=CM(e,n);if(typeof l=="function"){t.push(l);return}e=l}let r=e.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(r==null||r.length<4)return n.push(zE(e)),t;let i=r[1],o=r[2],s=r[3];t.push(Q0(i,s));let a=i==Ya&&s==Ya;o[0]=="<"&&!a&&t.push(Q0(s,i))}function CM(e,t){switch(e){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(n,r)=>parseFloat(r)>parseFloat(n);case":decrement":return(n,r)=>parseFloat(r)<parseFloat(n);default:return t.push(GE(e)),"* => *"}}var Ha=new Set(["true","1"]),za=new Set(["false","0"]);function Q0(e,t){let n=Ha.has(e)||za.has(e),r=Ha.has(t)||za.has(t);return(i,o)=>{let s=e==Ya||e==i,a=t==Ya||t==o;return!s&&n&&typeof i=="boolean"&&(s=i?Ha.has(e):za.has(e)),!a&&r&&typeof o=="boolean"&&(a=o?Ha.has(t):za.has(t)),s&&a}}var dy=":self",DM=new RegExp(`s*${dy}s*,?`,"g");function fy(e,t,n,r){return new kd(e).build(t,n,r)}var Y0="",kd=class{constructor(t){this._driver=t}build(t,n,r){let i=new Rd(n);return this._resetContextStyleTimingState(i),Je(this,Yi(t),i)}_resetContextStyleTimingState(t){t.currentQuerySelector=Y0,t.collectedStyles=new Map,t.collectedStyles.set(Y0,new Map),t.currentTime=0}visitTrigger(t,n){let r=n.queryCount=0,i=n.depCount=0,o=[],s=[];return t.name.charAt(0)=="@"&&n.errors.push(PE()),t.definitions.forEach(a=>{if(this._resetContextStyleTimingState(n),a.type==U.State){let l=a,c=l.name;c.toString().split(/\s*,\s*/).forEach(u=>{l.name=u,o.push(this.visitState(l,n))}),l.name=c}else if(a.type==U.Transition){let l=this.visitTransition(a,n);r+=l.queryCount,i+=l.depCount,s.push(l)}else n.errors.push(kE())}),{type:U.Trigger,name:t.name,states:o,transitions:s,queryCount:r,depCount:i,options:null}}visitState(t,n){let r=this.visitStyle(t.styles,n),i=t.options&&t.options.params||null;if(r.containsDynamicStyles){let o=new Set,s=i||{};r.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{uy(l).forEach(c=>{s.hasOwnProperty(c)||o.add(c)})})}),o.size&&n.errors.push(RE(t.name,[...o.values()]))}return{type:U.State,name:t.name,style:r,options:i?{params:i}:null}}visitTransition(t,n){n.queryCount=0,n.depCount=0;let r=Je(this,Yi(t.animation),n),i=yM(t.expr,n.errors);return{type:U.Transition,matchers:i,animation:r,queryCount:n.queryCount,depCount:n.depCount,options:dr(t.options)}}visitSequence(t,n){return{type:U.Sequence,steps:t.steps.map(r=>Je(this,r,n)),options:dr(t.options)}}visitGroup(t,n){let r=n.currentTime,i=0,o=t.steps.map(s=>{n.currentTime=r;let a=Je(this,s,n);return i=Math.max(i,n.currentTime),a});return n.currentTime=i,{type:U.Group,steps:o,options:dr(t.options)}}visitAnimate(t,n){let r=EM(t.timings,n.errors);n.currentAnimateTimings=r;let i,o=t.styles?t.styles:bd({});if(o.type==U.Keyframes)i=this.visitKeyframes(o,n);else{let s=t.styles,a=!1;if(!s){a=!0;let c={};r.easing&&(c.easing=r.easing),s=bd(c)}n.currentTime+=r.duration+r.delay;let l=this.visitStyle(s,n);l.isEmptyStep=a,i=l}return n.currentAnimateTimings=null,{type:U.Animate,timings:r,style:i,options:null}}visitStyle(t,n){let r=this._makeStyleAst(t,n);return this._validateStyleAst(r,n),r}_makeStyleAst(t,n){let r=[],i=Array.isArray(t.styles)?t.styles:[t.styles];for(let a of i)typeof a=="string"?a===Rt?r.push(a):n.errors.push(FE(a)):r.push(new Map(Object.entries(a)));let o=!1,s=null;return r.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(s=a.get("easing"),a.delete("easing")),!o)){for(let l of a.values())if(l.toString().indexOf(ly)>=0){o=!0;break}}}),{type:U.Style,styles:r,easing:s,offset:t.offset,containsDynamicStyles:o,options:null}}_validateStyleAst(t,n){let r=n.currentAnimateTimings,i=n.currentTime,o=n.currentTime;r&&o>0&&(o-=r.duration+r.delay),t.styles.forEach(s=>{typeof s!="string"&&s.forEach((a,l)=>{let c=n.collectedStyles.get(n.currentQuerySelector),u=c.get(l),d=!0;u&&(o!=i&&o>=u.startTime&&i<=u.endTime&&(n.errors.push(LE(l,u.startTime,u.endTime,o,i)),d=!1),o=u.startTime),d&&c.set(l,{startTime:o,endTime:i}),n.options&&fM(a,n.options,n.errors)})})}visitKeyframes(t,n){let r={type:U.Keyframes,styles:[],options:null};if(!n.currentAnimateTimings)return n.errors.push(VE()),r;let i=1,o=0,s=[],a=!1,l=!1,c=0,u=t.steps.map(S=>{let z=this._makeStyleAst(S,n),x=z.offset!=null?z.offset:bM(z.styles),W=0;return x!=null&&(o++,W=z.offset=x),l=l||W<0||W>1,a=a||W<c,c=W,s.push(W),z});l&&n.errors.push(jE()),a&&n.errors.push(BE());let d=t.steps.length,g=0;o>0&&o<d?n.errors.push(UE()):o==0&&(g=i/(d-1));let f=d-1,m=n.currentTime,v=n.currentAnimateTimings,w=v.duration;return u.forEach((S,z)=>{let x=g>0?z==f?1:g*z:s[z],W=x*w;n.currentTime=m+v.delay+W,v.duration=W,this._validateStyleAst(S,n),S.offset=x,r.styles.push(S)}),r}visitReference(t,n){return{type:U.Reference,animation:Je(this,Yi(t.animation),n),options:dr(t.options)}}visitAnimateChild(t,n){return n.depCount++,{type:U.AnimateChild,options:dr(t.options)}}visitAnimateRef(t,n){return{type:U.AnimateRef,animation:this.visitReference(t.animation,n),options:dr(t.options)}}visitQuery(t,n){let r=n.currentQuerySelector,i=t.options||{};n.queryCount++,n.currentQuery=t;let[o,s]=wM(t.selector);n.currentQuerySelector=r.length?r+" "+o:o,et(n.collectedStyles,n.currentQuerySelector,new Map);let a=Je(this,Yi(t.animation),n);return n.currentQuery=null,n.currentQuerySelector=r,{type:U.Query,selector:o,limit:i.limit||0,optional:!!i.optional,includeSelf:s,animation:a,originalSelector:t.selector,options:dr(t.options)}}visitStagger(t,n){n.currentQuery||n.errors.push($E());let r=t.timings==="full"?{duration:0,delay:0,easing:"full"}:Za(t.timings,n.errors,!0);return{type:U.Stagger,animation:Je(this,Yi(t.animation),n),timings:r,options:null}}};function wM(e){let t=!!e.split(/\s*,\s*/).find(n=>n==dy);return t&&(e=e.replace(DM,"")),e=e.replace(/@\*/g,Wa).replace(/@\w+/g,n=>Wa+"-"+n.slice(1)).replace(/:animating/g,Nd),[e,t]}function _M(e){return e?_({},e):null}var Rd=class{constructor(t){this.errors=t,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}};function bM(e){if(typeof e=="string")return null;let t=null;if(Array.isArray(e))e.forEach(n=>{if(n instanceof Map&&n.has("offset")){let r=n;t=parseFloat(r.get("offset")),r.delete("offset")}});else if(e instanceof Map&&e.has("offset")){let n=e;t=parseFloat(n.get("offset")),n.delete("offset")}return t}function EM(e,t){if(e.hasOwnProperty("duration"))return e;if(typeof e=="number"){let o=Za(e,t).duration;return Md(o,0,"")}let n=e;if(n.split(/\s+/).some(o=>o.charAt(0)=="{"&&o.charAt(1)=="{")){let o=Md(0,0,"");return o.dynamic=!0,o.strValue=n,o}let i=Za(n,t);return Md(i.duration,i.delay,i.easing)}function dr(e){return e?(e=_({},e),e.params&&(e.params=_M(e.params))):e={},e}function Md(e,t,n){return{duration:e,delay:t,easing:n}}function Xd(e,t,n,r,i,o,s=null,a=!1){return{type:1,element:e,keyframes:t,preStyleProps:n,postStyleProps:r,duration:i,delay:o,totalTime:i+o,easing:s,subTimeline:a}}var Ji=class{constructor(){this._map=new Map}get(t){return this._map.get(t)||[]}append(t,n){let r=this._map.get(t);r||this._map.set(t,r=[]),r.push(...n)}has(t){return this._map.has(t)}clear(){this._map.clear()}},MM=1,IM=":enter",SM=new RegExp(IM,"g"),xM=":leave",TM=new RegExp(xM,"g");function hy(e,t,n,r,i,o=new Map,s=new Map,a,l,c=[]){return new Fd().buildKeyframes(e,t,n,r,i,o,s,a,l,c)}var Fd=class{buildKeyframes(t,n,r,i,o,s,a,l,c,u=[]){c=c||new Ji;let d=new Ld(t,n,c,i,o,u,[]);d.options=l;let g=l.delay?sn(l.delay):0;d.currentTimeline.delayNextStep(g),d.currentTimeline.setStyles([s],null,d.errors,l),Je(this,r,d);let f=d.timelines.filter(m=>m.containsAnimation());if(f.length&&a.size){let m;for(let v=f.length-1;v>=0;v--){let w=f[v];if(w.element===n){m=w;break}}m&&!m.allowOnlyTimelineStyles()&&m.setStyles([a],null,d.errors,l)}return f.length?f.map(m=>m.buildKeyframes()):[Xd(n,[],[],[],0,g,"",!1)]}visitTrigger(t,n){}visitState(t,n){}visitTransition(t,n){}visitAnimateChild(t,n){let r=n.subInstructions.get(n.element);if(r){let i=n.createSubContext(t.options),o=n.currentTimeline.currentTime,s=this._visitSubInstructions(r,i,i.options);o!=s&&n.transformIntoNewTimeline(s)}n.previousNode=t}visitAnimateRef(t,n){let r=n.createSubContext(t.options);r.transformIntoNewTimeline(),this._applyAnimationRefDelays([t.options,t.animation.options],n,r),this.visitReference(t.animation,r),n.transformIntoNewTimeline(r.currentTimeline.currentTime),n.previousNode=t}_applyAnimationRefDelays(t,n,r){for(let i of t){let o=i?.delay;if(o){let s=typeof o=="number"?o:sn(Xi(o,i?.params??{},n.errors));r.delayNextStep(s)}}}_visitSubInstructions(t,n,r){let o=n.currentTimeline.currentTime,s=r.duration!=null?sn(r.duration):null,a=r.delay!=null?sn(r.delay):null;return s!==0&&t.forEach(l=>{let c=n.appendInstructionToTimeline(l,s,a);o=Math.max(o,c.duration+c.delay)}),o}visitReference(t,n){n.updateOptions(t.options,!0),Je(this,t.animation,n),n.previousNode=t}visitSequence(t,n){let r=n.subContextCount,i=n,o=t.options;if(o&&(o.params||o.delay)&&(i=n.createSubContext(o),i.transformIntoNewTimeline(),o.delay!=null)){i.previousNode.type==U.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=Ka);let s=sn(o.delay);i.delayNextStep(s)}t.steps.length&&(t.steps.forEach(s=>Je(this,s,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>r&&i.transformIntoNewTimeline()),n.previousNode=t}visitGroup(t,n){let r=[],i=n.currentTimeline.currentTime,o=t.options&&t.options.delay?sn(t.options.delay):0;t.steps.forEach(s=>{let a=n.createSubContext(t.options);o&&a.delayNextStep(o),Je(this,s,a),i=Math.max(i,a.currentTimeline.currentTime),r.push(a.currentTimeline)}),r.forEach(s=>n.currentTimeline.mergeTimelineCollectedStyles(s)),n.transformIntoNewTimeline(i),n.previousNode=t}_visitTiming(t,n){if(t.dynamic){let r=t.strValue,i=n.params?Xi(r,n.params,n.errors):r;return Za(i,n.errors)}else return{duration:t.duration,delay:t.delay,easing:t.easing}}visitAnimate(t,n){let r=n.currentAnimateTimings=this._visitTiming(t.timings,n),i=n.currentTimeline;r.delay&&(n.incrementTime(r.delay),i.snapshotCurrentStyles());let o=t.style;o.type==U.Keyframes?this.visitKeyframes(o,n):(n.incrementTime(r.duration),this.visitStyle(o,n),i.applyStylesToKeyframe()),n.currentAnimateTimings=null,n.previousNode=t}visitStyle(t,n){let r=n.currentTimeline,i=n.currentAnimateTimings;!i&&r.hasCurrentStyleProperties()&&r.forwardFrame();let o=i&&i.easing||t.easing;t.isEmptyStep?r.applyEmptyStep(o):r.setStyles(t.styles,o,n.errors,n.options),n.previousNode=t}visitKeyframes(t,n){let r=n.currentAnimateTimings,i=n.currentTimeline.duration,o=r.duration,a=n.createSubContext().currentTimeline;a.easing=r.easing,t.styles.forEach(l=>{let c=l.offset||0;a.forwardTime(c*o),a.setStyles(l.styles,l.easing,n.errors,n.options),a.applyStylesToKeyframe()}),n.currentTimeline.mergeTimelineCollectedStyles(a),n.transformIntoNewTimeline(i+o),n.previousNode=t}visitQuery(t,n){let r=n.currentTimeline.currentTime,i=t.options||{},o=i.delay?sn(i.delay):0;o&&(n.previousNode.type===U.Style||r==0&&n.currentTimeline.hasCurrentStyleProperties())&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=Ka);let s=r,a=n.invokeQuery(t.selector,t.originalSelector,t.limit,t.includeSelf,!!i.optional,n.errors);n.currentQueryTotal=a.length;let l=null;a.forEach((c,u)=>{n.currentQueryIndex=u;let d=n.createSubContext(t.options,c);o&&d.delayNextStep(o),c===n.element&&(l=d.currentTimeline),Je(this,t.animation,d),d.currentTimeline.applyStylesToKeyframe();let g=d.currentTimeline.currentTime;s=Math.max(s,g)}),n.currentQueryIndex=0,n.currentQueryTotal=0,n.transformIntoNewTimeline(s),l&&(n.currentTimeline.mergeTimelineCollectedStyles(l),n.currentTimeline.snapshotCurrentStyles()),n.previousNode=t}visitStagger(t,n){let r=n.parentContext,i=n.currentTimeline,o=t.timings,s=Math.abs(o.duration),a=s*(n.currentQueryTotal-1),l=s*n.currentQueryIndex;switch(o.duration<0?"reverse":o.easing){case"reverse":l=a-l;break;case"full":l=r.currentStaggerTime;break}let u=n.currentTimeline;l&&u.delayNextStep(l);let d=u.currentTime;Je(this,t.animation,n),n.previousNode=t,r.currentStaggerTime=i.currentTime-d+(i.startTime-r.currentTimeline.startTime)}},Ka={},Ld=class e{constructor(t,n,r,i,o,s,a,l){this._driver=t,this.element=n,this.subInstructions=r,this._enterClassName=i,this._leaveClassName=o,this.errors=s,this.timelines=a,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=Ka,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=l||new Xa(this._driver,n,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(t,n){if(!t)return;let r=t,i=this.options;r.duration!=null&&(i.duration=sn(r.duration)),r.delay!=null&&(i.delay=sn(r.delay));let o=r.params;if(o){let s=i.params;s||(s=this.options.params={}),Object.keys(o).forEach(a=>{(!n||!s.hasOwnProperty(a))&&(s[a]=Xi(o[a],s,this.errors))})}}_copyOptions(){let t={};if(this.options){let n=this.options.params;if(n){let r=t.params={};Object.keys(n).forEach(i=>{r[i]=n[i]})}}return t}createSubContext(t=null,n,r){let i=n||this.element,o=new e(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,r||0));return o.previousNode=this.previousNode,o.currentAnimateTimings=this.currentAnimateTimings,o.options=this._copyOptions(),o.updateOptions(t),o.currentQueryIndex=this.currentQueryIndex,o.currentQueryTotal=this.currentQueryTotal,o.parentContext=this,this.subContextCount++,o}transformIntoNewTimeline(t){return this.previousNode=Ka,this.currentTimeline=this.currentTimeline.fork(this.element,t),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(t,n,r){let i={duration:n??t.duration,delay:this.currentTimeline.currentTime+(r??0)+t.delay,easing:""},o=new Vd(this._driver,t.element,t.keyframes,t.preStyleProps,t.postStyleProps,i,t.stretchStartingKeyframe);return this.timelines.push(o),i}incrementTime(t){this.currentTimeline.forwardTime(this.currentTimeline.duration+t)}delayNextStep(t){t>0&&this.currentTimeline.delayNextStep(t)}invokeQuery(t,n,r,i,o,s){let a=[];if(i&&a.push(this.element),t.length>0){t=t.replace(SM,"."+this._enterClassName),t=t.replace(TM,"."+this._leaveClassName);let l=r!=1,c=this._driver.query(this.element,t,l);r!==0&&(c=r<0?c.slice(c.length+r,c.length):c.slice(0,r)),a.push(...c)}return!o&&a.length==0&&s.push(HE(n)),a}},Xa=class e{constructor(t,n,r,i){this._driver=t,this.element=n,this.startTime=r,this._elementTimelineStylesLookup=i,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(n),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(n,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(t){let n=this._keyframes.size===1&&this._pendingStyles.size;this.duration||n?(this.forwardTime(this.currentTime+t),n&&this.snapshotCurrentStyles()):this.startTime+=t}fork(t,n){return this.applyStylesToKeyframe(),new e(this._driver,t,n||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=MM,this._loadKeyframe()}forwardTime(t){this.applyStylesToKeyframe(),this.duration=t,this._loadKeyframe()}_updateStyle(t,n){this._localTimelineStyles.set(t,n),this._globalTimelineStyles.set(t,n),this._styleSummary.set(t,{time:this.currentTime,value:n})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(t){t&&this._previousKeyframe.set("easing",t);for(let[n,r]of this._globalTimelineStyles)this._backFill.set(n,r||Rt),this._currentKeyframe.set(n,Rt);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(t,n,r,i){n&&this._previousKeyframe.set("easing",n);let o=i&&i.params||{},s=AM(t,this._globalTimelineStyles);for(let[a,l]of s){let c=Xi(l,o,r);this._pendingStyles.set(a,c),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??Rt),this._updateStyle(a,c)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((t,n)=>{this._currentKeyframe.set(n,t)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((t,n)=>{this._currentKeyframe.has(n)||this._currentKeyframe.set(n,t)}))}snapshotCurrentStyles(){for(let[t,n]of this._localTimelineStyles)this._pendingStyles.set(t,n),this._updateStyle(t,n)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let t=[];for(let n in this._currentKeyframe)t.push(n);return t}mergeTimelineCollectedStyles(t){t._styleSummary.forEach((n,r)=>{let i=this._styleSummary.get(r);(!i||n.time>i.time)&&this._updateStyle(r,n.value)})}buildKeyframes(){this.applyStylesToKeyframe();let t=new Set,n=new Set,r=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((a,l)=>{let c=new Map([...this._backFill,...a]);c.forEach((u,d)=>{u===Ua?t.add(d):u===Rt&&n.add(d)}),r||c.set("offset",l/this.duration),i.push(c)});let o=[...t.values()],s=[...n.values()];if(r){let a=i[0],l=new Map(a);a.set("offset",0),l.set("offset",1),i=[a,l]}return Xd(this.element,i,o,s,this.duration,this.startTime,this.easing,!1)}},Vd=class extends Xa{constructor(t,n,r,i,o,s,a=!1){super(t,n,s.delay),this.keyframes=r,this.preStyleProps=i,this.postStyleProps=o,this._stretchStartingKeyframe=a,this.timings={duration:s.duration,delay:s.delay,easing:s.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let t=this.keyframes,{delay:n,duration:r,easing:i}=this.timings;if(this._stretchStartingKeyframe&&n){let o=[],s=r+n,a=n/s,l=new Map(t[0]);l.set("offset",0),o.push(l);let c=new Map(t[0]);c.set("offset",K0(a)),o.push(c);let u=t.length-1;for(let d=1;d<=u;d++){let g=new Map(t[d]),f=g.get("offset"),m=n+f*r;g.set("offset",K0(m/s)),o.push(g)}r=s,n=0,i="",t=o}return Xd(this.element,t,this.preStyleProps,this.postStyleProps,r,n,i,!0)}};function K0(e,t=3){let n=Math.pow(10,t-1);return Math.round(e*n)/n}function AM(e,t){let n=new Map,r;return e.forEach(i=>{if(i==="*"){r??=t.keys();for(let o of r)n.set(o,Rt)}else for(let[o,s]of i)n.set(o,s)}),n}function X0(e,t,n,r,i,o,s,a,l,c,u,d,g){return{type:0,element:e,triggerName:t,isRemovalTransition:i,fromState:n,fromStyles:o,toState:r,toStyles:s,timelines:a,queriedElements:l,preStyleProps:c,postStyleProps:u,totalTime:d,errors:g}}var Id={},Ja=class{constructor(t,n,r){this._triggerName=t,this.ast=n,this._stateStyles=r}match(t,n,r,i){return NM(this.ast.matchers,t,n,r,i)}buildStyles(t,n,r){let i=this._stateStyles.get("*");return t!==void 0&&(i=this._stateStyles.get(t?.toString())||i),i?i.buildStyles(n,r):new Map}build(t,n,r,i,o,s,a,l,c,u){let d=[],g=this.ast.options&&this.ast.options.params||Id,f=a&&a.params||Id,m=this.buildStyles(r,f,d),v=l&&l.params||Id,w=this.buildStyles(i,v,d),S=new Set,z=new Map,x=new Map,W=i==="void",he={params:py(v,g),delay:this.ast.options?.delay},ne=u?[]:hy(t,n,this.ast.animation,o,s,m,w,he,c,d),ae=0;return ne.forEach(Ee=>{ae=Math.max(Ee.duration+Ee.delay,ae)}),d.length?X0(n,this._triggerName,r,i,W,m,w,[],[],z,x,ae,d):(ne.forEach(Ee=>{let jt=Ee.element,cn=et(z,jt,new Set);Ee.preStyleProps.forEach(jn=>cn.add(jn));let Zf=et(x,jt,new Set);Ee.postStyleProps.forEach(jn=>Zf.add(jn)),jt!==n&&S.add(jt)}),X0(n,this._triggerName,r,i,W,m,w,ne,[...S.values()],z,x,ae))}};function NM(e,t,n,r,i){return e.some(o=>o(t,n,r,i))}function py(e,t){let n=_({},t);return Object.entries(e).forEach(([r,i])=>{i!=null&&(n[r]=i)}),n}var jd=class{constructor(t,n,r){this.styles=t,this.defaultParams=n,this.normalizer=r}buildStyles(t,n){let r=new Map,i=py(t,this.defaultParams);return this.styles.styles.forEach(o=>{typeof o!="string"&&o.forEach((s,a)=>{s&&(s=Xi(s,i,n));let l=this.normalizer.normalizePropertyName(a,n);s=this.normalizer.normalizeStyleValue(a,l,s,n),r.set(a,s)})}),r}};function OM(e,t,n){return new Bd(e,t,n)}var Bd=class{constructor(t,n,r){this.name=t,this.ast=n,this._normalizer=r,this.transitionFactories=[],this.states=new Map,n.states.forEach(i=>{let o=i.options&&i.options.params||{};this.states.set(i.name,new jd(i.style,o,r))}),J0(this.states,"true","1"),J0(this.states,"false","0"),n.transitions.forEach(i=>{this.transitionFactories.push(new Ja(t,i,this.states))}),this.fallbackTransition=PM(t,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(t,n,r,i){return this.transitionFactories.find(s=>s.match(t,n,r,i))||null}matchStyles(t,n,r){return this.fallbackTransition.buildStyles(t,n,r)}};function PM(e,t,n){let r=[(s,a)=>!0],i={type:U.Sequence,steps:[],options:null},o={type:U.Transition,animation:i,matchers:r,options:null,queryCount:0,depCount:0};return new Ja(e,o,t)}function J0(e,t,n){e.has(t)?e.has(n)||e.set(n,e.get(t)):e.has(n)&&e.set(t,e.get(n))}var kM=new Ji,Ud=class{constructor(t,n,r){this.bodyNode=t,this._driver=n,this._normalizer=r,this._animations=new Map,this._playersById=new Map,this.players=[]}register(t,n){let r=[],i=[],o=fy(this._driver,n,r,i);if(r.length)throw ZE(r);i.length&&void 0,this._animations.set(t,o)}_buildPlayer(t,n,r){let i=t.element,o=oy(this._normalizer,t.keyframes,n,r);return this._driver.animate(i,o,t.duration,t.delay,t.easing,[],!0)}create(t,n,r={}){let i=[],o=this._animations.get(t),s,a=new Map;if(o?(s=hy(this._driver,n,o,cy,Ad,new Map,new Map,r,kM,i),s.forEach(u=>{let d=et(a,u.element,new Map);u.postStyleProps.forEach(g=>d.set(g,null))})):(i.push(QE()),s=[]),i.length)throw YE(i);a.forEach((u,d)=>{u.forEach((g,f)=>{u.set(f,this._driver.computeStyle(d,f,Rt))})});let l=s.map(u=>{let d=a.get(u.element);return this._buildPlayer(u,new Map,d)}),c=Rn(l);return this._playersById.set(t,c),c.onDestroy(()=>this.destroy(t)),this.players.push(c),c}destroy(t){let n=this._getPlayer(t);n.destroy(),this._playersById.delete(t);let r=this.players.indexOf(n);r>=0&&this.players.splice(r,1)}_getPlayer(t){let n=this._playersById.get(t);if(!n)throw KE(t);return n}listen(t,n,r,i){let o=Wd(n,"","","");return qd(this._getPlayer(t),r,o,i),()=>{}}command(t,n,r,i){if(r=="register"){this.register(t,i[0]);return}if(r=="create"){let s=i[0]||{};this.create(t,n,s);return}let o=this._getPlayer(t);switch(r){case"play":o.play();break;case"pause":o.pause();break;case"reset":o.reset();break;case"restart":o.restart();break;case"finish":o.finish();break;case"init":o.init();break;case"setPosition":o.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(t);break}}},ey="ng-animate-queued",RM=".ng-animate-queued",Sd="ng-animate-disabled",FM=".ng-animate-disabled",LM="ng-star-inserted",VM=".ng-star-inserted",jM=[],gy={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},BM={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},_t="__ng_removed",eo=class{get params(){return this.options.params}constructor(t,n=""){this.namespaceId=n;let r=t&&t.hasOwnProperty("value"),i=r?t.value:t;if(this.value=$M(i),r){let o=t,{value:s}=o,a=Ol(o,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(t){let n=t.params;if(n){let r=this.options.params;Object.keys(n).forEach(i=>{r[i]==null&&(r[i]=n[i])})}}},Ki="void",xd=new eo(Ki),$d=class{constructor(t,n,r){this.id=t,this.hostElement=n,this._engine=r,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+t,ft(n,this._hostClassName)}listen(t,n,r,i){if(!this._triggers.has(n))throw XE(r,n);if(r==null||r.length==0)throw JE(n);if(!HM(r))throw eM(r,n);let o=et(this._elementListeners,t,[]),s={name:n,phase:r,callback:i};o.push(s);let a=et(this._engine.statesByElement,t,new Map);return a.has(n)||(ft(t,$a),ft(t,$a+"-"+n),a.set(n,xd)),()=>{this._engine.afterFlush(()=>{let l=o.indexOf(s);l>=0&&o.splice(l,1),this._triggers.has(n)||a.delete(n)})}}register(t,n){return this._triggers.has(t)?!1:(this._triggers.set(t,n),!0)}_getTrigger(t){let n=this._triggers.get(t);if(!n)throw tM(t);return n}trigger(t,n,r,i=!0){let o=this._getTrigger(n),s=new to(this.id,n,t),a=this._engine.statesByElement.get(t);a||(ft(t,$a),ft(t,$a+"-"+n),this._engine.statesByElement.set(t,a=new Map));let l=a.get(n),c=new eo(r,this.id);if(!(r&&r.hasOwnProperty("value"))&&l&&c.absorbOptions(l.options),a.set(n,c),l||(l=xd),!(c.value===Ki)&&l.value===c.value){if(!qM(l.params,c.params)){let v=[],w=o.matchStyles(l.value,l.params,v),S=o.matchStyles(c.value,c.params,v);v.length?this._engine.reportError(v):this._engine.afterFlush(()=>{fr(t,w),Ft(t,S)})}return}let g=et(this._engine.playersByElement,t,[]);g.forEach(v=>{v.namespaceId==this.id&&v.triggerName==n&&v.queued&&v.destroy()});let f=o.matchTransition(l.value,c.value,t,c.params),m=!1;if(!f){if(!i)return;f=o.fallbackTransition,m=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:n,transition:f,fromState:l,toState:c,player:s,isFallbackTransition:m}),m||(ft(t,ey),s.onStart(()=>{ti(t,ey)})),s.onDone(()=>{let v=this.players.indexOf(s);v>=0&&this.players.splice(v,1);let w=this._engine.playersByElement.get(t);if(w){let S=w.indexOf(s);S>=0&&w.splice(S,1)}}),this.players.push(s),g.push(s),s}deregister(t){this._triggers.delete(t),this._engine.statesByElement.forEach(n=>n.delete(t)),this._elementListeners.forEach((n,r)=>{this._elementListeners.set(r,n.filter(i=>i.name!=t))})}clearElementCache(t){this._engine.statesByElement.delete(t),this._elementListeners.delete(t);let n=this._engine.playersByElement.get(t);n&&(n.forEach(r=>r.destroy()),this._engine.playersByElement.delete(t))}_signalRemovalForInnerTriggers(t,n){let r=this._engine.driver.query(t,Wa,!0);r.forEach(i=>{if(i[_t])return;let o=this._engine.fetchNamespacesByElement(i);o.size?o.forEach(s=>s.triggerLeaveAnimation(i,n,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>r.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(t,n,r,i){let o=this._engine.statesByElement.get(t),s=new Map;if(o){let a=[];if(o.forEach((l,c)=>{if(s.set(c,l.value),this._triggers.has(c)){let u=this.trigger(t,c,Ki,i);u&&a.push(u)}}),a.length)return this._engine.markElementAsRemoved(this.id,t,!0,n,s),r&&Rn(a).onDone(()=>this._engine.processLeaveNode(t)),!0}return!1}prepareLeaveAnimationListeners(t){let n=this._elementListeners.get(t),r=this._engine.statesByElement.get(t);if(n&&r){let i=new Set;n.forEach(o=>{let s=o.name;if(i.has(s))return;i.add(s);let l=this._triggers.get(s).fallbackTransition,c=r.get(s)||xd,u=new eo(Ki),d=new to(this.id,s,t);this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:s,transition:l,fromState:c,toState:u,player:d,isFallbackTransition:!0})})}}removeNode(t,n){let r=this._engine;if(t.childElementCount&&this._signalRemovalForInnerTriggers(t,n),this.triggerLeaveAnimation(t,n,!0))return;let i=!1;if(r.totalAnimations){let o=r.players.length?r.playersByQueriedElement.get(t):[];if(o&&o.length)i=!0;else{let s=t;for(;s=s.parentNode;)if(r.statesByElement.get(s)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(t),i)r.markElementAsRemoved(this.id,t,!1,n);else{let o=t[_t];(!o||o===gy)&&(r.afterFlush(()=>this.clearElementCache(t)),r.destroyInnerAnimations(t),r._onRemovalComplete(t,n))}}insertNode(t,n){ft(t,this._hostClassName)}drainQueuedTransitions(t){let n=[];return this._queue.forEach(r=>{let i=r.player;if(i.destroyed)return;let o=r.element,s=this._elementListeners.get(o);s&&s.forEach(a=>{if(a.name==r.triggerName){let l=Wd(o,r.triggerName,r.fromState.value,r.toState.value);l._data=t,qd(r.player,a.phase,l,a.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):n.push(r)}),this._queue=[],n.sort((r,i)=>{let o=r.transition.ast.depCount,s=i.transition.ast.depCount;return o==0||s==0?o-s:this._engine.driver.containsElement(r.element,i.element)?1:-1})}destroy(t){this.players.forEach(n=>n.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,t)}},Hd=class{_onRemovalComplete(t,n){this.onRemovalComplete(t,n)}constructor(t,n,r){this.bodyNode=t,this.driver=n,this._normalizer=r,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(i,o)=>{}}get queuedPlayers(){let t=[];return this._namespaceList.forEach(n=>{n.players.forEach(r=>{r.queued&&t.push(r)})}),t}createNamespace(t,n){let r=new $d(t,n,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,n)?this._balanceNamespaceList(r,n):(this.newHostElements.set(n,r),this.collectEnterElement(n)),this._namespaceLookup[t]=r}_balanceNamespaceList(t,n){let r=this._namespaceList,i=this.namespacesByHostElement;if(r.length-1>=0){let s=!1,a=this.driver.getParentElement(n);for(;a;){let l=i.get(a);if(l){let c=r.indexOf(l);r.splice(c+1,0,t),s=!0;break}a=this.driver.getParentElement(a)}s||r.unshift(t)}else r.push(t);return i.set(n,t),t}register(t,n){let r=this._namespaceLookup[t];return r||(r=this.createNamespace(t,n)),r}registerTrigger(t,n,r){let i=this._namespaceLookup[t];i&&i.register(n,r)&&this.totalAnimations++}destroy(t,n){t&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let r=this._fetchNamespace(t);this.namespacesByHostElement.delete(r.hostElement);let i=this._namespaceList.indexOf(r);i>=0&&this._namespaceList.splice(i,1),r.destroy(n),delete this._namespaceLookup[t]}))}_fetchNamespace(t){return this._namespaceLookup[t]}fetchNamespacesByElement(t){let n=new Set,r=this.statesByElement.get(t);if(r){for(let i of r.values())if(i.namespaceId){let o=this._fetchNamespace(i.namespaceId);o&&n.add(o)}}return n}trigger(t,n,r,i){if(Ga(n)){let o=this._fetchNamespace(t);if(o)return o.trigger(n,r,i),!0}return!1}insertNode(t,n,r,i){if(!Ga(n))return;let o=n[_t];if(o&&o.setForRemoval){o.setForRemoval=!1,o.setForMove=!0;let s=this.collectedLeaveElements.indexOf(n);s>=0&&this.collectedLeaveElements.splice(s,1)}if(t){let s=this._fetchNamespace(t);s&&s.insertNode(n,r)}i&&this.collectEnterElement(n)}collectEnterElement(t){this.collectedEnterElements.push(t)}markElementAsDisabled(t,n){n?this.disabledNodes.has(t)||(this.disabledNodes.add(t),ft(t,Sd)):this.disabledNodes.has(t)&&(this.disabledNodes.delete(t),ti(t,Sd))}removeNode(t,n,r){if(Ga(n)){let i=t?this._fetchNamespace(t):null;i?i.removeNode(n,r):this.markElementAsRemoved(t,n,!1,r);let o=this.namespacesByHostElement.get(n);o&&o.id!==t&&o.removeNode(n,r)}else this._onRemovalComplete(n,r)}markElementAsRemoved(t,n,r,i,o){this.collectedLeaveElements.push(n),n[_t]={namespaceId:t,setForRemoval:i,hasAnimation:r,removedBeforeQueried:!1,previousTriggersValues:o}}listen(t,n,r,i,o){return Ga(n)?this._fetchNamespace(t).listen(n,r,i,o):()=>{}}_buildInstruction(t,n,r,i,o){return t.transition.build(this.driver,t.element,t.fromState.value,t.toState.value,r,i,t.fromState.options,t.toState.options,n,o)}destroyInnerAnimations(t){let n=this.driver.query(t,Wa,!0);n.forEach(r=>this.destroyActiveAnimationsForElement(r)),this.playersByQueriedElement.size!=0&&(n=this.driver.query(t,Nd,!0),n.forEach(r=>this.finishActiveQueriedAnimationOnElement(r)))}destroyActiveAnimationsForElement(t){let n=this.playersByElement.get(t);n&&n.forEach(r=>{r.queued?r.markedForDestroy=!0:r.destroy()})}finishActiveQueriedAnimationOnElement(t){let n=this.playersByQueriedElement.get(t);n&&n.forEach(r=>r.finish())}whenRenderingDone(){return new Promise(t=>{if(this.players.length)return Rn(this.players).onDone(()=>t());t()})}processLeaveNode(t){let n=t[_t];if(n&&n.setForRemoval){if(t[_t]=gy,n.namespaceId){this.destroyInnerAnimations(t);let r=this._fetchNamespace(n.namespaceId);r&&r.clearElementCache(t)}this._onRemovalComplete(t,n.setForRemoval)}t.classList?.contains(Sd)&&this.markElementAsDisabled(t,!1),this.driver.query(t,FM,!0).forEach(r=>{this.markElementAsDisabled(r,!1)})}flush(t=-1){let n=[];if(this.newHostElements.size&&(this.newHostElements.forEach((r,i)=>this._balanceNamespaceList(r,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let r=0;r<this.collectedEnterElements.length;r++){let i=this.collectedEnterElements[r];ft(i,LM)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let r=[];try{n=this._flushAnimations(r,t)}finally{for(let i=0;i<r.length;i++)r[i]()}}else for(let r=0;r<this.collectedLeaveElements.length;r++){let i=this.collectedLeaveElements[r];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(r=>r()),this._flushFns=[],this._whenQuietFns.length){let r=this._whenQuietFns;this._whenQuietFns=[],n.length?Rn(n).onDone(()=>{r.forEach(i=>i())}):r.forEach(i=>i())}}reportError(t){throw nM(t)}_flushAnimations(t,n){let r=new Ji,i=[],o=new Map,s=[],a=new Map,l=new Map,c=new Map,u=new Set;this.disabledNodes.forEach(I=>{u.add(I);let T=this.driver.query(I,RM,!0);for(let A=0;A<T.length;A++)u.add(T[A])});let d=this.bodyNode,g=Array.from(this.statesByElement.keys()),f=ry(g,this.collectedEnterElements),m=new Map,v=0;f.forEach((I,T)=>{let A=cy+v++;m.set(T,A),I.forEach(Z=>ft(Z,A))});let w=[],S=new Set,z=new Set;for(let I=0;I<this.collectedLeaveElements.length;I++){let T=this.collectedLeaveElements[I],A=T[_t];A&&A.setForRemoval&&(w.push(T),S.add(T),A.hasAnimation?this.driver.query(T,VM,!0).forEach(Z=>S.add(Z)):z.add(T))}let x=new Map,W=ry(g,Array.from(S));W.forEach((I,T)=>{let A=Ad+v++;x.set(T,A),I.forEach(Z=>ft(Z,A))}),t.push(()=>{f.forEach((I,T)=>{let A=m.get(T);I.forEach(Z=>ti(Z,A))}),W.forEach((I,T)=>{let A=x.get(T);I.forEach(Z=>ti(Z,A))}),w.forEach(I=>{this.processLeaveNode(I)})});let he=[],ne=[];for(let I=this._namespaceList.length-1;I>=0;I--)this._namespaceList[I].drainQueuedTransitions(n).forEach(A=>{let Z=A.player,Me=A.element;if(he.push(Z),this.collectedEnterElements.length){let Oe=Me[_t];if(Oe&&Oe.setForMove){if(Oe.previousTriggersValues&&Oe.previousTriggersValues.has(A.triggerName)){let Bn=Oe.previousTriggersValues.get(A.triggerName),rt=this.statesByElement.get(A.element);if(rt&&rt.has(A.triggerName)){let To=rt.get(A.triggerName);To.value=Bn,rt.set(A.triggerName,To)}}Z.destroy();return}}let Et=!d||!this.driver.containsElement(d,Me),qe=x.get(Me),un=m.get(Me),ue=this._buildInstruction(A,r,un,qe,Et);if(ue.errors&&ue.errors.length){ne.push(ue);return}if(Et){Z.onStart(()=>fr(Me,ue.fromStyles)),Z.onDestroy(()=>Ft(Me,ue.toStyles)),i.push(Z);return}if(A.isFallbackTransition){Z.onStart(()=>fr(Me,ue.fromStyles)),Z.onDestroy(()=>Ft(Me,ue.toStyles)),i.push(Z);return}let Kf=[];ue.timelines.forEach(Oe=>{Oe.stretchStartingKeyframe=!0,this.disabledNodes.has(Oe.element)||Kf.push(Oe)}),ue.timelines=Kf,r.append(Me,ue.timelines);let z1={instruction:ue,player:Z,element:Me};s.push(z1),ue.queriedElements.forEach(Oe=>et(a,Oe,[]).push(Z)),ue.preStyleProps.forEach((Oe,Bn)=>{if(Oe.size){let rt=l.get(Bn);rt||l.set(Bn,rt=new Set),Oe.forEach((To,Nl)=>rt.add(Nl))}}),ue.postStyleProps.forEach((Oe,Bn)=>{let rt=c.get(Bn);rt||c.set(Bn,rt=new Set),Oe.forEach((To,Nl)=>rt.add(Nl))})});if(ne.length){let I=[];ne.forEach(T=>{I.push(rM(T.triggerName,T.errors))}),he.forEach(T=>T.destroy()),this.reportError(I)}let ae=new Map,Ee=new Map;s.forEach(I=>{let T=I.element;r.has(T)&&(Ee.set(T,T),this._beforeAnimationBuild(I.player.namespaceId,I.instruction,ae))}),i.forEach(I=>{let T=I.element;this._getPreviousPlayers(T,!1,I.namespaceId,I.triggerName,null).forEach(Z=>{et(ae,T,[]).push(Z),Z.destroy()})});let jt=w.filter(I=>iy(I,l,c)),cn=new Map;ny(cn,this.driver,z,c,Rt).forEach(I=>{iy(I,l,c)&&jt.push(I)});let jn=new Map;f.forEach((I,T)=>{ny(jn,this.driver,new Set(I),l,Ua)}),jt.forEach(I=>{let T=cn.get(I),A=jn.get(I);cn.set(I,new Map([...T?.entries()??[],...A?.entries()??[]]))});let Al=[],Qf=[],Yf={};s.forEach(I=>{let{element:T,player:A,instruction:Z}=I;if(r.has(T)){if(u.has(T)){A.onDestroy(()=>Ft(T,Z.toStyles)),A.disabled=!0,A.overrideTotalTime(Z.totalTime),i.push(A);return}let Me=Yf;if(Ee.size>1){let qe=T,un=[];for(;qe=qe.parentNode;){let ue=Ee.get(qe);if(ue){Me=ue;break}un.push(qe)}un.forEach(ue=>Ee.set(ue,Me))}let Et=this._buildAnimation(A.namespaceId,Z,ae,o,jn,cn);if(A.setRealPlayer(Et),Me===Yf)Al.push(A);else{let qe=this.playersByElement.get(Me);qe&&qe.length&&(A.parentPlayer=Rn(qe)),i.push(A)}}else fr(T,Z.fromStyles),A.onDestroy(()=>Ft(T,Z.toStyles)),Qf.push(A),u.has(T)&&i.push(A)}),Qf.forEach(I=>{let T=o.get(I.element);if(T&&T.length){let A=Rn(T);I.setRealPlayer(A)}}),i.forEach(I=>{I.parentPlayer?I.syncPlayerEvents(I.parentPlayer):I.destroy()});for(let I=0;I<w.length;I++){let T=w[I],A=T[_t];if(ti(T,Ad),A&&A.hasAnimation)continue;let Z=[];if(a.size){let Et=a.get(T);Et&&Et.length&&Z.push(...Et);let qe=this.driver.query(T,Nd,!0);for(let un=0;un<qe.length;un++){let ue=a.get(qe[un]);ue&&ue.length&&Z.push(...ue)}}let Me=Z.filter(Et=>!Et.destroyed);Me.length?zM(this,T,Me):this.processLeaveNode(T)}return w.length=0,Al.forEach(I=>{this.players.push(I),I.onDone(()=>{I.destroy();let T=this.players.indexOf(I);this.players.splice(T,1)}),I.play()}),Al}afterFlush(t){this._flushFns.push(t)}afterFlushAnimationsDone(t){this._whenQuietFns.push(t)}_getPreviousPlayers(t,n,r,i,o){let s=[];if(n){let a=this.playersByQueriedElement.get(t);a&&(s=a)}else{let a=this.playersByElement.get(t);if(a){let l=!o||o==Ki;a.forEach(c=>{c.queued||!l&&c.triggerName!=i||s.push(c)})}}return(r||i)&&(s=s.filter(a=>!(r&&r!=a.namespaceId||i&&i!=a.triggerName))),s}_beforeAnimationBuild(t,n,r){let i=n.triggerName,o=n.element,s=n.isRemovalTransition?void 0:t,a=n.isRemovalTransition?void 0:i;for(let l of n.timelines){let c=l.element,u=c!==o,d=et(r,c,[]);this._getPreviousPlayers(c,u,s,a,n.toState).forEach(f=>{let m=f.getRealPlayer();m.beforeDestroy&&m.beforeDestroy(),f.destroy(),d.push(f)})}fr(o,n.fromStyles)}_buildAnimation(t,n,r,i,o,s){let a=n.triggerName,l=n.element,c=[],u=new Set,d=new Set,g=n.timelines.map(m=>{let v=m.element;u.add(v);let w=v[_t];if(w&&w.removedBeforeQueried)return new kn(m.duration,m.delay);let S=v!==l,z=GM((r.get(v)||jM).map(ae=>ae.getRealPlayer())).filter(ae=>{let Ee=ae;return Ee.element?Ee.element===v:!1}),x=o.get(v),W=s.get(v),he=oy(this._normalizer,m.keyframes,x,W),ne=this._buildPlayer(m,he,z);if(m.subTimeline&&i&&d.add(v),S){let ae=new to(t,a,v);ae.setRealPlayer(ne),c.push(ae)}return ne});c.forEach(m=>{et(this.playersByQueriedElement,m.element,[]).push(m),m.onDone(()=>UM(this.playersByQueriedElement,m.element,m))}),u.forEach(m=>ft(m,Z0));let f=Rn(g);return f.onDestroy(()=>{u.forEach(m=>ti(m,Z0)),Ft(l,n.toStyles)}),d.forEach(m=>{et(i,m,[]).push(f)}),f}_buildPlayer(t,n,r){return n.length>0?this.driver.animate(t.element,n,t.duration,t.delay,t.easing,r):new kn(t.duration,t.delay)}},to=class{constructor(t,n,r){this.namespaceId=t,this.triggerName=n,this.element=r,this._player=new kn,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(t){this._containsRealPlayer||(this._player=t,this._queuedCallbacks.forEach((n,r)=>{n.forEach(i=>qd(t,r,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(t.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(t){this.totalTime=t}syncPlayerEvents(t){let n=this._player;n.triggerCallback&&t.onStart(()=>n.triggerCallback("start")),t.onDone(()=>this.finish()),t.onDestroy(()=>this.destroy())}_queueEvent(t,n){et(this._queuedCallbacks,t,[]).push(n)}onDone(t){this.queued&&this._queueEvent("done",t),this._player.onDone(t)}onStart(t){this.queued&&this._queueEvent("start",t),this._player.onStart(t)}onDestroy(t){this.queued&&this._queueEvent("destroy",t),this._player.onDestroy(t)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(t){this.queued||this._player.setPosition(t)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(t){let n=this._player;n.triggerCallback&&n.triggerCallback(t)}};function UM(e,t,n){let r=e.get(t);if(r){if(r.length){let i=r.indexOf(n);r.splice(i,1)}r.length==0&&e.delete(t)}return r}function $M(e){return e??null}function Ga(e){return e&&e.nodeType===1}function HM(e){return e=="start"||e=="done"}function ty(e,t){let n=e.style.display;return e.style.display=t??"none",n}function ny(e,t,n,r,i){let o=[];n.forEach(l=>o.push(ty(l)));let s=[];r.forEach((l,c)=>{let u=new Map;l.forEach(d=>{let g=t.computeStyle(c,d,i);u.set(d,g),(!g||g.length==0)&&(c[_t]=BM,s.push(c))}),e.set(c,u)});let a=0;return n.forEach(l=>ty(l,o[a++])),s}function ry(e,t){let n=new Map;if(e.forEach(a=>n.set(a,[])),t.length==0)return n;let r=1,i=new Set(t),o=new Map;function s(a){if(!a)return r;let l=o.get(a);if(l)return l;let c=a.parentNode;return n.has(c)?l=c:i.has(c)?l=r:l=s(c),o.set(a,l),l}return t.forEach(a=>{let l=s(a);l!==r&&n.get(l).push(a)}),n}function ft(e,t){e.classList?.add(t)}function ti(e,t){e.classList?.remove(t)}function zM(e,t,n){Rn(n).onDone(()=>e.processLeaveNode(t))}function GM(e){let t=[];return my(e,t),t}function my(e,t){for(let n=0;n<e.length;n++){let r=e[n];r instanceof Qi?my(r.players,t):t.push(r)}}function qM(e,t){let n=Object.keys(e),r=Object.keys(t);if(n.length!=r.length)return!1;for(let i=0;i<n.length;i++){let o=n[i];if(!t.hasOwnProperty(o)||e[o]!==t[o])return!1}return!0}function iy(e,t,n){let r=n.get(e);if(!r)return!1;let i=t.get(e);return i?r.forEach(o=>i.add(o)):t.set(e,r),n.delete(e),!0}var ni=class{constructor(t,n,r){this._driver=n,this._normalizer=r,this._triggerCache={},this.onRemovalComplete=(i,o)=>{},this._transitionEngine=new Hd(t.body,n,r),this._timelineEngine=new Ud(t.body,n,r),this._transitionEngine.onRemovalComplete=(i,o)=>this.onRemovalComplete(i,o)}registerTrigger(t,n,r,i,o){let s=t+"-"+i,a=this._triggerCache[s];if(!a){let l=[],c=[],u=fy(this._driver,o,l,c);if(l.length)throw qE(i,l);c.length&&void 0,a=OM(i,u,this._normalizer),this._triggerCache[s]=a}this._transitionEngine.registerTrigger(n,i,a)}register(t,n){this._transitionEngine.register(t,n)}destroy(t,n){this._transitionEngine.destroy(t,n)}onInsert(t,n,r,i){this._transitionEngine.insertNode(t,n,r,i)}onRemove(t,n,r){this._transitionEngine.removeNode(t,n,r)}disableAnimations(t,n){this._transitionEngine.markElementAsDisabled(t,n)}process(t,n,r,i){if(r.charAt(0)=="@"){let[o,s]=q0(r),a=i;this._timelineEngine.command(o,n,s,a)}else this._transitionEngine.trigger(t,n,r,i)}listen(t,n,r,i,o){if(r.charAt(0)=="@"){let[s,a]=q0(r);return this._timelineEngine.listen(s,n,a,o)}return this._transitionEngine.listen(t,n,r,i,o)}flush(t=-1){this._transitionEngine.flush(t)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(t){this._transitionEngine.afterFlushAnimationsDone(t)}};function WM(e,t){let n=null,r=null;return Array.isArray(t)&&t.length?(n=Td(t[0]),t.length>1&&(r=Td(t[t.length-1]))):t instanceof Map&&(n=Td(t)),n||r?new zd(e,n,r):null}var zd=class e{static{this.initialStylesByElement=new WeakMap}constructor(t,n,r){this._element=t,this._startStyles=n,this._endStyles=r,this._state=0;let i=e.initialStylesByElement.get(t);i||e.initialStylesByElement.set(t,i=new Map),this._initialStyles=i}start(){this._state<1&&(this._startStyles&&Ft(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(Ft(this._element,this._initialStyles),this._endStyles&&(Ft(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(e.initialStylesByElement.delete(this._element),this._startStyles&&(fr(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(fr(this._element,this._endStyles),this._endStyles=null),Ft(this._element,this._initialStyles),this._state=3)}};function Td(e){let t=null;return e.forEach((n,r)=>{ZM(r)&&(t=t||new Map,t.set(r,n))}),t}function ZM(e){return e==="display"||e==="position"}var el=class{constructor(t,n,r,i){this.element=t,this.keyframes=n,this.options=r,this._specialStyles=i,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=r.duration,this._delay=r.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let t=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,t,this.options),this._finalKeyframe=t.length?t[t.length-1]:new Map;let n=()=>this._onFinish();this.domPlayer.addEventListener("finish",n),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",n)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(t){let n=[];return t.forEach(r=>{n.push(Object.fromEntries(r))}),n}_triggerWebAnimation(t,n,r){return t.animate(this._convertKeyframesToObject(n),r)}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(t=>t()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}setPosition(t){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=t*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let t=new Map;this.hasStarted()&&this._finalKeyframe.forEach((r,i)=>{i!=="offset"&&t.set(i,this._finished?r:Kd(this.element,i))}),this.currentSnapshot=t}triggerCallback(t){let n=t==="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},tl=class{validateStyleProperty(t){return!0}validateAnimatableStyleProperty(t){return!0}containsElement(t,n){return sy(t,n)}getParentElement(t){return Zd(t)}query(t,n,r){return ay(t,n,r)}computeStyle(t,n,r){return Kd(t,n)}animate(t,n,r,i,o,s=[]){let a=i==0?"both":"forwards",l={duration:r,delay:i,fill:a};o&&(l.easing=o);let c=new Map,u=s.filter(f=>f instanceof el);pM(r,i)&&u.forEach(f=>{f.currentSnapshot.forEach((m,v)=>c.set(v,m))});let d=dM(n).map(f=>new Map(f));d=gM(t,d,c);let g=WM(t,d);return new el(t,d,l,g)}};var qa="@",yy="@.disabled",nl=class{constructor(t,n,r,i){this.namespaceId=t,this.delegate=n,this.engine=r,this._onDestroy=i,this.\u0275type=0}get data(){return this.delegate.data}destroyNode(t){this.delegate.destroyNode?.(t)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(t,n){return this.delegate.createElement(t,n)}createComment(t){return this.delegate.createComment(t)}createText(t){return this.delegate.createText(t)}appendChild(t,n){this.delegate.appendChild(t,n),this.engine.onInsert(this.namespaceId,n,t,!1)}insertBefore(t,n,r,i=!0){this.delegate.insertBefore(t,n,r),this.engine.onInsert(this.namespaceId,n,t,i)}removeChild(t,n,r){this.parentNode(n)&&this.engine.onRemove(this.namespaceId,n,this.delegate)}selectRootElement(t,n){return this.delegate.selectRootElement(t,n)}parentNode(t){return this.delegate.parentNode(t)}nextSibling(t){return this.delegate.nextSibling(t)}setAttribute(t,n,r,i){this.delegate.setAttribute(t,n,r,i)}removeAttribute(t,n,r){this.delegate.removeAttribute(t,n,r)}addClass(t,n){this.delegate.addClass(t,n)}removeClass(t,n){this.delegate.removeClass(t,n)}setStyle(t,n,r,i){this.delegate.setStyle(t,n,r,i)}removeStyle(t,n,r){this.delegate.removeStyle(t,n,r)}setProperty(t,n,r){n.charAt(0)==qa&&n==yy?this.disableAnimations(t,!!r):this.delegate.setProperty(t,n,r)}setValue(t,n){this.delegate.setValue(t,n)}listen(t,n,r){return this.delegate.listen(t,n,r)}disableAnimations(t,n){this.engine.disableAnimations(t,n)}},Gd=class extends nl{constructor(t,n,r,i,o){super(n,r,i,o),this.factory=t,this.namespaceId=n}setProperty(t,n,r){n.charAt(0)==qa?n.charAt(1)=="."&&n==yy?(r=r===void 0?!0:!!r,this.disableAnimations(t,r)):this.engine.process(this.namespaceId,t,n.slice(1),r):this.delegate.setProperty(t,n,r)}listen(t,n,r){if(n.charAt(0)==qa){let i=QM(t),o=n.slice(1),s="";return o.charAt(0)!=qa&&([o,s]=YM(o)),this.engine.listen(this.namespaceId,i,o,s,a=>{let l=a._data||-1;this.factory.scheduleListenerCallback(l,r,a)})}return this.delegate.listen(t,n,r)}};function QM(e){switch(e){case"body":return document.body;case"document":return document;case"window":return window;default:return e}}function YM(e){let t=e.indexOf("."),n=e.substring(0,t),r=e.slice(t+1);return[n,r]}var rl=class{constructor(t,n,r){this.delegate=t,this.engine=n,this._zone=r,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,n.onRemovalComplete=(i,o)=>{o?.removeChild(null,i)}}createRenderer(t,n){let r="",i=this.delegate.createRenderer(t,n);if(!t||!n?.data?.animation){let c=this._rendererCache,u=c.get(i);if(!u){let d=()=>c.delete(i);u=new nl(r,i,this.engine,d),c.set(i,u)}return u}let o=n.id,s=n.id+"-"+this._currentId;this._currentId++,this.engine.register(s,t);let a=c=>{Array.isArray(c)?c.forEach(a):this.engine.registerTrigger(o,s,t,c.name,c)};return n.data.animation.forEach(a),new Gd(this,s,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(t,n,r){if(t>=0&&t<this._microtaskId){this._zone.run(()=>n(r));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(o=>{let[s,a]=o;s(a)}),this._animationCallbacksBuffer=[]})}),i.push([n,r])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}};var XM=(()=>{class e extends ni{constructor(n,r,i){super(n,r,i)}ngOnDestroy(){this.flush()}static{this.\u0275fac=function(r){return new(r||e)(E(be),E(hr),E(pr))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function JM(){return new Qa}function e3(e,t,n){return new rl(e,t,n)}var Cy=[{provide:pr,useFactory:JM},{provide:ni,useClass:XM},{provide:_n,useFactory:e3,deps:[Va,ni,J]}],vy=[{provide:hr,useFactory:()=>new tl},{provide:Au,useValue:"BrowserAnimations"},...Cy],t3=[{provide:hr,useClass:Qd},{provide:Au,useValue:"NoopAnimations"},...Cy],Dy=(()=>{class e{static withConfig(n){return{ngModule:e,providers:n.disableAnimations?t3:vy}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({providers:vy,imports:[Ba]})}}return e})();var n3=new M(""),r3=new M("");function My(e){return e!=null}function Iy(e){return ar(e)?le(e):e}function Sy(e){let t={};return e.forEach(n=>{t=n!=null?_(_({},t),n):t}),Object.keys(t).length===0?null:t}function xy(e,t){return t.map(n=>n(e))}function i3(e){return!e.validate}function Ty(e){return e.map(t=>i3(t)?t:n=>t.validate(n))}function o3(e){if(!e)return null;let t=e.filter(My);return t.length==0?null:function(n){return Sy(xy(n,t))}}function rf(e){return e!=null?o3(Ty(e)):null}function s3(e){if(!e)return null;let t=e.filter(My);return t.length==0?null:function(n){let r=xy(n,t).map(Iy);return Yl(r).pipe(R(Sy))}}function of(e){return e!=null?s3(Ty(e)):null}function wy(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function a3(e){return e._rawValidators}function l3(e){return e._rawAsyncValidators}function Jd(e){return e?Array.isArray(e)?e:[e]:[]}function ol(e,t){return Array.isArray(e)?e.includes(t):e===t}function _y(e,t){let n=Jd(t);return Jd(e).forEach(i=>{ol(n,i)||n.push(i)}),n}function by(e,t){return Jd(t).filter(n=>!ol(e,n))}var ef=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=rf(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=of(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},ao=class extends ef{get formDirective(){return null}get path(){return null}};var tf=class{constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},c3={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},_7=Q(_({},c3),{"[class.ng-submitted]":"isSubmitted"});var Ay=(()=>{class e extends tf{constructor(n){super(n)}static{this.\u0275fac=function(r){return new(r||e)(B(ao,10))}}static{this.\u0275dir=yt({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,i){r&2&&Dt("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},features:[la]})}}return e})();var no="VALID",il="INVALID",ri="PENDING",ro="DISABLED",oi=class{},sl=class extends oi{constructor(t,n){super(),this.value=t,this.source=n}},oo=class extends oi{constructor(t,n){super(),this.pristine=t,this.source=n}},so=class extends oi{constructor(t,n){super(),this.touched=t,this.source=n}},ii=class extends oi{constructor(t,n){super(),this.status=t,this.source=n}};function u3(e){return(sf(e)?e.validators:e)||null}function d3(e){return Array.isArray(e)?rf(e):e||null}function f3(e,t){return(sf(t)?t.asyncValidators:e)||null}function h3(e){return Array.isArray(e)?of(e):e||null}function sf(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function p3(e,t,n){let r=e.controls;if(!(t?Object.keys(r):r).length)throw new C(1e3,"");if(!r[n])throw new C(1001,"")}function g3(e,t,n){e._forEachChild((r,i)=>{if(n[i]===void 0)throw new C(1002,"")})}var nf=class{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=Bi(()=>this.statusReactive()),this.statusReactive=Fi(void 0),this._pristine=Bi(()=>this.pristineReactive()),this.pristineReactive=Fi(!0),this._touched=Bi(()=>this.touchedReactive()),this.touchedReactive=Fi(!1),this._events=new Ie,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return Xt(this.statusReactive)}set status(t){Xt(()=>this.statusReactive.set(t))}get valid(){return this.status===no}get invalid(){return this.status===il}get pending(){return this.status==ri}get disabled(){return this.status===ro}get enabled(){return this.status!==ro}get pristine(){return Xt(this.pristineReactive)}set pristine(t){Xt(()=>this.pristineReactive.set(t))}get dirty(){return!this.pristine}get touched(){return Xt(this.touchedReactive)}set touched(t){Xt(()=>this.touchedReactive.set(t))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(_y(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(_y(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(by(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(by(t,this._rawAsyncValidators))}hasValidator(t){return ol(this._rawValidators,t)}hasAsyncValidator(t){return ol(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(Q(_({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new so(!0,r))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new so(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(Q(_({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new oo(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new oo(!0,r))}markAsPending(t={}){this.status=ri;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new ii(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(Q(_({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=ro,this.errors=null,this._forEachChild(i=>{i.disable(Q(_({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new sl(this.value,r)),this._events.next(new ii(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(Q(_({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=no,this._forEachChild(r=>{r.enable(Q(_({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(Q(_({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===no||this.status===ri)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new sl(this.value,n)),this._events.next(new ii(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(Q(_({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?ro:no}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=ri,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let r=Iy(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(i=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(i,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,i)=>r&&r._find(i),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new ii(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new ye,this.statusChanges=new ye}_calculateStatus(){return this._allControlsDisabled()?ro:this.errors?il:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(ri)?ri:this._anyControlsHaveStatus(il)?il:no}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),i=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),i&&this._events.next(new oo(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new so(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){sf(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=d3(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=h3(this._rawAsyncValidators)}},al=class extends nf{constructor(t,n,r){super(u3(n),f3(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,r={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){g3(this,!0,t),Object.keys(t).forEach(r=>{p3(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(Object.keys(t).forEach(r=>{let i=this.controls[r];i&&i.patchValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((r,i)=>{r.reset(t?t[i]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,r)=>(t[r]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,r)=>r._syncPendingControls()?!0:n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{let r=this.controls[n];r&&t(r,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[n,r]of Object.entries(this.controls))if(this.contains(n)&&t(r))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(n,r,i)=>((r.enabled||this.disabled)&&(n[i]=r.value),n))}_reduceChildren(t,n){let r=t;return this._forEachChild((i,o)=>{r=n(r,i,o)}),r}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var af=new M("CallSetDisabledState",{providedIn:"root",factory:()=>ll}),ll="always";function m3(e,t,n=ll){Ny(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),v3(e,t),D3(e,t),C3(e,t),y3(e,t)}function Ey(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function y3(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function Ny(e,t){let n=a3(e);t.validator!==null?e.setValidators(wy(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=l3(e);t.asyncValidator!==null?e.setAsyncValidators(wy(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let i=()=>e.updateValueAndValidity();Ey(t._rawValidators,i),Ey(t._rawAsyncValidators,i)}function v3(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&Oy(e,t)})}function C3(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&Oy(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function Oy(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function D3(e,t){let n=(r,i)=>{t.valueAccessor.writeValue(r),i&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function w3(e,t){e==null,Ny(e,t)}function _3(e,t){e._syncPendingControls(),t.forEach(n=>{let r=n.control;r.updateOn==="submit"&&r._pendingChange&&(n.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}var b3={provide:ao,useExisting:Gs(()=>lf)},io=Promise.resolve(),lf=(()=>{class e extends ao{get submitted(){return Xt(this.submittedReactive)}constructor(n,r,i){super(),this.callSetDisabledState=i,this._submitted=Bi(()=>this.submittedReactive()),this.submittedReactive=Fi(!1),this._directives=new Set,this.ngSubmit=new ye,this.form=new al({},rf(n),of(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){io.then(()=>{let r=this._findContainer(n.path);n.control=r.registerControl(n.name,n.control),m3(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){io.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){io.then(()=>{let r=this._findContainer(n.path),i=new al({});w3(i,n),r.registerControl(n.name,i),i.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){io.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,r){io.then(()=>{this.form.get(n.path).setValue(r)})}setValue(n){this.control.setValue(n)}onSubmit(n){return this.submittedReactive.set(!0),_3(this.form,this._directives),this.ngSubmit.emit(n),n?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}static{this.\u0275fac=function(r){return new(r||e)(B(n3,10),B(r3,10),B(af,8))}}static{this.\u0275dir=yt({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,i){r&1&&ct("submit",function(s){return i.onSubmit(s)})("reset",function(){return i.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[Bm([b3]),la]})}}return e})();var Py=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=yt({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return e})();var E3=new M("");var ky=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({})}}return e})();var Ry=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:af,useValue:n.callSetDisabledState??ll}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({imports:[ky]})}}return e})(),Fy=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:E3,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:af,useValue:n.callSetDisabledState??ll}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({imports:[ky]})}}return e})();var L="primary",_o=Symbol("RouteTitle"),hf=class{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function di(e){return new hf(e)}function I3(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let i={};for(let o=0;o<r.length;o++){let s=r[o],a=e[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:i}}function S3(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Lt(e[n],t[n]))return!1;return!0}function Lt(e,t){let n=e?pf(e):void 0,r=t?pf(t):void 0;if(!n||!r||n.length!=r.length)return!1;let i;for(let o=0;o<n.length;o++)if(i=n[o],!Wy(e[i],t[i]))return!1;return!0}function pf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Wy(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((i,o)=>r[o]===i)}else return e===t}function Zy(e){return e.length>0?e[e.length-1]:null}function Vn(e){return Ql(e)?e:ar(e)?le(Promise.resolve(e)):N(e)}var x3={exact:Yy,subset:Ky},Qy={exact:T3,subset:A3,ignored:()=>!0};function Ly(e,t,n){return x3[n.paths](e.root,t.root,n.matrixParams)&&Qy[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function T3(e,t){return Lt(e,t)}function Yy(e,t,n){if(!mr(e.segments,t.segments)||!dl(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Yy(e.children[r],t.children[r],n))return!1;return!0}function A3(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Wy(e[n],t[n]))}function Ky(e,t,n){return Xy(e,t,t.segments,n)}function Xy(e,t,n,r){if(e.segments.length>n.length){let i=e.segments.slice(0,n.length);return!(!mr(i,n)||t.hasChildren()||!dl(i,n,r))}else if(e.segments.length===n.length){if(!mr(e.segments,n)||!dl(e.segments,n,r))return!1;for(let i in t.children)if(!e.children[i]||!Ky(e.children[i],t.children[i],r))return!1;return!0}else{let i=n.slice(0,e.segments.length),o=n.slice(e.segments.length);return!mr(e.segments,i)||!dl(e.segments,i,r)||!e.children[L]?!1:Xy(e.children[L],t,o,r)}}function dl(e,t,n){return t.every((r,i)=>Qy[n](e[i].parameters,r.parameters))}var ln=class{constructor(t=new te([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=di(this.queryParams),this._queryParamMap}toString(){return P3.serialize(this)}},te=class{constructor(t,n){this.segments=t,this.children=n,this.parent=null,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return fl(this)}},gr=class{constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=di(this.parameters),this._parameterMap}toString(){return e1(this)}};function N3(e,t){return mr(e,t)&&e.every((n,r)=>Lt(n.parameters,t[r].parameters))}function mr(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function O3(e,t){let n=[];return Object.entries(e.children).forEach(([r,i])=>{r===L&&(n=n.concat(t(i,r)))}),Object.entries(e.children).forEach(([r,i])=>{r!==L&&(n=n.concat(t(i,r)))}),n}var bo=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>new fi,providedIn:"root"})}}return e})(),fi=class{parse(t){let n=new mf(t);return new ln(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${lo(t.root,!0)}`,r=F3(t.queryParams),i=typeof t.fragment=="string"?`#${k3(t.fragment)}`:"";return`${n}${r}${i}`}},P3=new fi;function fl(e){return e.segments.map(t=>e1(t)).join("/")}function lo(e,t){if(!e.hasChildren())return fl(e);if(t){let n=e.children[L]?lo(e.children[L],!1):"",r=[];return Object.entries(e.children).forEach(([i,o])=>{i!==L&&r.push(`${i}:${lo(o,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=O3(e,(r,i)=>i===L?[lo(e.children[L],!1)]:[`${i}:${lo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[L]!=null?`${fl(e)}/${n[0]}`:`${fl(e)}/(${n.join("//")})`}}function Jy(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function cl(e){return Jy(e).replace(/%3B/gi,";")}function k3(e){return encodeURI(e)}function gf(e){return Jy(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function hl(e){return decodeURIComponent(e)}function Vy(e){return hl(e.replace(/\+/g,"%20"))}function e1(e){return`${gf(e.path)}${R3(e.parameters)}`}function R3(e){return Object.entries(e).map(([t,n])=>`;${gf(t)}=${gf(n)}`).join("")}function F3(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(i=>`${cl(n)}=${cl(i)}`).join("&"):`${cl(n)}=${cl(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var L3=/^[^\/()?;#]+/;function cf(e){let t=e.match(L3);return t?t[0]:""}var V3=/^[^\/()?;=#]+/;function j3(e){let t=e.match(V3);return t?t[0]:""}var B3=/^[^=?&#]+/;function U3(e){let t=e.match(B3);return t?t[0]:""}var $3=/^[^&#]+/;function H3(e){let t=e.match($3);return t?t[0]:""}var mf=class{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new te([],{}):new te([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[L]=new te(t,n)),r}parseSegment(){let t=cf(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new C(4009,!1);return this.capture(t),new gr(hl(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=j3(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let i=cf(this.remaining);i&&(r=i,this.capture(r))}t[hl(n)]=hl(r)}parseQueryParam(t){let n=U3(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=H3(this.remaining);s&&(r=s,this.capture(r))}let i=Vy(n),o=Vy(r);if(t.hasOwnProperty(i)){let s=t[i];Array.isArray(s)||(s=[s],t[i]=s),s.push(o)}else t[i]=o}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=cf(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new C(4010,!1);let o;r.indexOf(":")>-1?(o=r.slice(0,r.indexOf(":")),this.capture(o),this.capture(":")):t&&(o=L);let s=this.parseChildren();n[o]=Object.keys(s).length===1?s[L]:new te([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new C(4011,!1)}};function t1(e){return e.segments.length>0?new te([],{[L]:e}):e}function n1(e){let t={};for(let[r,i]of Object.entries(e.children)){let o=n1(i);if(r===L&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))t[s]=a;else(o.segments.length>0||o.hasChildren())&&(t[r]=o)}let n=new te(e.segments,t);return z3(n)}function z3(e){if(e.numberOfChildren===1&&e.children[L]){let t=e.children[L];return new te(e.segments.concat(t.segments),t.children)}return e}function yr(e){return e instanceof ln}function G3(e,t,n=null,r=null){let i=r1(e);return i1(i,t,n,r)}function r1(e){let t;function n(o){let s={};for(let l of o.children){let c=n(l);s[l.outlet]=c}let a=new te(o.url,s);return o===e&&(t=a),a}let r=n(e.root),i=t1(r);return t??i}function i1(e,t,n,r){let i=e;for(;i.parent;)i=i.parent;if(t.length===0)return uf(i,i,i,n,r);let o=q3(t);if(o.toRoot())return uf(i,i,new te([],{}),n,r);let s=W3(o,i,e),a=s.processChildren?fo(s.segmentGroup,s.index,o.commands):s1(s.segmentGroup,s.index,o.commands);return uf(i,s.segmentGroup,a,n,r)}function pl(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function go(e){return typeof e=="object"&&e!=null&&e.outlets}function uf(e,t,n,r,i){let o={};r&&Object.entries(r).forEach(([l,c])=>{o[l]=Array.isArray(c)?c.map(u=>`${u}`):`${c}`});let s;e===t?s=n:s=o1(e,t,n);let a=t1(n1(s));return new ln(a,o,i)}function o1(e,t,n){let r={};return Object.entries(e.children).forEach(([i,o])=>{o===t?r[i]=n:r[i]=o1(o,t,n)}),new te(e.segments,r)}var gl=class{constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&pl(r[0]))throw new C(4003,!1);let i=r.find(go);if(i&&i!==Zy(r))throw new C(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function q3(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new gl(!0,0,e);let t=0,n=!1,r=e.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([l,c])=>{a[l]=typeof c=="string"?c.split("/"):c}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,l)=>{l==0&&a==="."||(l==0&&a===""?n=!0:a===".."?t++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new gl(n,t,r)}var li=class{constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function W3(e,t,n){if(e.isAbsolute)return new li(t,!0,0);if(!n)return new li(t,!1,NaN);if(n.parent===null)return new li(n,!0,0);let r=pl(e.commands[0])?0:1,i=n.segments.length-1+r;return Z3(n,i,e.numberOfDoubleDots)}function Z3(e,t,n){let r=e,i=t,o=n;for(;o>i;){if(o-=i,r=r.parent,!r)throw new C(4005,!1);i=r.segments.length}return new li(r,!1,i-o)}function Q3(e){return go(e[0])?e[0].outlets:{[L]:e}}function s1(e,t,n){if(e??=new te([],{}),e.segments.length===0&&e.hasChildren())return fo(e,t,n);let r=Y3(e,t,n),i=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let o=new te(e.segments.slice(0,r.pathIndex),{});return o.children[L]=new te(e.segments.slice(r.pathIndex),e.children),fo(o,0,i)}else return r.match&&i.length===0?new te(e.segments,{}):r.match&&!e.hasChildren()?yf(e,t,n):r.match?fo(e,0,i):yf(e,t,n)}function fo(e,t,n){if(n.length===0)return new te(e.segments,{});{let r=Q3(n),i={};if(Object.keys(r).some(o=>o!==L)&&e.children[L]&&e.numberOfChildren===1&&e.children[L].segments.length===0){let o=fo(e.children[L],t,n);return new te(e.segments,o.children)}return Object.entries(r).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=s1(e.children[o],t,s))}),Object.entries(e.children).forEach(([o,s])=>{r[o]===void 0&&(i[o]=s)}),new te(e.segments,i)}}function Y3(e,t,n){let r=0,i=t,o={match:!1,pathIndex:0,commandIndex:0};for(;i<e.segments.length;){if(r>=n.length)return o;let s=e.segments[i],a=n[r];if(go(a))break;let l=`${a}`,c=r<n.length-1?n[r+1]:null;if(i>0&&l===void 0)break;if(l&&c&&typeof c=="object"&&c.outlets===void 0){if(!By(l,c,s))return o;r+=2}else{if(!By(l,{},s))return o;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function yf(e,t,n){let r=e.segments.slice(0,t),i=0;for(;i<n.length;){let o=n[i];if(go(o)){let l=K3(o.outlets);return new te(r,l)}if(i===0&&pl(n[0])){let l=e.segments[t];r.push(new gr(l.path,jy(n[0]))),i++;continue}let s=go(o)?o.outlets[L]:`${o}`,a=i<n.length-1?n[i+1]:null;s&&a&&pl(a)?(r.push(new gr(s,jy(a))),i+=2):(r.push(new gr(s,{})),i++)}return new te(r,{})}function K3(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=yf(new te([],{}),0,r))}),t}function jy(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function By(e,t,n){return e==n.path&&Lt(t,n.parameters)}var ho="imperative",xe=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(xe||{}),ht=class{constructor(t,n){this.id=t,this.url=n}},hi=class extends ht{constructor(t,n,r="imperative",i=null){super(t,n),this.type=xe.NavigationStart,this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Vt=class extends ht{constructor(t,n,r){super(t,n),this.urlAfterRedirects=r,this.type=xe.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},nt=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(nt||{}),ml=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(ml||{}),an=class extends ht{constructor(t,n,r,i){super(t,n),this.reason=r,this.code=i,this.type=xe.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Fn=class extends ht{constructor(t,n,r,i){super(t,n),this.reason=r,this.code=i,this.type=xe.NavigationSkipped}},mo=class extends ht{constructor(t,n,r,i){super(t,n),this.error=r,this.target=i,this.type=xe.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},yl=class extends ht{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=xe.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},vf=class extends ht{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=xe.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Cf=class extends ht{constructor(t,n,r,i,o){super(t,n),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=o,this.type=xe.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Df=class extends ht{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=xe.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},wf=class extends ht{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=xe.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},_f=class{constructor(t){this.route=t,this.type=xe.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},bf=class{constructor(t){this.route=t,this.type=xe.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ef=class{constructor(t){this.snapshot=t,this.type=xe.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Mf=class{constructor(t){this.snapshot=t,this.type=xe.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},If=class{constructor(t){this.snapshot=t,this.type=xe.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Sf=class{constructor(t){this.snapshot=t,this.type=xe.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},vl=class{constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r,this.type=xe.Scroll}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},yo=class{},pi=class{constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function X3(e,t){return e.providers&&!e._injector&&(e._injector=ca(e.providers,t,`Route: ${e.path}`)),e._injector??t}function bt(e){return e.outlet||L}function J3(e,t){let n=e.filter(r=>bt(r)===t);return n.push(...e.filter(r=>bt(r)!==t)),n}function Eo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var xf=class{get injector(){return Eo(this.route?.snapshot)??this.rootInjector}set injector(t){}constructor(t){this.rootInjector=t,this.outlet=null,this.route=null,this.children=new Mo(this.rootInjector),this.attachRef=null}},Mo=(()=>{class e{constructor(n){this.rootInjector=n,this.contexts=new Map}onChildOutletCreated(n,r){let i=this.getOrCreateContext(n);i.outlet=r,this.contexts.set(n,i)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new xf(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static{this.\u0275fac=function(r){return new(r||e)(E(Fe))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Cl=class{constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Tf(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Tf(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=Af(t,this._root);return n.length<2?[]:n[n.length-2].children.map(i=>i.value).filter(i=>i!==t)}pathFromRoot(t){return Af(t,this._root).map(n=>n.value)}};function Tf(e,t){if(e===t.value)return t;for(let n of t.children){let r=Tf(e,n);if(r)return r}return null}function Af(e,t){if(e===t.value)return[t];for(let n of t.children){let r=Af(e,n);if(r.length)return r.unshift(t),r}return[]}var tt=class{constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function ai(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Dl=class extends Cl{constructor(t,n){super(t),this.snapshot=n,jf(this,t)}toString(){return this.snapshot.toString()}};function a1(e){let t=e5(e),n=new De([new gr("",{})]),r=new De({}),i=new De({}),o=new De({}),s=new De(""),a=new vr(n,r,o,s,i,L,e,t.root);return a.snapshot=t.root,new Dl(new tt(a,[]),t)}function e5(e){let t={},n={},r={},i="",o=new ci([],t,r,i,n,L,e,null,{});return new _l("",new tt(o,[]))}var vr=class{constructor(t,n,r,i,o,s,a,l){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=l,this.title=this.dataSubject?.pipe(R(c=>c[_o]))??N(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(R(t=>di(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(R(t=>di(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function wl(e,t,n="emptyOnly"){let r,{routeConfig:i}=e;return t!==null&&(n==="always"||i?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:_(_({},t.params),e.params),data:_(_({},t.data),e.data),resolve:_(_(_(_({},e.data),t.data),i?.data),e._resolvedData)}:r={params:_({},e.params),data:_({},e.data),resolve:_(_({},e.data),e._resolvedData??{})},i&&c1(i)&&(r.resolve[_o]=i.title),r}var ci=class{get title(){return this.data?.[_o]}constructor(t,n,r,i,o,s,a,l,c){this.url=t,this.params=n,this.queryParams=r,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=l,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=di(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=di(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},_l=class extends Cl{constructor(t,n){super(n),this.url=t,jf(this,n)}toString(){return l1(this._root)}};function jf(e,t){t.value._routerState=e,t.children.forEach(n=>jf(e,n))}function l1(e){let t=e.children.length>0?` { ${e.children.map(l1).join(", ")} } `:"";return`${e.value}${t}`}function df(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Lt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Lt(t.params,n.params)||e.paramsSubject.next(n.params),S3(t.url,n.url)||e.urlSubject.next(n.url),Lt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Nf(e,t){let n=Lt(e.params,t.params)&&N3(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Nf(e.parent,t.parent))}function c1(e){return typeof e.title=="string"||e.title===null}var Bf=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=L,this.activateEvents=new ye,this.deactivateEvents=new ye,this.attachEvents=new ye,this.detachEvents=new ye,this.parentContexts=D(Mo),this.location=D(Mn),this.changeDetector=D(Yr),this.inputBinder=D(Sl,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:i}=n.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new C(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new C(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new C(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new C(4013,!1);this._activatedRoute=n;let i=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,l=new Of(n,a,i.injector);this.activated=i.createComponent(s,{index:i.length,injector:l,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=yt({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[qr]})}}return e})(),Of=class e{__ngOutletInjector(t){return new e(this.route,this.childContexts,t)}constructor(t,n,r){this.route=t,this.childContexts=n,this.parent=r}get(t,n){return t===vr?this.route:t===Mo?this.childContexts:this.parent.get(t,n)}},Sl=new M(""),Uy=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,i=wi([r.queryParams,r.params,r.data]).pipe(Be(([o,s,a],l)=>(a=_(_(_({},o),s),a),l===0?N(a):Promise.resolve(a)))).subscribe(o=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=r0(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(n,i)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function t5(e,t,n){let r=vo(e,t._root,n?n._root:void 0);return new Dl(r,t)}function vo(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let i=n5(e,t,n);return new tt(r,i)}else{if(e.shouldAttach(t.value)){let o=e.retrieve(t.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>vo(e,a)),s}}let r=r5(t.value),i=t.children.map(o=>vo(e,o));return new tt(r,i)}}function n5(e,t,n){return t.children.map(r=>{for(let i of n.children)if(e.shouldReuseRoute(r.value,i.value.snapshot))return vo(e,r,i);return vo(e,r)})}function r5(e){return new vr(new De(e.url),new De(e.params),new De(e.queryParams),new De(e.fragment),new De(e.data),e.outlet,e.component,e)}var Co=class{constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},u1="ngNavigationCancelingError";function bl(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=yr(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,i=d1(!1,nt.Redirect);return i.url=n,i.navigationBehaviorOptions=r,i}function d1(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[u1]=!0,n.cancellationCode=t,n}function i5(e){return f1(e)&&yr(e.url)}function f1(e){return!!e&&e[u1]}var o5=(e,t,n,r)=>R(i=>(new Pf(t,i.targetRouterState,i.currentRouterState,n,r).activate(e),i)),Pf=class{constructor(t,n,r,i,o){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=o}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),df(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let i=ai(n);t.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],r),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,r)})}deactivateRoutes(t,n,r){let i=t.value,o=n?n.value:null;if(i===o)if(i.component){let s=r.getContext(i.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else o&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),i=r&&t.value.component?r.children:n,o=ai(t);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),i=r&&t.value.component?r.children:n,o=ai(t);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let i=ai(n);t.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],r),this.forwardEvent(new Sf(o.value.snapshot))}),t.children.length&&this.forwardEvent(new Mf(t.value.snapshot))}activateRoutes(t,n,r){let i=t.value,o=n?n.value:null;if(df(i),i===o)if(i.component){let s=r.getOrCreateContext(i.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(i.component){let s=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),df(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},El=class{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},ui=class{constructor(t,n){this.component=t,this.route=n}};function s5(e,t,n){let r=e._root,i=t?t._root:null;return co(r,i,n,[r.value])}function a5(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function mi(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!Tp(e)?e:t.get(e):r}function co(e,t,n,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=ai(t);return e.children.forEach(s=>{l5(s,o[s.value.outlet],n,r.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>po(a,n.getContext(s),i)),i}function l5(e,t,n,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let l=c5(s,o,o.routeConfig.runGuardsAndResolvers);l?i.canActivateChecks.push(new El(r)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?co(e,t,a?a.children:null,r,i):co(e,t,n,r,i),l&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new ui(a.outlet.component,s))}else s&&po(t,a,i),i.canActivateChecks.push(new El(r)),o.component?co(e,null,a?a.children:null,r,i):co(e,null,n,r,i);return i}function c5(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!mr(e.url,t.url);case"pathParamsOrQueryParamsChange":return!mr(e.url,t.url)||!Lt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Nf(e,t)||!Lt(e.queryParams,t.queryParams);case"paramsChange":default:return!Nf(e,t)}}function po(e,t,n){let r=ai(e),i=e.value;Object.entries(r).forEach(([o,s])=>{i.component?t?po(s,t.children.getContext(o),n):po(s,null,n):po(s,t,n)}),i.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new ui(t.outlet.component,i)):n.canDeactivateChecks.push(new ui(null,i)):n.canDeactivateChecks.push(new ui(null,i))}function Io(e){return typeof e=="function"}function u5(e){return typeof e=="boolean"}function d5(e){return e&&Io(e.canLoad)}function f5(e){return e&&Io(e.canActivate)}function h5(e){return e&&Io(e.canActivateChild)}function p5(e){return e&&Io(e.canDeactivate)}function g5(e){return e&&Io(e.canMatch)}function h1(e){return e instanceof Bt||e?.name==="EmptyError"}var ul=Symbol("INITIAL_VALUE");function gi(){return Be(e=>wi(e.map(t=>t.pipe($t(1),ec(ul)))).pipe(R(t=>{for(let n of t)if(n!==!0){if(n===ul)return ul;if(n===!1||m5(n))return n}return!0}),je(t=>t!==ul),$t(1)))}function m5(e){return yr(e)||e instanceof Co}function y5(e,t){return me(n=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=n;return s.length===0&&o.length===0?N(Q(_({},n),{guardsResult:!0})):v5(s,r,i,e).pipe(me(a=>a&&u5(a)?C5(r,o,e,t):N(a)),R(a=>Q(_({},n),{guardsResult:a})))})}function v5(e,t,n,r){return le(e).pipe(me(i=>E5(i.component,i.route,n,t,r)),Mt(i=>i!==!0,!0))}function C5(e,t,n,r){return le(t).pipe(Ut(i=>xr(w5(i.route.parent,r),D5(i.route,r),b5(e,i.path,n),_5(e,i.route,n))),Mt(i=>i!==!0,!0))}function D5(e,t){return e!==null&&t&&t(new If(e)),N(!0)}function w5(e,t){return e!==null&&t&&t(new Ef(e)),N(!0)}function _5(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return N(!0);let i=r.map(o=>os(()=>{let s=Eo(t)??n,a=mi(o,s),l=f5(a)?a.canActivate(t,e):Ke(s,()=>a(t,e));return Vn(l).pipe(Mt())}));return N(i).pipe(gi())}function b5(e,t,n){let r=t[t.length-1],o=t.slice(0,t.length-1).reverse().map(s=>a5(s)).filter(s=>s!==null).map(s=>os(()=>{let a=s.guards.map(l=>{let c=Eo(s.node)??n,u=mi(l,c),d=h5(u)?u.canActivateChild(r,e):Ke(c,()=>u(r,e));return Vn(d).pipe(Mt())});return N(a).pipe(gi())}));return N(o).pipe(gi())}function E5(e,t,n,r,i){let o=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!o||o.length===0)return N(!0);let s=o.map(a=>{let l=Eo(t)??i,c=mi(a,l),u=p5(c)?c.canDeactivate(e,t,n,r):Ke(l,()=>c(e,t,n,r));return Vn(u).pipe(Mt())});return N(s).pipe(gi())}function M5(e,t,n,r){let i=t.canLoad;if(i===void 0||i.length===0)return N(!0);let o=i.map(s=>{let a=mi(s,e),l=d5(a)?a.canLoad(t,n):Ke(e,()=>a(t,n));return Vn(l)});return N(o).pipe(gi(),p1(r))}function p1(e){return Gl(Se(t=>{if(typeof t!="boolean")throw bl(e,t)}),R(t=>t===!0))}function I5(e,t,n,r){let i=t.canMatch;if(!i||i.length===0)return N(!0);let o=i.map(s=>{let a=mi(s,e),l=g5(a)?a.canMatch(t,n):Ke(e,()=>a(t,n));return Vn(l)});return N(o).pipe(gi(),p1(r))}var Do=class{constructor(t){this.segmentGroup=t||null}},wo=class extends Error{constructor(t){super(),this.urlTree=t}};function si(e){return Ir(new Do(e))}function S5(e){return Ir(new C(4e3,!1))}function x5(e){return Ir(d1(!1,nt.GuardRejected))}var kf=class{constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],i=n.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return N(r);if(i.numberOfChildren>1||!i.children[L])return S5(`${t.redirectTo}`);i=i.children[L]}}applyRedirectCommands(t,n,r,i,o){if(typeof n!="string"){let a=n,{queryParams:l,fragment:c,routeConfig:u,url:d,outlet:g,params:f,data:m,title:v}=i,w=Ke(o,()=>a({params:f,data:m,queryParams:l,fragment:c,routeConfig:u,url:d,outlet:g,title:v}));if(w instanceof ln)throw new wo(w);n=w}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new wo(s);return s}applyRedirectCreateUrlTree(t,n,r,i){let o=this.createSegmentGroup(t,n.root,r,i);return new ln(o,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);r[i]=n[a]}else r[i]=o}),r}createSegmentGroup(t,n,r,i){let o=this.createSegments(t,n.segments,r,i),s={};return Object.entries(n.children).forEach(([a,l])=>{s[a]=this.createSegmentGroup(t,l,r,i)}),new te(o,s)}createSegments(t,n,r,i){return n.map(o=>o.path[0]===":"?this.findPosParam(t,o,i):this.findOrReturn(o,r))}findPosParam(t,n,r){let i=r[n.path.substring(1)];if(!i)throw new C(4001,!1);return i}findOrReturn(t,n){let r=0;for(let i of n){if(i.path===t.path)return n.splice(r),i;r++}return t}},Rf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function T5(e,t,n,r,i){let o=Uf(e,t,n);return o.matched?(r=X3(t,r),I5(r,t,n,i).pipe(R(s=>s===!0?o:_({},Rf)))):N(o)}function Uf(e,t,n){if(t.path==="**")return A5(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?_({},Rf):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let i=(t.matcher||I3)(n,e,t);if(!i)return _({},Rf);let o={};Object.entries(i.posParams??{}).forEach(([a,l])=>{o[a]=l.path});let s=i.consumed.length>0?_(_({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:n.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function A5(e){return{matched:!0,parameters:e.length>0?Zy(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function $y(e,t,n,r){return n.length>0&&P5(e,n,r)?{segmentGroup:new te(t,O5(r,new te(n,e.children))),slicedSegments:[]}:n.length===0&&k5(e,n,r)?{segmentGroup:new te(e.segments,N5(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new te(e.segments,e.children),slicedSegments:n}}function N5(e,t,n,r){let i={};for(let o of n)if(xl(e,t,o)&&!r[bt(o)]){let s=new te([],{});i[bt(o)]=s}return _(_({},r),i)}function O5(e,t){let n={};n[L]=t;for(let r of e)if(r.path===""&&bt(r)!==L){let i=new te([],{});n[bt(r)]=i}return n}function P5(e,t,n){return n.some(r=>xl(e,t,r)&&bt(r)!==L)}function k5(e,t,n){return n.some(r=>xl(e,t,r))}function xl(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function R5(e,t,n,r){return bt(e)!==r&&(r===L||!xl(t,n,e))?!1:Uf(t,e,n).matched}function F5(e,t,n){return t.length===0&&!e.children[n]}var Ff=class{};function L5(e,t,n,r,i,o,s="emptyOnly"){return new Lf(e,t,n,r,i,s,o).recognize()}var V5=31,Lf=class{constructor(t,n,r,i,o,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new kf(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(t){return new C(4002,`'${t.segmentGroup}'`)}recognize(){let t=$y(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(R(({children:n,rootSnapshot:r})=>{let i=new tt(r,n),o=new _l("",i),s=G3(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(t){let n=new ci([],Object.freeze({}),Object.freeze(_({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),L,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,L,n).pipe(R(r=>({children:r,rootSnapshot:n})),hn(r=>{if(r instanceof wo)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Do?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,i,o){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,o):this.processSegment(t,n,r,r.segments,i,!0,o).pipe(R(s=>s instanceof tt?[s]:[]))}processChildren(t,n,r,i){let o=[];for(let s of Object.keys(r.children))s==="primary"?o.unshift(s):o.push(s);return le(o).pipe(Ut(s=>{let a=r.children[s],l=J3(n,s);return this.processSegmentGroup(t,l,a,s,i)}),Jl((s,a)=>(s.push(...a),s)),pn(null),Xl(),me(s=>{if(s===null)return si(r);let a=g1(s);return j5(a),N(a)}))}processSegment(t,n,r,i,o,s,a){return le(n).pipe(Ut(l=>this.processSegmentAgainstRoute(l._injector??t,n,l,r,i,o,s,a).pipe(hn(c=>{if(c instanceof Do)return N(null);throw c}))),Mt(l=>!!l),hn(l=>{if(h1(l))return F5(r,i,o)?N(new Ff):si(r);throw l}))}processSegmentAgainstRoute(t,n,r,i,o,s,a,l){return R5(r,i,o,s)?r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,i,r,o,s,l):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,i,n,r,o,s,l):si(i):si(i)}expandSegmentAgainstRouteUsingRedirect(t,n,r,i,o,s,a){let{matched:l,parameters:c,consumedSegments:u,positionalParamSegments:d,remainingSegments:g}=Uf(n,i,o);if(!l)return si(n);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>V5&&(this.allowRedirects=!1));let f=new ci(o,c,Object.freeze(_({},this.urlTree.queryParams)),this.urlTree.fragment,Hy(i),bt(i),i.component??i._loadedComponent??null,i,zy(i)),m=wl(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(m.params),f.data=Object.freeze(m.data);let v=this.applyRedirects.applyRedirectCommands(u,i.redirectTo,d,f,t);return this.applyRedirects.lineralizeSegments(i,v).pipe(me(w=>this.processSegment(t,r,n,w.concat(g),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,i,o,s){let a=T5(n,r,i,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Be(l=>l.matched?(t=r._injector??t,this.getChildConfig(t,r,i).pipe(Be(({routes:c})=>{let u=r._loadedInjector??t,{parameters:d,consumedSegments:g,remainingSegments:f}=l,m=new ci(g,d,Object.freeze(_({},this.urlTree.queryParams)),this.urlTree.fragment,Hy(r),bt(r),r.component??r._loadedComponent??null,r,zy(r)),v=wl(m,s,this.paramsInheritanceStrategy);m.params=Object.freeze(v.params),m.data=Object.freeze(v.data);let{segmentGroup:w,slicedSegments:S}=$y(n,g,f,c);if(S.length===0&&w.hasChildren())return this.processChildren(u,c,w,m).pipe(R(x=>new tt(m,x)));if(c.length===0&&S.length===0)return N(new tt(m,[]));let z=bt(r)===o;return this.processSegment(u,c,w,S,z?L:o,!0,m).pipe(R(x=>new tt(m,x instanceof tt?[x]:[])))}))):si(n)))}getChildConfig(t,n,r){return n.children?N({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?N({routes:n._loadedRoutes,injector:n._loadedInjector}):M5(t,n,r,this.urlSerializer).pipe(me(i=>i?this.configLoader.loadChildren(t,n).pipe(Se(o=>{n._loadedRoutes=o.routes,n._loadedInjector=o.injector})):x5(n))):N({routes:[],injector:t})}};function j5(e){e.sort((t,n)=>t.value.outlet===L?-1:n.value.outlet===L?1:t.value.outlet.localeCompare(n.value.outlet))}function B5(e){let t=e.value.routeConfig;return t&&t.path===""}function g1(e){let t=[],n=new Set;for(let r of e){if(!B5(r)){t.push(r);continue}let i=t.find(o=>r.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...r.children),n.add(i)):t.push(r)}for(let r of n){let i=g1(r.children);t.push(new tt(r.value,i))}return t.filter(r=>!n.has(r))}function Hy(e){return e.data||{}}function zy(e){return e.resolve||{}}function U5(e,t,n,r,i,o){return me(s=>L5(e,t,n,r,s.extractedUrl,i,o).pipe(R(({state:a,tree:l})=>Q(_({},s),{targetSnapshot:a,urlAfterRedirects:l}))))}function $5(e,t){return me(n=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=n;if(!i.length)return N(n);let o=new Set(i.map(l=>l.route)),s=new Set;for(let l of o)if(!s.has(l))for(let c of m1(l))s.add(c);let a=0;return le(s).pipe(Ut(l=>o.has(l)?H5(l,r,e,t):(l.data=wl(l,l.parent,e).resolve,N(void 0))),Se(()=>a++),Tr(1),me(l=>a===s.size?N(n):Ze))})}function m1(e){let t=e.children.map(n=>m1(n)).flat();return[e,...t]}function H5(e,t,n,r){let i=e.routeConfig,o=e._resolve;return i?.title!==void 0&&!c1(i)&&(o[_o]=i.title),z5(o,e,t,r).pipe(R(s=>(e._resolvedData=s,e.data=wl(e,e.parent,n).resolve,null)))}function z5(e,t,n,r){let i=pf(e);if(i.length===0)return N({});let o={};return le(i).pipe(me(s=>G5(e[s],t,n,r).pipe(Mt(),Se(a=>{if(a instanceof Co)throw bl(new fi,a);o[s]=a}))),Tr(1),Kl(o),hn(s=>h1(s)?Ze:Ir(s)))}function G5(e,t,n,r){let i=Eo(t)??r,o=mi(e,i),s=o.resolve?o.resolve(t,n):Ke(i,()=>o(t,n));return Vn(s)}function ff(e){return Be(t=>{let n=e(t);return n?le(n).pipe(R(()=>t)):N(t)})}var y1=(()=>{class e{buildTitle(n){let r,i=n.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(o=>o.outlet===L);return r}getResolvedTitleForRoute(n){return n.data[_o]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>D(q5),providedIn:"root"})}}return e})(),q5=(()=>{class e extends y1{constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(E($0))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),So=new M("",{providedIn:"root",factory:()=>({})}),W5=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=fe({type:e,selectors:[["ng-component"]],standalone:!0,features:[Um],decls:1,vars:0,template:function(r,i){r&1&&y(0,"router-outlet")},dependencies:[Bf],encapsulation:2})}}return e})();function $f(e){let t=e.children&&e.children.map($f),n=t?Q(_({},e),{children:t}):_({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==L&&(n.component=W5),n}var Ml=new M(""),Hf=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=D(ma)}loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return N(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Vn(n.loadComponent()).pipe(R(v1),Se(o=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=o}),gn(()=>{this.componentLoaders.delete(n)})),i=new Mr(r,()=>new Ie).pipe(Er());return this.componentLoaders.set(n,i),i}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return N({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let o=Z5(r,this.compiler,n,this.onLoadEndListener).pipe(gn(()=>{this.childrenLoaders.delete(r)})),s=new Mr(o,()=>new Ie).pipe(Er());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Z5(e,t,n,r){return Vn(e.loadChildren()).pipe(R(v1),me(i=>i instanceof Ai||Array.isArray(i)?N(i):le(t.compileModuleAsync(i))),R(i=>{r&&r(e);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(n).injector,s=o.get(Ml,[],{optional:!0,self:!0}).flat()),{routes:s.map($f),injector:o}}))}function Q5(e){return e&&typeof e=="object"&&"default"in e}function v1(e){return Q5(e)?e.default:e}var zf=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>D(Y5),providedIn:"root"})}}return e})(),Y5=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),C1=new M(""),D1=new M("");function K5(e,t,n){let r=e.get(D1),i=e.get(be);return e.get(J).runOutsideAngular(()=>{if(!i.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(c=>setTimeout(c));let o,s=new Promise(c=>{o=c}),a=i.startViewTransition(()=>(o(),X5(e))),{onViewTransitionCreated:l}=r;return l&&Ke(e,()=>l({transition:a,from:t,to:n})),s})}function X5(e){return new Promise(t=>{Hu({read:()=>setTimeout(t)},{injector:e})})}var J5=new M(""),Gf=(()=>{class e{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new Ie,this.transitionAbortSubject=new Ie,this.configLoader=D(Hf),this.environmentInjector=D(Fe),this.urlSerializer=D(bo),this.rootContexts=D(Mo),this.location=D(Jr),this.inputBindingEnabled=D(Sl,{optional:!0})!==null,this.titleStrategy=D(y1),this.options=D(So,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=D(zf),this.createViewTransition=D(C1,{optional:!0}),this.navigationErrorHandler=D(J5,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>N(void 0),this.rootComponentType=null;let n=i=>this.events.next(new _f(i)),r=i=>this.events.next(new bf(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(Q(_(_({},this.transitions.value),n),{id:r}))}setupNavigations(n,r,i){return this.transitions=new De({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:ho,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(je(o=>o.id!==0),R(o=>Q(_({},o),{extractedUrl:this.urlHandlingStrategy.extract(o.rawUrl)})),Be(o=>{let s=!1,a=!1;return N(o).pipe(Be(l=>{if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",nt.SupersededByNewNavigation),Ze;this.currentTransition=o,this.currentNavigation={id:l.id,initialUrl:l.rawUrl,extractedUrl:l.extractedUrl,targetBrowserUrl:typeof l.extras.browserUrl=="string"?this.urlSerializer.parse(l.extras.browserUrl):l.extras.browserUrl,trigger:l.source,extras:l.extras,previousNavigation:this.lastSuccessfulNavigation?Q(_({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let c=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),u=l.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!c&&u!=="reload"){let d="";return this.events.next(new Fn(l.id,this.urlSerializer.serialize(l.rawUrl),d,ml.IgnoredSameUrlNavigation)),l.resolve(!1),Ze}if(this.urlHandlingStrategy.shouldProcessUrl(l.rawUrl))return N(l).pipe(Be(d=>{let g=this.transitions?.getValue();return this.events.next(new hi(d.id,this.urlSerializer.serialize(d.extractedUrl),d.source,d.restoredState)),g!==this.transitions?.getValue()?Ze:Promise.resolve(d)}),U5(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Se(d=>{o.targetSnapshot=d.targetSnapshot,o.urlAfterRedirects=d.urlAfterRedirects,this.currentNavigation=Q(_({},this.currentNavigation),{finalUrl:d.urlAfterRedirects});let g=new yl(d.id,this.urlSerializer.serialize(d.extractedUrl),this.urlSerializer.serialize(d.urlAfterRedirects),d.targetSnapshot);this.events.next(g)}));if(c&&this.urlHandlingStrategy.shouldProcessUrl(l.currentRawUrl)){let{id:d,extractedUrl:g,source:f,restoredState:m,extras:v}=l,w=new hi(d,this.urlSerializer.serialize(g),f,m);this.events.next(w);let S=a1(this.rootComponentType).snapshot;return this.currentTransition=o=Q(_({},l),{targetSnapshot:S,urlAfterRedirects:g,extras:Q(_({},v),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=g,N(o)}else{let d="";return this.events.next(new Fn(l.id,this.urlSerializer.serialize(l.extractedUrl),d,ml.IgnoredByUrlHandlingStrategy)),l.resolve(!1),Ze}}),Se(l=>{let c=new vf(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(c)}),R(l=>(this.currentTransition=o=Q(_({},l),{guards:s5(l.targetSnapshot,l.currentSnapshot,this.rootContexts)}),o)),y5(this.environmentInjector,l=>this.events.next(l)),Se(l=>{if(o.guardsResult=l.guardsResult,l.guardsResult&&typeof l.guardsResult!="boolean")throw bl(this.urlSerializer,l.guardsResult);let c=new Cf(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot,!!l.guardsResult);this.events.next(c)}),je(l=>l.guardsResult?!0:(this.cancelNavigationTransition(l,"",nt.GuardRejected),!1)),ff(l=>{if(l.guards.canActivateChecks.length)return N(l).pipe(Se(c=>{let u=new Df(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}),Be(c=>{let u=!1;return N(c).pipe($5(this.paramsInheritanceStrategy,this.environmentInjector),Se({next:()=>u=!0,complete:()=>{u||this.cancelNavigationTransition(c,"",nt.NoDataFromResolver)}}))}),Se(c=>{let u=new wf(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}))}),ff(l=>{let c=u=>{let d=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(u.routeConfig).pipe(Se(g=>{u.component=g}),R(()=>{})));for(let g of u.children)d.push(...c(g));return d};return wi(c(l.targetSnapshot.root)).pipe(pn(null),$t(1))}),ff(()=>this.afterPreactivation()),Be(()=>{let{currentSnapshot:l,targetSnapshot:c}=o,u=this.createViewTransition?.(this.environmentInjector,l.root,c.root);return u?le(u).pipe(R(()=>o)):N(o)}),R(l=>{let c=t5(n.routeReuseStrategy,l.targetSnapshot,l.currentRouterState);return this.currentTransition=o=Q(_({},l),{targetRouterState:c}),this.currentNavigation.targetRouterState=c,o}),Se(()=>{this.events.next(new yo)}),o5(this.rootContexts,n.routeReuseStrategy,l=>this.events.next(l),this.inputBindingEnabled),$t(1),Se({next:l=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Vt(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects))),this.titleStrategy?.updateTitle(l.targetRouterState.snapshot),l.resolve(!0)},complete:()=>{s=!0}}),tc(this.transitionAbortSubject.pipe(Se(l=>{throw l}))),gn(()=>{!s&&!a&&this.cancelNavigationTransition(o,"",nt.SupersededByNewNavigation),this.currentTransition?.id===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),hn(l=>{if(a=!0,f1(l))this.events.next(new an(o.id,this.urlSerializer.serialize(o.extractedUrl),l.message,l.cancellationCode)),i5(l)?this.events.next(new pi(l.url,l.navigationBehaviorOptions)):o.resolve(!1);else{let c=new mo(o.id,this.urlSerializer.serialize(o.extractedUrl),l,o.targetSnapshot??void 0);try{let u=Ke(this.environmentInjector,()=>this.navigationErrorHandler?.(c));if(u instanceof Co){let{message:d,cancellationCode:g}=bl(this.urlSerializer,u);this.events.next(new an(o.id,this.urlSerializer.serialize(o.extractedUrl),d,g)),this.events.next(new pi(u.redirectTo,u.navigationBehaviorOptions))}else{this.events.next(c);let d=n.errorHandler(l);o.resolve(!!d)}}catch(u){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(u)}}return Ze}))}))}cancelNavigationTransition(n,r,i){let o=new an(n.id,this.urlSerializer.serialize(n.extractedUrl),r,i);this.events.next(o),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function e4(e){return e!==ho}var t4=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>D(n4),providedIn:"root"})}}return e})(),Vf=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},n4=(()=>{class e extends Vf{static{this.\u0275fac=(()=>{let n;return function(i){return(n||(n=ta(e)))(i||e)}})()}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),w1=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>D(r4),providedIn:"root"})}}return e})(),r4=(()=>{class e extends w1{constructor(){super(...arguments),this.location=D(Jr),this.urlSerializer=D(bo),this.options=D(So,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=D(zf),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new ln,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=a1(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&n(r.url,r.state)})}handleRouterEvent(n,r){if(n instanceof hi)this.stateMemento=this.createStateMemento();else if(n instanceof Fn)this.rawUrlTree=r.initialUrl;else if(n instanceof yl){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??i,r)}}else n instanceof yo?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):n instanceof an&&(n.code===nt.GuardRejected||n.code===nt.NoDataFromResolver)?this.restoreHistory(r):n instanceof mo?this.restoreHistory(r,!0):n instanceof Vt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,r){let i=n instanceof ln?this.urlSerializer.serialize(n):n;if(this.location.isCurrentPathEqualTo(i)||r.extras.replaceUrl){let o=this.browserPageId,s=_(_({},r.extras.state),this.generateNgRouterState(r.id,o));this.location.replaceState(i,"",s)}else{let o=_(_({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(i,"",o)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.currentUrlTree===n.finalUrl&&o===0&&(this.resetState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static{this.\u0275fac=(()=>{let n;return function(i){return(n||(n=ta(e)))(i||e)}})()}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),uo=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(uo||{});function _1(e,t){e.events.pipe(je(n=>n instanceof Vt||n instanceof an||n instanceof mo||n instanceof Fn),R(n=>n instanceof Vt||n instanceof Fn?uo.COMPLETE:(n instanceof an?n.code===nt.Redirect||n.code===nt.SupersededByNewNavigation:!1)?uo.REDIRECTING:uo.FAILED),je(n=>n!==uo.REDIRECTING),$t(1)).subscribe(()=>{t()})}function i4(e){throw e}var o4={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},s4={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ln=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=D(da),this.stateManager=D(w1),this.options=D(So,{optional:!0})||{},this.pendingTasks=D(Zt),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=D(Gf),this.urlSerializer=D(bo),this.location=D(Jr),this.urlHandlingStrategy=D(zf),this._events=new Ie,this.errorHandler=this.options.errorHandler||i4,this.navigated=!1,this.routeReuseStrategy=D(t4),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=D(Ml,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!D(Sl,{optional:!0}),this.eventsSubscription=new ce,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(r,o),r instanceof an&&r.code!==nt.Redirect&&r.code!==nt.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Vt)this.navigated=!0;else if(r instanceof pi){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),l=_({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||e4(i.source)},s);this.scheduleNavigation(a,ho,null,l,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}l4(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),ho,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(n,"popstate",r)},0)})}navigateToSyncWithBrowser(n,r,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let l=_({},i);delete l.navigationId,delete l.\u0275routerPageId,Object.keys(l).length!==0&&(o.state=l)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map($f),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:l}=r,c=l?this.currentUrlTree.fragment:s,u=null;switch(a){case"merge":u=_(_({},this.currentUrlTree.queryParams),o);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=o||null}u!==null&&(u=this.removeEmptyProps(u));let d;try{let g=i?i.snapshot:this.routerState.snapshot.root;d=r1(g)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return i1(d,n,u,c??null)}navigateByUrl(n,r={skipLocationChange:!1}){let i=yr(n)?n:this.parseUrl(n),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,ho,null,r)}navigate(n,r={skipLocationChange:!1}){return a4(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let i;if(r===!0?i=_({},o4):r===!1?i=_({},s4):i=r,yr(n))return Ly(this.currentUrlTree,n,i);let o=this.parseUrl(n);return Ly(this.currentUrlTree,o,i)}removeEmptyProps(n){return Object.entries(n).reduce((r,[i,o])=>(o!=null&&(r[i]=o),r),{})}scheduleNavigation(n,r,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,l,c;s?(a=s.resolve,l=s.reject,c=s.promise):c=new Promise((d,g)=>{a=d,l=g});let u=this.pendingTasks.add();return _1(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:o,resolve:a,reject:l,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(d=>Promise.reject(d))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function a4(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new C(4008,!1)}function l4(e){return!(e instanceof yo)&&!(e instanceof pi)}var b1=(()=>{class e{constructor(n,r,i,o,s,a){this.router=n,this.route=r,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a,this.href=null,this.onChanges=new Ie,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1,this.routerLinkInput=null;let l=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=l==="a"||l==="area",this.isAnchorElement?this.subscription=n.events.subscribe(c=>{c instanceof Vt&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(yr(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,i,o,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||i||o||s||typeof this.target=="string"&&this.target!="_self"))return!0;let l={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,l),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:Qg(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let i=this.renderer,o=this.el.nativeElement;r!==null?i.setAttribute(o,n,r):i.removeAttribute(o,n)}get urlTree(){return this.routerLinkInput===null?null:yr(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(r){return new(r||e)(B(Ln),B(vr),Iu("tabindex"),B(Zr),B(at),B(nn))}}static{this.\u0275dir=yt({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&ct("click",function(s){return i.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Qr("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Kr],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Kr],replaceUrl:[2,"replaceUrl","replaceUrl",Kr],routerLink:"routerLink"},standalone:!0,features:[qu,qr]})}}return e})();var Il=class{};var c4=(()=>{class e{constructor(n,r,i,o,s){this.router=n,this.injector=i,this.preloadingStrategy=o,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(je(n=>n instanceof Vt),Ut(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let i=[];for(let o of r){o.providers&&!o._injector&&(o._injector=ca(o.providers,n,`Route: ${o.path}`));let s=o._injector??n,a=o._loadedInjector??s;(o.loadChildren&&!o._loadedRoutes&&o.canLoad===void 0||o.loadComponent&&!o._loadedComponent)&&i.push(this.preloadConfig(s,o)),(o.children||o._loadedRoutes)&&i.push(this.processRoutes(a,o.children??o._loadedRoutes))}return le(i).pipe(Sr())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let i;r.loadChildren&&r.canLoad===void 0?i=this.loader.loadChildren(n,r):i=N(null);let o=i.pipe(me(s=>s===null?N(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return le([o,s]).pipe(Sr())}else return o})}static{this.\u0275fac=function(r){return new(r||e)(E(Ln),E(ma),E(Fe),E(Il),E(Hf))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),E1=new M(""),u4=(()=>{class e{constructor(n,r,i,o,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=i,this.zone=o,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof hi?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof Vt?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof Fn&&n.code===ml.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof vl&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new vl(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(r){lm()}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function d4(e){return e.routerState.root}function xo(e,t){return{\u0275kind:e,\u0275providers:t}}function f4(){let e=D(Ye);return t=>{let n=e.get(xn);if(t!==n.components[0])return;let r=e.get(Ln),i=e.get(M1);e.get(qf)===1&&r.initialNavigation(),e.get(I1,null,V.Optional)?.setUpPreloading(),e.get(E1,null,V.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var M1=new M("",{factory:()=>new Ie}),qf=new M("",{providedIn:"root",factory:()=>1});function h4(){return xo(2,[{provide:qf,useValue:0},{provide:pa,multi:!0,deps:[Ye],useFactory:t=>{let n=t.get(f0,Promise.resolve());return()=>n.then(()=>new Promise(r=>{let i=t.get(Ln),o=t.get(M1);_1(i,()=>{r(!0)}),t.get(Gf).afterPreactivation=()=>(r(!0),o.closed?N(void 0):o),i.initialNavigation()}))}}])}function p4(){return xo(3,[{provide:pa,multi:!0,useFactory:()=>{let t=D(Ln);return()=>{t.setUpLocationChangeListener()}}},{provide:qf,useValue:2}])}var I1=new M("");function g4(e){return xo(0,[{provide:I1,useExisting:c4},{provide:Il,useExisting:e}])}function m4(){return xo(8,[Uy,{provide:Sl,useExisting:Uy}])}function y4(e){let t=[{provide:C1,useValue:K5},{provide:D1,useValue:_({skipNextTransition:!!e?.skipInitialTransition},e)}];return xo(9,t)}var Gy=new M("ROUTER_FORROOT_GUARD"),v4=[Jr,{provide:bo,useClass:fi},Ln,Mo,{provide:vr,useFactory:d4,deps:[Ln]},Hf,[]],Wf=(()=>{class e{constructor(n){}static forRoot(n,r){return{ngModule:e,providers:[v4,[],{provide:Ml,multi:!0,useValue:n},{provide:Gy,useFactory:_4,deps:[[Ln,new Zs,new uu]]},{provide:So,useValue:r||{}},r?.useHash?D4():w4(),C4(),r?.preloadingStrategy?g4(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?b4(r):[],r?.bindToComponentInputs?m4().\u0275providers:[],r?.enableViewTransitions?y4().\u0275providers:[],E4()]}}static forChild(n){return{ngModule:e,providers:[{provide:Ml,multi:!0,useValue:n}]}}static{this.\u0275fac=function(r){return new(r||e)(E(Gy,8))}}static{this.\u0275mod=_e({type:e})}static{this.\u0275inj=we({})}}return e})();function C4(){return{provide:E1,useFactory:()=>{let e=D(D0),t=D(J),n=D(So),r=D(Gf),i=D(bo);return n.scrollOffset&&e.setOffset(n.scrollOffset),new u4(i,r,e,t,n)}}}function D4(){return{provide:nn,useClass:p0}}function w4(){return{provide:nn,useClass:ld}}function _4(e){return"guarded"}function b4(e){return[e.initialNavigation==="disabled"?p4().\u0275providers:[],e.initialNavigation==="enabledBlocking"?h4().\u0275providers:[]]}var qy=new M("");function E4(){return[{provide:qy,useFactory:f4},{provide:ga,multi:!0,useExisting:qy}]}var x1=(()=>{class e{http;about={name:"",description:"",photo:"",email:"",portfolioUrl:""};constructor(n){this.http=n}ngOnInit(){this.getAboutData()}getAboutData(){this.http.get("https://portflio-backend-uiv7.onrender.com/api/about").subscribe(n=>{this.about=n},n=>{console.error("Error fetching About data:",n)})}static \u0275fac=function(r){return new(r||e)(B(on))};static \u0275cmp=fe({type:e,selectors:[["app-about"]],decls:20,vars:1,consts:[["id","about-me",1,"about-me","transition-colors","duration-300"],[1,"about-me-header"],[1,"about-me-title","theme-text-primary","transition-colors","duration-300"],[1,"about-me-layout"],[1,"spline-left-wrapper"],["src",Zg`https://my.spline.design/molang3dcopy-WdvJb5OqkYqVTP1prRjNjqcv/`,"frameborder","0","allowfullscreen",""],[1,"content-right"],[1,"about-me-container"],[1,"about-me-flex-container"],[1,"about-me-content"],[1,"text"],["href","https://flowcv.com/resume/8hm00kwrls","target","_blank"],[1,"cta"],["width","15px","height","10px","viewBox","0 0 13 10"],["d","M1,5 L11,5"],["points","8 1 12 5 8 9"]],template:function(r,i){r&1&&(h(0,"section",0)(1,"div",1)(2,"div",2),P(3," About "),p()(),h(4,"div",3)(5,"div",4),y(6,"iframe",5),p(),h(7,"div",6)(8,"div",7)(9,"div",8)(10,"div",9)(11,"div",10),P(12),p(),h(13,"a",11)(14,"button",12)(15,"span"),P(16,"Resume"),p(),Ct(),h(17,"svg",13),y(18,"path",14)(19,"polyline",15),p()()()()()()()()()),r&2&&($(12),Sn(" ",i.about.description||"Loading description..."," "))},styles:['.about-me[_ngcontent-%COMP%]{position:relative;overflow:hidden;min-height:100vh;background:transparent;transition:background .3s ease}.dark[_nghost-%COMP%]   .about-me[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .about-me[_ngcontent-%COMP%]{background:transparent}.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]{position:relative;z-index:2}@media (max-width: 960px){.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]{padding-bottom:60px}}.about-me-header[_ngcontent-%COMP%]{position:relative;z-index:10;padding:40px 0 30px;text-align:center}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:4rem;font-weight:800;background:linear-gradient(135deg,var(--primary-pink),var(--accent-pink));-webkit-background-clip:text;background-clip:text;-webkit-text-fill-color:transparent;line-height:1.1;margin:0;letter-spacing:-.02em;position:relative;transition:all .3s ease}.dark[_nghost-%COMP%]   .about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-pink),var(--accent-pink));-webkit-background-clip:text;background-clip:text;-webkit-text-fill-color:transparent}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-10px;left:50%;transform:translate(-50%);width:80px;height:4px;background:linear-gradient(90deg,var(--primary-pink),var(--accent-pink));border-radius:2px}@media (max-width: 768px){.about-me-header[_ngcontent-%COMP%]{padding:30px 0 20px}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:3rem}}@media (max-width: 500px){.about-me-header[_ngcontent-%COMP%]{padding:25px 0 15px}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:2.5rem}}.about-me-flex-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:40px;text-align:right;padding:20px}@media (max-width: 768px){.about-me-flex-container[_ngcontent-%COMP%]{gap:30px;padding:15px}}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]{gap:25px;padding:10px}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-end;gap:40px;width:100%;max-width:900px;background:#ffffffb3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:20px;padding:40px;box-shadow:0 8px 32px #db27771a,0 0 0 1px #fff3;border:1px solid var(--border-pink);transition:all .3s ease}.dark[_nghost-%COMP%]   .about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{background:#1e1e1ee6;border:1px solid var(--border-default);box-shadow:0 8px 32px #00000080,0 0 0 1px #f472b61a}@media (max-width: 768px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{gap:30px;max-width:100%;padding:30px;border-radius:15px}}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{padding:20px;gap:25px}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:var(--text-secondary);font-weight:500;font-size:1.25rem;line-height:1.8;text-align:right;max-width:100%;margin:0;letter-spacing:.01em;position:relative;transition:color .3s ease}.dark[_nghost-%COMP%]   .about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:var(--text-on-bg);opacity:.9}@media (max-width: 768px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.7}}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-size:1rem;line-height:1.6}}.cta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:relative;font-size:1.125rem;font-weight:600;letter-spacing:.02em;color:#fff;text-shadow:0 1px 2px rgba(0,0,0,.1);z-index:2}.cta[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{position:relative;margin-left:12px;fill:none;stroke-linecap:round;stroke-linejoin:round;stroke:#fff;stroke-width:2.5;transform:translate(-3px);transition:all .4s cubic-bezier(.4,0,.2,1);z-index:2}.cta[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%]{transform:translate(3px);stroke-width:3}@media (max-width: 768px){.cta[_ngcontent-%COMP%]{padding:16px 35px}.cta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 500px){.cta[_ngcontent-%COMP%]{padding:14px 30px}.cta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.9rem}}.cta[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center;padding:18px 40px;background:linear-gradient(135deg,var(--primary-pink),var(--accent-pink));border:none;border-radius:50px;cursor:pointer;transition:all .4s cubic-bezier(.4,0,.2,1);text-decoration:none;box-shadow:0 8px 25px #db27774d,0 0 0 1px #ffffff1a;overflow:hidden}.dark[_nghost-%COMP%]   .cta[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .cta[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-pink),var(--accent-pink));box-shadow:0 8px 25px #f472b666,0 0 0 1px #f472b633}.dark[_nghost-%COMP%]   .cta[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .cta[_ngcontent-%COMP%]:hover{box-shadow:0 12px 35px #f472b680,0 0 0 1px #f472b64d}.cta[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .6s ease}.cta[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,var(--accent-pink),var(--primary-pink));transform:translateY(-3px) scale(1.02);box-shadow:0 12px 35px #db277766,0 0 0 1px #fff3}.cta[_ngcontent-%COMP%]:hover:before{left:100%}.cta[_ngcontent-%COMP%]:active{transform:translateY(-1px) scale(.98)}.about-me-layout[_ngcontent-%COMP%]{display:flex;min-height:100vh;align-items:center;position:relative}.content-right[_ngcontent-%COMP%]{flex:1;padding:40px;display:flex;align-items:center;justify-content:flex-end;min-height:100vh;order:2;z-index:2}.spline-left-wrapper[_ngcontent-%COMP%]{flex:0 0 50%;height:100vh;position:relative;order:1;z-index:1}.spline-left-wrapper[_ngcontent-%COMP%]   iframe[_ngcontent-%COMP%]{width:100%;height:100%;border:none;display:block;border-radius:0 20px 20px 0}@media (max-width: 1024px){.about-me-layout[_ngcontent-%COMP%]{flex-direction:column;min-height:100vh}.spline-left-wrapper[_ngcontent-%COMP%]{flex:none;width:100%;height:400px;order:1}.spline-left-wrapper[_ngcontent-%COMP%]   iframe[_ngcontent-%COMP%]{border-radius:0}.content-right[_ngcontent-%COMP%]{padding:40px 30px;min-height:60vh;order:2}}@media (max-width: 768px){.spline-left-wrapper[_ngcontent-%COMP%]{height:350px}.content-right[_ngcontent-%COMP%]{padding:30px 20px;min-height:50vh}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{padding:25px}}@media (max-width: 480px){.spline-left-wrapper[_ngcontent-%COMP%]{height:300px}.content-right[_ngcontent-%COMP%]{padding:20px 15px;min-height:40vh}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{padding:20px;border-radius:12px}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:2rem}}@media (prefers-reduced-motion: reduce){.cta[_ngcontent-%COMP%], .cta[_ngcontent-%COMP%]:before, .cta[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{transition:none}}.about-me[_ngcontent-%COMP%]{scroll-behavior:smooth}']})}return e})();var Tl=(()=>{class e{STORAGE_KEY="darkMode";darkModeSubject=new De(!1);constructor(){this.initializeDarkMode()}get isDarkMode$(){return this.darkModeSubject.asObservable()}get isDarkMode(){return this.darkModeSubject.value}initializeDarkMode(){let n=localStorage.getItem(this.STORAGE_KEY);if(n!==null)this.setDarkMode(n==="true");else{let r=window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches;this.setDarkMode(r)}window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",r=>{localStorage.getItem(this.STORAGE_KEY)===null&&this.setDarkMode(r.matches)})}toggleDarkMode(){this.setDarkMode(!this.isDarkMode)}setDarkMode(n){this.darkModeSubject.next(n),this.updateDocumentClass(n),this.savePreference(n)}updateDocumentClass(n){let r=document.documentElement;n?r.classList.add("dark"):r.classList.remove("dark")}savePreference(n){localStorage.setItem(this.STORAGE_KEY,n.toString())}clearPreference(){localStorage.removeItem(this.STORAGE_KEY);let n=window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches;this.setDarkMode(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var A1=(()=>{class e{elementRef;darkModeService;vantaEffect;subscription=new ce;isDarkMode=!1;originalSettings={speedLimit:9,separation:58,cohesion:40};hoverSettings={speedLimit:25,separation:250,cohesion:10};lightThemeColors={backgroundColor:16171257,color1:11275825,color2:6750463,backgroundAlpha:0};darkThemeColors={backgroundColor:0,color1:16027569,color2:16758465,backgroundAlpha:.9};constructor(n,r){this.elementRef=n,this.darkModeService=r}ngOnInit(){this.subscription.add(this.darkModeService.isDarkMode$.subscribe(n=>{this.isDarkMode=n,this.updateVantaTheme()})),this.initializeVanta(),this.setupHoverEffects()}initializeVanta(){let n=this.isDarkMode?this.darkThemeColors:this.lightThemeColors;this.vantaEffect=VANTA.BIRDS({el:"#vanta-birds",mouseControls:!0,touchControls:!0,gyroControls:!1,minHeight:200,minWidth:200,scale:1,scaleMobile:1,backgroundColor:n.backgroundColor,color1:n.color1,color2:n.color2,birdSize:1.2,wingSpan:32,speedLimit:this.originalSettings.speedLimit,separation:this.originalSettings.separation,alignment:77,cohesion:this.originalSettings.cohesion,quantity:4,backgroundAlpha:n.backgroundAlpha})}updateVantaTheme(){if(this.vantaEffect){let n=this.isDarkMode?this.darkThemeColors:this.lightThemeColors;this.vantaEffect.setOptions({backgroundColor:n.backgroundColor,color1:n.color1,color2:n.color2,backgroundAlpha:n.backgroundAlpha})}}setupHoverEffects(){let n=this.elementRef.nativeElement.querySelector("#vanta-birds");n&&(n.addEventListener("mouseenter",()=>{this.onHoverStart()}),n.addEventListener("mouseleave",()=>{this.onHoverEnd()}))}onHoverStart(){this.vantaEffect&&this.vantaEffect.setOptions({speedLimit:this.hoverSettings.speedLimit,separation:this.hoverSettings.separation,cohesion:this.hoverSettings.cohesion})}onHoverEnd(){this.vantaEffect&&this.vantaEffect.setOptions({speedLimit:this.originalSettings.speedLimit,separation:this.originalSettings.separation,cohesion:this.originalSettings.cohesion})}ngOnDestroy(){this.subscription.unsubscribe(),this.vantaEffect&&this.vantaEffect.destroy()}static \u0275fac=function(r){return new(r||e)(B(at),B(Tl))};static \u0275cmp=fe({type:e,selectors:[["app-first"]],decls:7,vars:0,consts:[["id","vanta-birds",1,"hero-section"],[1,"profile-pic"],["src","images/roro.jpg","alt","Roaa Ayman"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"div",1),y(2,"img",2),p(),h(3,"h1"),P(4,"Roaa Ayman"),p(),h(5,"p"),P(6,"Front-End Developer | React & Angular | 3 Months of Hands-on Experience"),p()())},styles:[".hero-section[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;width:100vw;max-width:100%;color:var(--text-primary);overflow:hidden;cursor:pointer;transition:all .3s ease;background:transparent}.dark[_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]{background:transparent;color:var(--text-on-bg)}.hero-section[_ngcontent-%COMP%]:hover{filter:brightness(1.05)}.profile-pic[_ngcontent-%COMP%]{border-radius:50%;overflow:hidden;width:150px;height:150px;margin-bottom:20px;border:4px solid var(--border-pink);box-shadow:0 0 20px var(--shadow-pink);transition:all .3s ease}.profile-pic[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 0 30px var(--shadow-pink)}.dark[_nghost-%COMP%]   .profile-pic[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .profile-pic[_ngcontent-%COMP%]{border-color:var(--primary-pink);box-shadow:0 0 25px var(--shadow-pink)}.dark[_nghost-%COMP%]   .profile-pic[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .profile-pic[_ngcontent-%COMP%]:hover{box-shadow:0 0 35px var(--shadow-pink);border-color:var(--primary-pink-light)}.profile-pic[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:10px;color:var(--text-primary);text-shadow:2px 2px 4px var(--shadow-pink);transition:all .3s ease;font-weight:700;text-align:center}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:20px;color:var(--text-secondary);transition:all .3s ease;text-align:center;max-width:600px;line-height:1.6}.dark[_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:var(--primary-pink);text-shadow:2px 2px 8px var(--shadow-pink)}.dark[_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-on-bg)}@media (max-width: 768px){.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;padding:0 20px}}.social-icons[_ngcontent-%COMP%]{display:flex;gap:15px}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--text-primary);font-size:1.5rem;transition:color .3s}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--accent-pink)}.dark[_nghost-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--text-primary)}.dark[_nghost-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--accent-pink);filter:drop-shadow(0 0 8px var(--shadow-pink))}.hero-section[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{transition:color .3s ease,background-color .3s ease,border-color .3s ease,box-shadow .3s ease,text-shadow .3s ease}.hero-section[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s ease-out forwards;opacity:0;transform:translateY(30px)}.hero-section[_ngcontent-%COMP%]   .profile-pic[_ngcontent-%COMP%]{animation-delay:.2s}.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{animation-delay:.4s}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{animation-delay:.6s}.hero-section[_ngcontent-%COMP%]   .social-icons[_ngcontent-%COMP%]{animation-delay:.8s}@keyframes _ngcontent-%COMP%_fadeInUp{to{opacity:1;transform:translateY(0)}}@media (prefers-reduced-motion: reduce){.hero-section[_ngcontent-%COMP%]   *[_ngcontent-%COMP%], .hero-section[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{animation:none;transition:none}.hero-section[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{opacity:1;transform:none}}"]})}return e})();var M4=[{path:"",component:A1},{path:"about",component:x1},{path:"**",redirectTo:""}],N1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=_e({type:e});static \u0275inj=we({imports:[Wf.forRoot(M4,{scrollPositionRestoration:"enabled",anchorScrolling:"enabled"}),Wf]})}return e})();function I4(e,t){e&1&&(Ct(),h(0,"svg",4),y(1,"path",5),p())}function S4(e,t){e&1&&(Ct(),h(0,"svg",6),y(1,"path",7),p())}var O1=(()=>{class e{darkModeService;isDarkMode=!1;subscription=new ce;constructor(n){this.darkModeService=n}ngOnInit(){this.subscription.add(this.darkModeService.isDarkMode$.subscribe(n=>{this.isDarkMode=n}))}ngOnDestroy(){this.subscription.unsubscribe()}onToggle(){this.darkModeService.toggleDarkMode()}static \u0275fac=function(r){return new(r||e)(B(Tl))};static \u0275cmp=fe({type:e,selectors:[["app-dark-mode-toggle"]],decls:4,vars:12,consts:[[1,"relative","inline-flex","items-center","justify-center","w-12","h-6","rounded-full","transition-all","duration-300","ease-in-out","focus:outline-none","focus:ring-2","focus:ring-pink-500","focus:ring-offset-2","dark:focus:ring-offset-white",3,"click"],[1,"absolute","left-1","top-1","w-4","h-4","bg-white","rounded-full","shadow-md","transform","transition-transform","duration-300","ease-in-out","flex","items-center","justify-center"],["class","w-3 h-3 text-yellow-500 transition-opacity duration-200","fill","currentColor","viewBox","0 0 20 20",4,"ngIf"],["class","w-3 h-3 text-gray-700 transition-opacity duration-200","fill","currentColor","viewBox","0 0 20 20",4,"ngIf"],["fill","currentColor","viewBox","0 0 20 20",1,"w-3","h-3","text-yellow-500","transition-opacity","duration-200"],["fill-rule","evenodd","d","M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z","clip-rule","evenodd"],["fill","currentColor","viewBox","0 0 20 20",1,"w-3","h-3","text-gray-700","transition-opacity","duration-200"],["d","M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"]],template:function(r,i){r&1&&(h(0,"button",0),ct("click",function(){return i.onToggle()}),h(1,"span",1),Ne(2,I4,2,0,"svg",2)(3,S4,2,0,"svg",3),p()()),r&2&&(Dt("bg-pink-600",i.isDarkMode)("bg-pink-100",!i.isDarkMode),Qr("aria-label",i.isDarkMode?"Switch to light mode":"Switch to dark mode")("aria-pressed",i.isDarkMode),$(),Dt("translate-x-6",i.isDarkMode)("translate-x-0",!i.isDarkMode),$(),ie("ngIf",!i.isDarkMode),$(),ie("ngIf",i.isDarkMode))},dependencies:[An],styles:[".dark-mode-toggle[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center;justify-content:center}button[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}button[_ngcontent-%COMP%]:hover{transform:scale(1.05)}button[_ngcontent-%COMP%]:active{transform:scale(.95)}button[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #bb02624d}svg[_ngcontent-%COMP%]{transition:all .2s ease-in-out}span[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%]{transform:scale(1.1)}@keyframes _ngcontent-%COMP%_slide-right{0%{transform:translate(0)}to{transform:translate(1.5rem)}}@keyframes _ngcontent-%COMP%_slide-left{0%{transform:translate(1.5rem)}to{transform:translate(0)}}button[_ngcontent-%COMP%]{z-index:10}"]})}return e})();var T4=["mobileMenuCheckbox"],P1=(()=>{class e{mobileMenuCheckbox;isMobileMenuOpen=!1;constructor(){}closeMobileMenu(){this.isMobileMenuOpen=!1,this.mobileMenuCheckbox&&(this.mobileMenuCheckbox.nativeElement.checked=!1)}toggleMobileMenu(){this.isMobileMenuOpen=!this.isMobileMenuOpen}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=fe({type:e,selectors:[["app-header"]],viewQuery:function(r,i){if(r&1&&In(T4,5),r&2){let o;Yt(o=Kt())&&(i.mobileMenuCheckbox=o.first)}},decls:30,vars:0,consts:[[1,"h-[70px]","sticky","top-0","z-50","border-b-2","border-pink-300","dark:border-dark-pink-border","lg:bg-transparent","bg-gradient-to-r","from-primary-pink-lightest","via-[rgba(218,149,234,0.95)]","to-rose-100","dark:bg-gradient-to-r","dark:from-dark-bg-primary","dark:via-dark-bg-secondary","dark:to-dark-bg-tertiary","px-8","flex","items-center","justify-between","transition-all","duration-300"],["type","checkbox","id","check",1,"hidden","peer"],["for","check",1,"menu","block","lg:hidden","sticky","cursor-pointer","z-50","text-gray-700","dark:text-gray-200"],["xmlns","http://www.w3.org/2000/svg","width","30","height","30","fill","currentColor",1,"bi","bi-list","transition-colors","duration-300"],["fill-rule","evenodd","d","M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"],[1,"logo"],[1,"theme-text-primary","font-bold","cursor-pointer","text-xl","transition-colors","duration-300"],[1,"nav-items","peer-checked:right-0","fixed","lg:static","top-0","right-[-250px]","h-screen","lg:h-auto","w-[250px]","lg:w-auto","flex","flex-col","lg:flex-row","justify-evenly","lg:justify-end","items-start","lg:items-center","bg-pink-400","dark:bg-dark-bg-secondary","lg:bg-transparent","dark:lg:bg-transparent","transition-all","duration-500","p-8","lg:p-0","gap-y-6","lg:gap-x-6"],[1,"flex","flex-col","lg:flex-row","gap-y-4","lg:gap-x-4","theme-text-secondary","dark:text-gray-200","text-[18px]","font-medium"],["routerLink","/",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300"],["routerLink","/about",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300"],["href","#projects",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300"],["href","#skills",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300"],["href","#experience",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300"],["href","#contact",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300"],[1,"mt-4","lg:mt-0","lg:ml-4"]],template:function(r,i){r&1&&(h(0,"nav",0),y(1,"input",1),h(2,"label",2),Ct(),h(3,"svg",3),y(4,"path",4),p()(),ea(),h(5,"div",5)(6,"h2",6),P(7,"RA"),p()(),h(8,"div",7)(9,"ul",8)(10,"li")(11,"a",9),P(12,"Home"),p()(),h(13,"li")(14,"a",10),P(15,"About"),p()(),h(16,"li")(17,"a",11),P(18,"Projects"),p()(),h(19,"li")(20,"a",12),P(21,"Skills"),p()(),h(22,"li")(23,"a",13),P(24,"Experience"),p()(),h(25,"li")(26,"a",14),P(27,"Contact"),p()()(),h(28,"div",15),y(29,"app-dark-mode-toggle"),p()()())},dependencies:[b1,O1],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box}nav[_ngcontent-%COMP%]{height:70px;background:transparent;padding:0 2rem;display:flex;justify-content:space-between;align-items:center;top:0;z-index:1000;position:sticky;border-bottom:2px solid var(--border-pink-dark)}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:none}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:700;font-size:2rem;color:var(--text-primary);cursor:pointer;margin:0 .5rem;text-shadow:2px 2px 4px rgba(0,0,0,.3);transition:all .3s ease;position:relative;z-index:1}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:before{content:"";position:absolute;width:100%;height:2px;bottom:-5px;left:0;background:linear-gradient(to right,var(--primary-pink),var(--accent-pink));transform:scaleX(0);transform-origin:left;transition:transform .3s ease}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:hover:before{transform:scaleX(1)}.nav-items[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{display:flex}.overview[_ngcontent-%COMP%]{margin-right:4rem}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:none}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style:none;margin:0 .5rem}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:var(--text-secondary);font-size:18px}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--primary-pink-dark)}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after{content:"";display:block;height:3px;background:var(--primary-pink);width:0%;transition:all ease-in-out .3s}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after{width:100%}#check[_ngcontent-%COMP%], .menu[_ngcontent-%COMP%]{display:none}@media (max-width: 750px){.nav-items[_ngcontent-%COMP%]{z-index:1000;position:fixed;top:0;height:100vh;width:250px;flex-direction:column;justify-content:space-evenly;background:var(--accent-pink);padding:2rem;right:-250px;transition:all ease-in-out .5s}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{flex-direction:column;width:auto}.overview[_ngcontent-%COMP%]{margin:0}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:inline-block;font-weight:400;text-transform:uppercase;font-size:13px;margin-bottom:1rem}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:inline-block;cursor:pointer;vertical-align:top}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:1rem 0}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:inline-block}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{margin-left:2px;transition:all ease-in-out .3s}.menu[_ngcontent-%COMP%]{display:inline-block;position:fixed;right:2.5rem;z-index:1001}#check[_ngcontent-%COMP%]:checked ~ .nav-items[_ngcontent-%COMP%]{right:0}}']})}return e})();var k1=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/projects";constructor(n){this.http=n}getProjects(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(E(on))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var O4=["cardRef"],P4=e=>({"fade-in-up":e});function k4(e,t){if(e&1&&(h(0,"span",16),P(1),p()),e&2){let n=t.$implicit;$(),Sn(" ",n," ")}}function R4(e,t){if(e&1&&(h(0,"a",19),P(1," GitHub "),p()),e&2){let n=Ge(2).$implicit;ie("href",n.githubLink,or)}}function F4(e,t){if(e&1&&(h(0,"a",19),P(1," Live Demo "),p()),e&2){let n=Ge(2).$implicit;ie("href",n.link,or)}}function L4(e,t){if(e&1&&(h(0,"div",17),Ne(1,R4,2,1,"a",18)(2,F4,2,1,"a",18),p()),e&2){let n=Ge().$implicit;$(),ie("ngIf",n.githubLink),$(),ie("ngIf",n.link)}}function V4(e,t){e&1&&(h(0,"p",20),P(1," This project is private due to company confidentiality. "),p())}function j4(e,t){if(e&1&&(h(0,"div",3,0)(2,"div",4),y(3,"img",5),p(),h(4,"div",6)(5,"h3",7),P(6),p(),h(7,"p",8),P(8),p(),h(9,"p",9),P(10),p(),h(11,"div",10)(12,"strong",11),P(13,"Skills:"),p(),h(14,"div",12),Ne(15,k4,2,1,"span",13),p()()(),Ne(16,L4,3,2,"div",14)(17,V4,2,0,"p",15),p()),e&2){let n=t.$implicit,r=t.index,i=Ge();ie("ngClass",$m(8,P4,i.isVisible[r])),$(3),ie("src",n.photo,or),$(3),ut(n.name),$(2),ut(n.title),$(2),ut(n.description),$(5),ie("ngForOf",n.skills),$(),ie("ngIf",n.githubLink||n.link),$(),ie("ngIf",!n.githubLink&&!n.link)}}var R1=(()=>{class e{apiService;projects=[];isVisible=[];cardElements;constructor(n){this.apiService=n}ngOnInit(){this.apiService.getProjects().subscribe(n=>{this.projects=n.map(r=>({photo:r.photo,name:r.name,title:r.title,description:r.description,link:r.link,githubLink:r.githubLink,skills:r.skills})),this.isVisible=new Array(this.projects.length).fill(!1)},n=>{console.error("Error fetching projects:",n)})}ngAfterViewInit(){let n=new IntersectionObserver(r=>{r.forEach(i=>{if(i.isIntersecting){let o=this.cardElements.toArray().findIndex(s=>s.nativeElement===i.target);o!==-1&&(this.isVisible[o]=!0)}})},{threshold:.2});setTimeout(()=>{this.cardElements.forEach(r=>n.observe(r.nativeElement))},500)}static \u0275fac=function(r){return new(r||e)(B(k1))};static \u0275cmp=fe({type:e,selectors:[["app-projects"]],viewQuery:function(r,i){if(r&1&&In(O4,5),r&2){let o;Yt(o=Kt())&&(i.cardElements=o)}},decls:2,vars:1,consts:[["cardRef",""],[1,"grid","grid-cols-1","sm:grid-cols-2","lg:grid-cols-3","gap-10","px-6"],["class","project-card bg-white dark:bg-dark-bg-card border border-gray-200 dark:border-dark-border rounded-2xl p-6 shadow-lg dark:shadow-dark-card flex flex-col justify-between transition-all duration-300",3,"ngClass",4,"ngFor","ngForOf"],[1,"project-card","bg-white","dark:bg-dark-bg-card","border","border-gray-200","dark:border-dark-border","rounded-2xl","p-6","shadow-lg","dark:shadow-dark-card","flex","flex-col","justify-between","transition-all","duration-300",3,"ngClass"],[1,"overflow-hidden","rounded-xl","mb-4","h-52","relative"],["alt","Project Image",1,"project-image","w-full","h-full","object-cover",3,"src"],[1,"text-gray-800","dark:text-gray-200","transition-colors","duration-300"],[1,"title-underline","text-2xl","font-bold","font-righteous","uppercase","text-gray-900","dark:text-gray-100","transition-colors","duration-300"],[1,"text-sm","font-lato","tracking-wide","text-pink-600","dark:text-pink-400","mt-1","transition-colors","duration-300"],[1,"text-sm","mt-2","text-gray-700","dark:text-gray-300","transition-colors","duration-300","normal-case"],[1,"mt-4"],[1,"text-pink-600","dark:text-pink-400","transition-colors","duration-300"],[1,"flex","flex-wrap","gap-2","mt-1"],["class","skill-tag bg-pink-100 dark:bg-dark-bg-hover text-pink-700 dark:text-pink-300 text-xs font-semibold px-2 py-1 rounded-full cursor-default transition-all duration-300",4,"ngFor","ngForOf"],["class","mt-6 flex justify-between gap-3",4,"ngIf"],["class","text-sm italic text-gray-500 dark:text-gray-400 mt-4 text-center",4,"ngIf"],[1,"skill-tag","bg-pink-100","dark:bg-dark-bg-hover","text-pink-700","dark:text-pink-300","text-xs","font-semibold","px-2","py-1","rounded-full","cursor-default","transition-all","duration-300"],[1,"mt-6","flex","justify-between","gap-3"],["target","_blank","class","project-button text-xs px-4 py-2 border border-pink-500 dark:border-pink-400 text-pink-500 dark:text-pink-400 rounded-full hover:bg-pink-500 dark:hover:bg-pink-400 hover:text-white dark:hover:text-gray-900 flex-1 text-center transition-all duration-300",3,"href",4,"ngIf"],["target","_blank",1,"project-button","text-xs","px-4","py-2","border","border-pink-500","dark:border-pink-400","text-pink-500","dark:text-pink-400","rounded-full","hover:bg-pink-500","dark:hover:bg-pink-400","hover:text-white","dark:hover:text-gray-900","flex-1","text-center","transition-all","duration-300",3,"href"],[1,"text-sm","italic","text-gray-500","dark:text-gray-400","mt-4","text-center"]],template:function(r,i){r&1&&(h(0,"div",1),Ne(1,j4,18,10,"div",2),p()),r&2&&($(),ie("ngForOf",i.projects))},dependencies:[Aa,ei,An],styles:['.project-card[_ngcontent-%COMP%]{transition:all .5s cubic-bezier(.4,0,.2,1);will-change:transform,box-shadow}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px) scale(1.02);box-shadow:0 25px 50px -12px #00000040,0 0 0 1px #ec48991a}.project-image[_ngcontent-%COMP%]{transition:transform .7s cubic-bezier(.4,0,.2,1);will-change:transform}.project-card[_ngcontent-%COMP%]:hover   .project-image[_ngcontent-%COMP%]{transform:scale(1.1)}.skill-tag[_ngcontent-%COMP%]{transition:all .2s cubic-bezier(.4,0,.2,1);will-change:transform}.skill-tag[_ngcontent-%COMP%]:hover{transform:scale(1.05) translateY(-1px);box-shadow:0 4px 8px #ec489933}.project-button[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1);will-change:transform,box-shadow}.project-button[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.project-button[_ngcontent-%COMP%]:hover:before{left:100%}.project-button[_ngcontent-%COMP%]:hover{transform:scale(1.05) translateY(-2px);box-shadow:0 8px 16px #ec48994d}.title-underline[_ngcontent-%COMP%]{position:relative;overflow:hidden}.title-underline[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-2px;left:0;width:0;height:2px;background:linear-gradient(90deg,#ec4899,#f472b6);transition:width .3s ease-out}.project-card[_ngcontent-%COMP%]:hover   .title-underline[_ngcontent-%COMP%]:after{width:100%}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out forwards}.project-card[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.project-card[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.project-card[_ngcontent-%COMP%]:nth-child(3){animation-delay:.3s}.project-card[_ngcontent-%COMP%]:nth-child(4){animation-delay:.4s}.project-card[_ngcontent-%COMP%]:nth-child(5){animation-delay:.5s}.project-card[_ngcontent-%COMP%]:nth-child(6){animation-delay:.6s}']})}return e})();var U4=(e,t)=>({"submission-message success-message":e,"submission-message error-message":t});function $4(e,t){e&1&&(Ct(),h(0,"svg",221),y(1,"path",222),p())}function H4(e,t){e&1&&(Ct(),h(0,"svg",221),y(1,"path",223),p())}function z4(e,t){if(e&1&&(h(0,"div",219),Ne(1,$4,2,0,"svg",220)(2,H4,2,0,"svg",220),h(3,"span"),P(4),p()()),e&2){let n=Ge();ie("ngClass",Hm(4,U4,n.submissionStatus==="success",n.submissionStatus==="error")),$(),ie("ngIf",n.submissionStatus==="success"),$(),ie("ngIf",n.submissionStatus==="error"),$(2),ut(n.submissionMessage)}}var F1=(()=>{class e{submissionStatus=null;submissionMessage="";onSubmit(n){n.preventDefault();let r=n.target;fetch(r.action,{method:r.method,body:new FormData(r),headers:{Accept:"application/json"}}).then(i=>{i.ok?(this.submissionStatus="success",this.submissionMessage="Message sent successfully!",r.reset()):(this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later.")}).catch(()=>{this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later."})}onInputChange(){this.submissionStatus=null,this.submissionMessage=""}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=fe({type:e,selectors:[["app-contact"]],decls:223,vars:1,consts:[[1,"contact-header","transition-colors","duration-300"],[1,"text-5xl","font-righteous","text-pink-600","dark:text-pink-400","mb-10","relative","transition-colors","duration-300"],[1,"container","d-flex","justify-content-center","align-items-center","transition-colors","duration-300"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 790 563","fill","none"],["id","Image"],["id","g14"],["id","g16"],["id","g22"],["id","path24","d","M578.06 12.9772C592.384 8.33142 607.668 7.43103 622.682 8.31278C644.252 9.57946 666.668 15.0178 682.527 29.8837C692.521 39.2526 699.149 51.6277 707.182 62.7655C730.486 95.0785 766.513 118.198 782.236 154.912C795.674 186.289 790.623 225.749 767.498 250.666C744.37 275.583 703.649 282.658 675.018 264.535C647.531 247.136 635.383 212.503 610.273 191.742C592.326 176.901 569.144 170.28 549.646 157.607C529.69 144.636 513.457 124.248 509.79 100.515C506.745 80.8173 513.744 59.4156 528.903 46.4558C543.731 33.7796 559.331 19.0536 578.06 12.9772Z","fill","#D0F6FF"],["id","g26"],["id","path28","d","M702.629 254.14C677.841 258.169 653.602 251.674 628.841 247.05C605.059 242.608 581.372 234.267 562.49 218.522C553.842 211.31 546.177 202.344 542.784 191.529C537.944 176.097 542.362 159.436 542.319 143.243C542.267 124.241 537.593 105.929 524.57 91.9138C516.642 83.3826 507.429 75.9038 501.21 66.026C488.249 45.4368 498.285 17.8695 518.578 6.24557C537.067 -4.34208 560.588 -0.151769 579.793 9.03335C598.996 18.2198 615.855 31.9082 635.139 40.9228C656.28 50.8045 679.407 54.6779 702.724 56.9022C720.556 58.6044 738.716 56.5333 753.266 67.1156C763.675 74.6877 771.032 86.0519 775.307 98.2911C783.396 121.448 781.768 148.673 778.037 172.583C775.54 188.601 770.517 204.461 761.348 217.755C750.094 234.074 732.89 245.819 714.058 251.504C710.234 252.66 706.426 253.523 702.629 254.14Z","fill","#ADE0EC"],["id","g30"],["id","path32","d","M663.601 562.578H87.0689C43.5385 528.913 13.2922 480.886 5.1219 426.023C1.72497 403.207 3.65744 376.191 22.008 362.528C50.2285 341.516 92.5784 368.009 124.46 353.325C144.998 343.869 155.119 319.297 155.332 296.439C155.544 273.583 147.922 251.523 142.217 229.409C136.51 207.295 132.749 183.417 140.459 161.935C148.169 140.454 170.87 123.025 192.716 128.727C211.437 133.614 223.318 152.833 241.257 160.133C259.931 167.732 281.608 160.819 298.184 149.256C314.758 137.694 327.949 121.87 343.441 108.858C370.638 86.0156 406.562 72.0169 441.495 77.35C476.426 82.6831 508.747 110.108 514.202 145.471C518.662 174.4 506.652 207.826 524.152 231.129C543.883 257.401 585.152 250.025 613.676 265.983C636.899 278.972 649.286 309.077 642.052 334.934C634.666 361.336 609.565 383.494 613.653 410.622C616.583 430.071 633.6 443.505 645.587 458.982C668.627 488.727 679.049 528.158 663.601 562.578Z","fill","#D0F6FF"],["id","g34"],["id","path36","d","M636.536 562.578H142.588C127.567 548.706 110.711 535.931 102.179 517.242C93.6475 498.553 93.6698 474.269 107.702 459.372C124.638 441.394 152.947 443.847 176.763 437.899C204.228 431.038 229.205 408.689 232.723 380.251C237.265 343.537 206.911 309.992 208.804 273.041C210.296 243.911 234.698 217.737 263.314 214.567C282.66 212.424 302.727 219.607 321.415 214.109C338.741 209.012 351.237 194.119 366.296 184.052C383.968 172.235 406.528 167.099 426.891 172.974C447.257 178.85 464.492 196.695 467.235 217.968C470.152 240.588 458.004 266.283 470.888 284.991C480.485 298.927 499.63 301.618 516.392 301.075C533.155 300.531 551.03 298.252 565.763 306.372C579.463 313.921 587.611 329.548 589.138 345.273C590.664 360.996 586.334 376.788 579.943 391.199C574.357 403.794 567.162 415.706 562.961 428.843C558.759 441.979 557.893 457.066 564.737 469.006C571.941 481.577 585.915 488.105 597.307 496.94C617.442 512.552 635.027 536.936 636.536 562.578Z","fill","#ADE0EC"],["id","g38"],["id","path40","d","M595.195 76.2172L623.725 149.709L684.511 114.948L595.195 76.2172Z","fill","#FAFAFA"],["id","g42"],["id","path44","d","M595.195 76.2172L651.26 133.962L666.528 125.232L595.195 76.2172Z","fill","#DADADA"],["id","g46"],["id","path48","d","M666.528 125.231L655.896 151.885L651.262 133.962L666.528 125.231Z","fill","#DADADA"],["id","g50"],["id","path52","d","M655.896 151.885L642.776 138.814L651.262 133.962L655.896 151.885Z","fill","#B2B2B2"],["id","g54"],["id","path56","d","M222.015 539.778C157.683 522.604 101.579 476.087 72.2367 415.592C60.1279 390.628 52.3612 362.908 54.182 335.155C56.0014 307.4 68.2732 279.663 90.2639 263.011C112.253 246.359 144.303 242.756 167.56 257.538C190.03 271.821 200.733 299.209 220.204 317.461C243.475 339.274 280.404 345.641 308.459 330.683C336.514 315.723 352.288 279.369 342.05 248.968C332.575 220.834 305.793 203.339 282.527 185.228C259.261 167.115 236.126 141.651 239.454 112.116C242.315 86.7319 264.382 67.653 287.628 57.7513C332.132 38.7951 389.516 47.2223 419.844 85.2787C452.476 126.224 446.202 185.954 431.486 236.425C416.769 286.896 395.069 337.985 402.391 390.086C408.475 433.375 434.97 472.304 470.109 497.688C505.247 523.075 548.365 535.649 591.441 538.326C634.426 540.999 680.569 532.908 712.364 503.476C744.158 474.044 754.899 419.157 726.78 386.108C712.226 369.003 690.497 360.328 669.604 352.466C648.708 344.604 626.907 336.377 611.765 319.807C596.621 303.236 590.753 275.553 604.995 258.181C621.492 238.058 665.44 235.858 680.982 214.969C692.069 200.069 679.116 171.364 666.529 157.269","stroke","#00C0E0","stroke-width","2.541","stroke-miterlimit","10","stroke-dasharray","7.62 7.62"],["id","g58"],["id","path60","d","M186.221 462.671C158.423 444.172 133.639 421.035 113.173 394.475C104.595 383.341 96.7115 371.5 91.5083 358.398C86.3038 345.294 83.8862 330.794 86.4431 316.909C88.2757 306.953 93.6209 296.589 103.112 293.404C110.525 290.917 118.902 293.505 125.077 298.35C131.253 303.195 135.584 310.023 139.418 316.916C154.207 343.52 163.287 372.9 174.224 401.352C179.474 415.006 185.205 428.511 192.17 441.366C195.631 447.754 199.387 453.984 203.532 459.939C207.289 465.334 214.117 471.144 216.477 476.969C211.073 481.321 191.263 466.026 186.221 462.671Z","fill","#009D9C"],["id","g62"],["id","path64","d","M107.952 308.508C121.544 366.877 153.477 420.713 197.968 460.267","stroke","#00BBBF","stroke-width","2.02","stroke-miterlimit","10"],["id","g66"],["id","path68","d","M556.282 462.962C580.155 451.221 602.114 435.493 621.004 416.609C628.922 408.693 636.362 400.145 641.81 390.319C647.257 380.493 650.64 369.27 650.028 358.018C649.587 349.946 646.41 341.19 639.223 337.682C633.608 334.942 626.717 336.117 621.339 339.307C615.961 342.497 611.841 347.447 608.109 352.504C593.705 372.014 583.539 394.316 571.997 415.691C566.459 425.947 560.553 436.037 553.736 445.484C550.349 450.177 546.746 454.716 542.861 458.995C539.341 462.875 533.349 466.761 530.891 471.124C534.727 475.129 551.952 465.092 556.282 462.962Z","fill","#009D9C"],["id","g70"],["id","path72","d","M633.861 349.129C617.182 393.899 586.452 433.173 547.233 459.836","stroke","#00BBBF","stroke-width","1.612","stroke-miterlimit","10"],["id","g74"],["id","path76","d","M198.233 424.458C213.177 349.774 197.247 269.251 155.048 206.17","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g78"],["id","path80","d","M159.471 213.554C147.424 209.56 136.887 201.07 130.331 190.079C123.775 179.087 121.256 165.687 123.366 153.024C136.148 156.495 148.154 164.541 154.962 176.037C161.771 187.536 162.465 200.493 159.471 213.554Z","fill","#11ABBA"],["id","g82"],["id","path84","d","M172.923 237.731C170.163 228.217 170.886 217.71 174.922 208.676C178.958 199.643 186.273 192.157 195.149 187.981C198.557 197.74 198.756 208.999 194.512 218.417C190.269 227.834 182.434 233.949 172.923 237.731Z","fill","#11ABBA"],["id","g86"],["id","path88","d","M173.775 236.831C166.404 230.308 156.684 226.574 146.897 226.504C137.11 226.434 127.338 230.03 119.876 236.447C127.196 243.672 137.206 248.568 147.423 248.608C157.641 248.647 166.403 243.999 173.775 236.831Z","fill","#11ABBA"],["id","g90"],["id","path92","d","M188.104 276.094C187.024 266.239 189.542 256.02 195.07 247.837C200.597 239.655 209.088 233.576 218.546 231.029C220.225 241.241 218.483 252.363 212.686 260.887C206.887 269.41 198.122 274.049 188.104 276.094Z","fill","#11ABBA"],["id","g94"],["id","path96","d","M189.099 275.358C182.962 267.634 174.033 262.24 164.408 260.443C154.782 258.647 144.542 260.463 136.091 265.464C142.057 273.87 151.07 280.459 161.124 282.301C171.179 284.145 180.606 281.115 189.099 275.358Z","fill","#11ABBA"],["id","g98"],["id","path100","d","M198.154 314.469C197.924 304.556 201.31 294.598 207.521 286.933C213.729 279.267 222.71 273.961 232.351 272.257C233.146 282.578 230.456 293.504 223.948 301.485C217.439 309.467 208.308 313.315 198.154 314.469Z","fill","#11ABBA"],["id","g102"],["id","path104","d","M199.208 313.823C193.758 305.586 185.324 299.426 175.891 296.789C166.457 294.15 156.099 295.057 147.252 299.294C152.471 308.194 160.885 315.553 170.744 318.274C180.602 320.997 190.253 318.808 199.208 313.823Z","fill","#11ABBA"],["id","g106"],["id","path108","d","M203.971 356.696C205.264 346.866 210.136 337.563 217.445 330.968C224.754 324.372 234.439 320.543 244.225 320.378C243.428 330.699 239.095 341.071 231.443 347.929C223.789 354.789 214.179 357.154 203.971 356.696Z","fill","#11ABBA"],["id","g110"],["id","path112","d","M205.112 356.224C200.99 347.23 193.605 339.817 184.689 335.725C175.775 331.635 165.404 330.9 156.012 333.694C159.806 343.307 166.988 351.901 176.31 356.142C185.632 360.381 195.5 359.74 205.112 356.224Z","fill","#11ABBA"],["id","g114"],["id","path116","d","M546.285 450.207C530.11 375.786 544.71 295.004 585.861 231.219","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g118"],["id","path120","d","M581.562 238.676C593.54 234.478 603.937 225.811 610.312 214.71C616.685 203.608 618.983 190.168 616.663 177.542C603.94 181.23 592.069 189.476 585.452 201.088C578.835 212.7 578.354 225.668 581.562 238.676Z","fill","#11ABBA"],["id","g122"],["id","path124","d","M568.512 263.078C571.114 253.518 570.219 243.024 566.033 234.06C561.85 225.096 554.412 217.737 545.469 213.71C542.22 223.525 542.208 234.787 546.607 244.131C551.006 253.476 558.939 259.457 568.512 263.078Z","fill","#11ABBA"],["id","g126"],["id","path128","d","M567.646 262.192C574.907 255.545 584.566 251.647 594.349 251.411C604.134 251.175 613.963 254.605 621.528 260.895C614.331 268.242 604.403 273.308 594.187 273.52C583.972 273.732 575.135 269.234 567.646 262.192Z","fill","#11ABBA"],["id","g130"],["id","path132","d","M553.965 301.692C554.883 291.82 552.196 281.645 546.534 273.556C540.872 265.469 532.283 259.535 522.783 257.148C521.274 267.388 523.198 278.478 529.136 286.902C535.074 295.328 543.915 299.817 553.965 301.692Z","fill","#11ABBA"],["id","g134"],["id","path136","d","M552.959 300.973C558.968 293.147 567.807 287.6 577.401 285.642C586.995 283.683 597.263 285.324 605.795 290.182C599.97 298.687 591.066 305.428 581.044 307.441C571.021 309.454 561.546 306.585 552.959 300.973Z","fill","#11ABBA"],["id","g138"],["id","path140","d","M544.55 340.232C544.617 330.317 541.066 320.416 534.731 312.857C528.396 305.299 519.329 300.144 509.661 298.606C509.036 308.939 511.905 319.818 518.546 327.687C525.186 335.556 534.379 339.25 544.55 340.232Z","fill","#11ABBA"],["id","g142"],["id","path144","d","M543.486 339.603C548.799 331.276 557.13 324.975 566.519 322.176C575.908 319.378 586.279 320.109 595.196 324.198C590.124 333.185 581.833 340.685 572.021 343.571C562.207 346.46 552.522 344.437 543.486 339.603Z","fill","#11ABBA"],["id","g146"],["id","path148","d","M539.431 382.551C537.978 372.745 532.951 363.525 525.535 357.055C518.117 350.586 508.371 346.92 498.585 346.921C499.551 357.227 504.053 367.523 511.819 374.253C519.583 380.981 529.232 383.182 539.431 382.551Z","fill","#11ABBA"],["id","g150"],["id","path152","d","M538.282 382.098C542.255 373.036 549.518 365.498 558.363 361.259C567.21 357.016 577.568 356.105 587.003 358.74C583.369 368.417 576.328 377.13 567.079 381.527C557.828 385.925 547.95 385.452 538.282 382.098Z","fill","#11ABBA"],["id","g154"],["id","path156","d","M186.615 500.321C190.696 492.791 196.119 485.823 199.682 478.076C190.178 465.849 178.777 454.862 166.819 445.23C159.004 438.931 150.847 433.032 142.419 427.531C134.688 433.762 126.957 439.994 119.225 446.225C120.579 435.351 121.356 425.888 122.482 415.574C105.313 406.143 87.2411 398.331 68.6211 392.377C64.3289 399.386 60.6691 406.825 54.8967 412.829C54.9847 404.798 54.2249 396.412 53.1469 387.893C35.9349 383.405 18.3639 380.482 0.707452 379.308C0.649609 386.531 1.06635 393.746 1.88798 400.912C6.50223 399.507 10.074 395.563 14.9604 394.821C11.7383 402.728 8.82513 411.421 4.99044 419.449C9.19717 438.521 16.3959 456.93 26.2186 473.763C34.3468 468.915 41.9636 462.248 51.7627 458.125C50.0576 473.301 50.0274 489.179 48.7351 504.527C53.8963 510.215 59.4097 515.573 65.2741 520.527C75.5977 529.245 86.9217 536.691 98.9201 542.791C101.353 533.385 103.872 524.016 109.898 516.114C116.996 529.781 124.688 541.96 131.128 555.467C157.986 563.194 186.571 564.779 214.002 559.454C218.542 558.574 222.349 551.211 223.76 546.749C225.172 542.289 224.898 537.468 224.262 532.827C222.26 518.237 216.907 504.646 209.377 492.145C201.36 494.069 193.248 496.332 186.615 500.321Z","fill","#11ABBA"],["id","g158"],["id","path160","d","M194.298 545.299C131.158 507.676 73.43 460.749 23.4922 406.451","stroke","#55CDE2","stroke-width","2.541","stroke-miterlimit","10"],["id","g162"],["id","path164","d","M559.699 515.384C555.868 510.221 551.098 505.622 547.63 500.242C553.415 490.113 560.744 480.704 568.626 472.241C573.781 466.709 579.23 461.436 584.922 456.429C591.334 460.232 597.744 464.032 604.155 467.835C602.002 459.887 600.425 452.929 598.502 445.374C610.285 436.498 622.913 428.73 636.143 422.286C640.078 427.037 643.584 432.176 648.514 436.023C647.601 430.055 647.283 423.73 647.188 417.273C659.526 412.073 672.295 407.997 685.312 405.212C686.116 410.582 686.566 415.998 686.708 421.42C683.127 420.873 680.053 418.324 676.338 418.3C679.571 423.837 682.654 429.991 686.353 435.55C685.232 450.201 681.815 464.681 676.277 478.269C669.715 475.541 663.346 471.403 655.618 469.394C658.486 480.504 660.182 492.319 662.759 503.602C659.518 508.393 655.978 512.977 652.135 517.298C645.372 524.903 637.727 531.67 629.441 537.508C626.639 530.772 623.778 524.07 618.459 518.842C614.616 529.781 610.176 539.676 606.805 550.427C587.63 559.082 566.522 563.353 545.546 562.358C542.075 562.193 538.466 557.123 536.945 553.957C535.425 550.79 535.121 547.171 535.105 543.651C535.058 532.573 537.61 521.88 541.897 511.761C548.065 512.326 554.342 513.133 559.699 515.384Z","fill","#11ABBA"],["id","g166"],["id","path168","d","M558.719 549.691C601.746 514.86 639.767 473.689 671.212 427.878","stroke","#55CDE2","stroke-width","1.91","stroke-miterlimit","10"],["id","g170"],["id","path172","d","M554.113 562.578H187.856C180.008 562.578 173.645 556.132 173.645 548.18V310.114C173.645 302.163 180.008 295.717 187.856 295.717H554.113C561.963 295.717 568.324 302.163 568.324 310.114V548.18C568.324 556.132 561.963 562.578 554.113 562.578Z","fill","#060C37"],["id","g174"],["id","path176","d","M563.719 429.147C563.719 435.866 558.342 441.314 551.71 441.314C545.078 441.314 539.701 435.866 539.701 429.147C539.701 422.427 545.078 416.981 551.71 416.981C558.342 416.981 563.719 422.427 563.719 429.147Z","fill","#111E65"],["id","g178"],["id","path180","d","M182.05 474.266C179.95 474.266 178.247 472.542 178.247 470.413V387.882C178.247 385.753 179.95 384.028 182.05 384.028C184.151 384.028 185.854 385.753 185.854 387.882V470.413C185.854 472.542 184.151 474.266 182.05 474.266Z","fill","#111E65"],["id","path182","d","M535.104 552.722H191.254V305.564H535.104V552.722Z","fill","#D8E9F5"],["id","path184","d","M535.1 322.18H191.256V305.568H535.1V322.18Z","fill","#A4B1BA"],["id","path186","d","M201.252 320.17H196.898V314.56H201.252V320.17Z","fill","#FF6044"],["id","path188","d","M206.906 320.17H202.552V310.653H206.906V320.17Z","fill","#FF6044"],["id","path190","d","M212.886 320.17H208.532V307.952H212.886V320.17Z","fill","#FF6044"],["id","g192"],["id","path194","d","M507.781 308.957V309.767C507.781 310.411 507.264 310.933 506.629 310.933H505.346C504.711 310.933 504.196 311.455 504.196 312.099V315.647C504.196 316.293 504.711 316.814 505.346 316.814H506.629C507.264 316.814 507.781 317.336 507.781 317.979V318.792C507.781 319.435 508.296 319.957 508.931 319.957H526.844C527.479 319.957 527.995 319.435 527.995 318.792V308.957C527.995 308.313 527.479 307.791 526.844 307.791H508.931C508.296 307.791 507.781 308.313 507.781 308.957Z","fill","#D8E9F5"],["id","g196"],["id","path198","d","M526.894 319.341H523.692C523.458 319.341 523.267 319.148 523.267 318.909V308.824C523.267 308.584 523.458 308.391 523.692 308.391H526.894C527.13 308.391 527.32 308.584 527.32 308.824V318.909C527.32 319.148 527.13 319.341 526.894 319.341Z","fill","#92FC28"],["id","g200"],["id","path202","d","M521.94 319.341H518.739C518.505 319.341 518.313 319.148 518.313 318.909V308.824C518.313 308.584 518.505 308.391 518.739 308.391H521.94C522.175 308.391 522.366 308.584 522.366 308.824V318.909C522.366 319.148 522.175 319.341 521.94 319.341Z","fill","#92FC28"],["id","g204"],["id","path206","d","M516.987 319.341H513.785C513.551 319.341 513.36 319.148 513.36 318.909V308.824C513.36 308.584 513.551 308.391 513.785 308.391H516.987C517.223 308.391 517.413 308.584 517.413 308.824V318.909C517.413 319.148 517.223 319.341 516.987 319.341Z","fill","#92FC28"],["id","g208"],["id","path210","d","M498.8 313.874C498.8 316.456 496.733 318.551 494.183 318.551C491.635 318.551 489.569 316.456 489.569 313.874C489.569 311.292 491.635 309.197 494.183 309.197C496.733 309.197 498.8 311.292 498.8 313.874Z","fill","#D8E9F5"],["id","path212","d","M513.36 533.681H212.999V340.836H513.36V533.681Z","fill","#C0CFDA"],["id","path214","d","M513.36 357.464H212.999V340.838H513.36V357.464Z","fill","#A4B3BC"],["id","path216","d","M507.28 373.991H310.642V366.083H507.28V373.991Z","fill","#DCEEFB"],["id","path218","d","M419.169 389.046H310.642V381.138H419.169V389.046Z","fill","#DCEEFB"],["id","path220","d","M369.032 404.104H310.642V396.196H369.032V404.104Z","fill","#DCEEFB"],["id","path222","d","M507.28 430.213H310.642V422.305H507.28V430.213Z","fill","#DCEEFB"],["id","path224","d","M419.169 445.268H310.642V437.36H419.169V445.268Z","fill","#DCEEFB"],["id","path226","d","M369.032 460.325H310.642V452.418H369.032V460.325Z","fill","#DCEEFB"],["id","path228","d","M507.28 485.114H310.642V477.206H507.28V485.114Z","fill","#DCEEFB"],["id","path230","d","M419.169 500.172H310.642V492.264H419.169V500.172Z","fill","#DCEEFB"],["id","path232","d","M369.032 515.228H310.642V507.32H369.032V515.228Z","fill","#DCEEFB"],["id","path234","d","M301.035 409.578H224.781V366.082H301.035V409.578Z","fill","#DCEEFB"],["id","g236"],["id","path238","d","M224.781 409.579L262.908 387.831L301.034 409.579H224.781Z","fill","#CADBE7"],["id","g240"],["id","path242","d","M301.034 366.082L262.908 387.83L224.781 366.082H301.034Z","fill","#CADBE7"],["id","path244","d","M301.035 465.546H224.781V422.05H301.035V465.546Z","fill","#DCEEFB"],["id","g246"],["id","path248","d","M224.781 465.546L262.908 443.798L301.034 465.546H224.781Z","fill","#CADBE7"],["id","g250"],["id","path252","d","M301.034 422.05L262.908 443.798L224.781 422.05H301.034Z","fill","#CADBE7"],["id","path254","d","M301.035 521.515H224.781V478.019H301.035V521.515Z","fill","#DCEEFB"],["id","g256"],["id","path258","d","M224.781 521.514L262.908 499.766L301.034 521.514H224.781Z","fill","#CADBE7"],["id","g260"],["id","path262","d","M301.034 478.018L262.908 499.766L224.781 478.018H301.034Z","fill","#CADBE7"],["id","g264"],["id","g282"],["id","g280","opacity","0.440002"],["id","g274","opacity","0.440002"],["id","path272","opacity","0.440002","d","M314.124 305.565L191.254 430.069V321.271L206.769 305.565H314.124Z","fill","white"],["id","g278","opacity","0.440002"],["id","path276","opacity","0.440002","d","M388.697 305.565L191.254 505.613V449.961L333.77 305.565H388.697Z","fill","white"],["id","g284"],["id","g302"],["id","g300","opacity","0.440002"],["id","g294","opacity","0.440002"],["id","path292","opacity","0.440002","d","M535.104 332.465V441.249L425.071 552.723H317.715L535.104 332.465Z","fill","white"],["id","g298","opacity","0.440002"],["id","path296","opacity","0.440002","d","M535.104 461.142V516.794L499.632 552.723H444.716L535.104 461.142Z","fill","white"],["id","envelope"],["id","g304"],["id","path306","d","M249.266 298.798L351.208 218.764C357.652 213.705 366.657 213.705 373.102 218.764L475.045 298.798V432.924H249.266V298.798Z","fill","#FF9004"],["id","path308","d","M448.926 227.706H275.382V421.076H448.926V227.706Z","fill","#FAFAFA"],["id","path310","d","M438.481 239.346H285.831V245.241H438.481V239.346Z","fill","#DCDCDC"],["id","path312","d","M415.561 251.195H285.831V257.09H415.561V251.195Z","fill","#DCDCDC"],["id","path314","d","M394.51 263.044H285.831V268.939H394.51V263.044Z","fill","#DCDCDC"],["id","path316","d","M394.51 285.792H285.831V291.688H394.51V285.792Z","fill","#DCDCDC"],["id","path318","d","M366.443 297.167H285.831V303.062H366.443V297.167Z","fill","#DCDCDC"],["id","path320","d","M442.769 321H362.156V326.896H442.769V321Z","fill","#DCDCDC"],["id","path322","d","M442.768 332.609H377.201V338.504H442.768V332.609Z","fill","#DCDCDC"],["id","g324"],["id","path326","d","M362.155 365.9L249.265 298.877V432.924L362.155 365.9Z","fill","#FFAE35"],["id","g328"],["id","path330","d","M362.156 365.9L475.045 298.877V432.924L362.156 365.9Z","fill","#FFAE35"],["id","g332"],["id","path334","d","M351.209 352.89L249.267 432.924H475.044L373.102 352.89C366.658 347.831 357.652 347.831 351.209 352.89Z","fill","#FFBF69"],["id","g348"],["id","path350","d","M185.705 159.357C185.994 158.402 185.854 157.315 185.28 156.095C184.719 154.898 183.98 154.112 183.067 153.736C182.152 153.361 181.213 153.405 180.251 153.868C179.287 154.333 178.667 155.04 178.388 155.99C178.109 156.941 178.251 158.015 178.813 159.212C179.375 160.409 180.11 161.203 181.02 161.595C181.927 161.986 182.863 161.951 183.826 161.487C184.789 161.022 185.415 160.312 185.705 159.357ZM184.018 139.899C186.987 140.019 189.648 140.858 192.003 142.415C194.358 143.972 196.169 146.103 197.439 148.81C198.376 150.805 198.868 152.668 198.915 154.398C198.964 156.13 198.62 157.627 197.886 158.892C197.151 160.158 196.083 161.127 194.682 161.803C193.522 162.361 192.412 162.597 191.351 162.51C190.29 162.423 189.34 161.997 188.499 161.234C188.332 163.679 187.01 165.499 184.538 166.691C183.247 167.313 181.88 167.543 180.435 167.382C178.991 167.222 177.639 166.671 176.378 165.728C175.116 164.786 174.101 163.494 173.331 161.853C172.56 160.212 172.207 158.602 172.27 157.021C172.334 155.441 172.761 154.037 173.555 152.812C174.35 151.588 175.402 150.658 176.716 150.026C178.642 149.097 180.458 148.996 182.169 149.723L181.404 148.093L186.755 145.514L191.517 155.66C191.851 156.368 192.222 156.816 192.63 157C193.038 157.183 193.46 157.17 193.898 156.96C195.278 156.295 195.16 154.244 193.547 150.807C192.558 148.7 191.191 147.062 189.448 145.89C187.703 144.718 185.729 144.098 183.524 144.033C181.32 143.967 179.056 144.493 176.737 145.61C174.438 146.718 172.631 148.201 171.317 150.058C170.001 151.916 169.265 153.963 169.108 156.2C168.949 158.438 169.386 160.655 170.416 162.85C171.468 165.09 172.892 166.864 174.687 168.175C176.483 169.485 178.493 170.207 180.719 170.346C182.943 170.483 185.205 169.998 187.503 168.891C189.845 167.763 191.793 166.226 193.349 164.28L196.235 167.254C195.479 168.216 194.473 169.176 193.219 170.134C191.964 171.092 190.636 171.908 189.237 172.583C186.063 174.113 182.955 174.794 179.912 174.629C176.868 174.464 174.138 173.537 171.722 171.846C169.304 170.157 167.414 167.859 166.05 164.954C164.698 162.072 164.144 159.149 164.393 156.189C164.639 153.228 165.671 150.494 167.487 147.988C169.301 145.481 171.807 143.458 175.003 141.918C178.045 140.453 181.05 139.779 184.018 139.899Z","fill","#ADE0EC"],["id","g352"],["id","path354","d","M478.281 145.979L473.499 145.088L471.809 150.637L476.591 151.528L478.281 145.979ZM483.567 146.965L481.877 152.514L486.737 153.418L485.812 158.499L480.333 157.478L478.528 163.209L473.241 162.224L475.046 156.492L470.263 155.601L468.46 161.331L463.174 160.347L464.977 154.616L460.079 153.702L461.001 148.622L466.522 149.65L468.214 144.102L463.314 143.19L464.237 138.109L469.759 139.138L471.562 133.407L476.848 134.393L475.043 140.124L479.826 141.015L481.629 135.284L486.917 136.269L485.112 142.001L490.01 142.913L489.088 147.994L483.567 146.965Z","fill","#ADE0EC"],["id","g356"],["id","path358","d","M230.094 489.727H164.645C144.782 489.727 128.679 473.412 128.679 453.286C128.679 433.159 144.782 416.844 164.645 416.844H194.128C213.99 416.844 230.094 433.159 230.094 453.286V489.727Z","fill","#FFBF69"],["id","g360"],["id","path362","d","M190.288 474.567C192.225 471.057 193.491 467.457 194.24 463.884C197.265 463.216 199.718 462.418 201.535 461.712C199.468 467.269 195.439 471.849 190.288 474.567ZM173.549 476.516C170.414 472.301 168.399 468.049 167.204 463.913C172.228 464.889 176.849 465.295 180.987 465.295C184.501 465.295 187.666 465.013 190.478 464.585C189.44 468.665 187.643 472.75 184.795 476.628C183.054 477.042 181.249 477.283 179.386 477.283C177.368 477.283 175.42 476.999 173.549 476.516ZM157.077 461.27C159.25 461.983 161.355 462.573 163.406 463.075C164.255 466.725 165.672 470.467 167.822 474.207C162.852 471.377 159.006 466.783 157.077 461.27ZM166.919 432.92C165.905 435.193 164.777 438.165 163.89 441.631C161.455 442.199 159.416 442.847 157.807 443.446C159.751 439.087 162.942 435.428 166.919 432.92ZM185.694 430.179C186.289 431.348 188.269 435.45 189.79 441.13C180.926 439.619 173.434 439.938 167.6 440.897C169.168 435.61 171.267 431.824 172.077 430.47C174.382 429.71 176.835 429.288 179.386 429.288C181.572 429.288 183.682 429.614 185.694 430.179ZM201.203 443.946C198.569 443.098 196.02 442.407 193.568 441.864C192.612 437.856 191.394 434.47 190.4 432.058C195.218 434.635 199.063 438.835 201.203 443.946ZM194.354 445.71C196.968 446.339 199.688 447.138 202.507 448.133C202.868 449.796 203.071 451.515 203.071 453.285C203.071 454.669 202.929 456.014 202.707 457.334C201.441 457.942 198.765 459.081 194.862 460.045C195.44 454.989 195.108 450.091 194.354 445.71ZM166.64 444.734C172.634 443.581 180.793 443.047 190.668 444.909C191.612 449.668 192.068 455.159 191.237 460.804C184.963 461.903 176.497 462.275 166.311 460.097C165.321 454.509 165.701 449.252 166.64 444.734ZM155.701 453.285C155.701 451.44 155.927 449.649 156.319 447.921C157.561 447.343 159.839 446.402 163.05 445.549C162.325 449.694 162.056 454.341 162.712 459.24C160.557 458.67 158.328 457.976 156.039 457.161C155.835 455.896 155.701 454.608 155.701 453.285ZM179.386 425.733C164.391 425.733 152.192 438.093 152.192 453.285C152.192 468.479 164.391 480.838 179.386 480.838C194.381 480.838 206.58 468.479 206.58 453.285C206.58 438.093 194.381 425.733 179.386 425.733Z","fill","#FAFAFA"],["id","g364"],["id","path366","d","M487.575 534.716H553.024C572.888 534.716 588.99 518.4 588.99 498.275C588.99 478.149 572.888 461.834 553.024 461.834H523.541C503.679 461.834 487.575 478.149 487.575 498.275V534.716Z","fill","#FFBF69"],["id","g368"],["id","path370","d","M565.214 487.805C565.214 477.497 549.034 468.633 538.283 477.531C527.532 468.633 511.352 477.497 511.352 487.805C511.352 487.805 507.872 508.014 538.283 522.676C568.694 508.014 565.214 487.805 565.214 487.805Z","stroke","#FAFAFA","stroke-width","3.811","stroke-miterlimit","10","stroke-linejoin","round"],["id","g372"],["id","path374","d","M466.093 53.4869C465.677 53.3258 465.259 53.1899 464.843 53.074C464.729 52.6558 464.594 52.2389 464.437 51.8207C463.767 50.1411 462.888 48.4615 461.12 46.7819C459.352 48.4615 458.474 50.1411 457.804 51.8207C457.645 52.2415 457.51 52.6638 457.395 53.0847C456.978 53.2019 456.563 53.3391 456.147 53.4989C454.489 54.1782 452.832 55.0679 451.174 56.8594C452.832 58.6509 454.489 59.5406 456.147 60.2199C456.56 60.3797 456.973 60.5156 457.384 60.6315C457.499 61.0537 457.633 61.4759 457.792 61.8982C458.46 63.5777 459.342 65.2573 461.12 66.9369C462.899 65.2573 463.781 63.5777 464.449 61.8982C464.605 61.4799 464.741 61.0617 464.855 60.6421C465.267 60.5276 465.681 60.3917 466.093 60.2319C467.751 59.5553 469.409 58.6615 471.067 56.8594C469.409 55.0573 467.751 54.1635 466.093 53.4869Z","fill","#ADE0EC"],["id","star1"],["id","path378","d","M18.666 335.315C18.2493 335.154 17.8325 335.016 17.4145 334.901C17.3001 334.484 17.166 334.067 17.0096 333.649C16.3392 331.968 15.461 330.289 13.6929 328.61C11.9247 330.289 11.0466 331.968 10.3761 333.649C10.2171 334.069 10.0816 334.492 9.96728 334.913C9.55186 335.028 9.13514 335.167 8.71972 335.327C7.06201 336.006 5.4043 336.896 3.74658 338.687C5.4043 340.479 7.06201 341.369 8.71972 342.048C9.13251 342.206 9.54398 342.342 9.95676 342.458C10.0698 342.882 10.2052 343.304 10.3643 343.725C11.0321 345.406 11.9142 347.085 13.6929 348.765C15.4715 347.085 16.3536 345.406 17.0214 343.725C17.1779 343.308 17.3133 342.89 17.4263 342.47C17.8391 342.354 18.2532 342.22 18.666 342.058C20.3237 341.383 21.9814 340.489 23.6391 338.687C21.9814 336.885 20.3237 335.991 18.666 335.315Z","fill","#ADE0EC"],["id","g380"],["id","path382","d","M500.378 253.717C499.962 253.558 499.545 253.42 499.128 253.305C499.014 252.886 498.878 252.469 498.722 252.052C498.052 250.372 497.173 248.692 495.405 247.012C493.637 248.692 492.759 250.372 492.089 252.052C491.931 252.472 491.795 252.894 491.681 253.317C491.264 253.432 490.849 253.57 490.433 253.729C488.774 254.409 487.117 255.298 485.459 257.09C487.117 258.881 488.774 259.772 490.433 260.45C490.845 260.61 491.258 260.746 491.669 260.862C491.784 261.284 491.918 261.706 492.078 262.129C492.745 263.808 493.627 265.488 495.405 267.167C497.184 265.488 498.066 263.808 498.734 262.129C498.892 261.71 499.026 261.292 499.14 260.874C499.553 260.758 499.966 260.622 500.378 260.462C502.037 259.786 503.694 258.892 505.352 257.09C503.694 255.289 502.037 254.395 500.378 253.717Z","fill","#ADE0EC"],["id","g384"],["id","path386","d","M673.413 79.5778C673.204 79.4978 672.995 79.4286 672.785 79.3713C672.729 79.1622 672.662 78.9517 672.583 78.7426C672.246 77.9008 671.806 77.059 670.921 76.2172C670.035 77.059 669.595 77.9008 669.258 78.7426C669.178 78.9544 669.112 79.1648 669.054 79.3766C668.844 79.4352 668.636 79.5032 668.429 79.5844C667.596 79.9241 666.766 80.3703 665.936 81.2693C666.766 82.1657 667.596 82.6119 668.429 82.9529C668.635 83.0328 668.84 83.1008 669.048 83.158C669.106 83.3698 669.173 83.5816 669.253 83.7947C669.587 84.6352 670.03 85.4769 670.921 86.3201C671.811 85.4769 672.254 84.6352 672.589 83.7947C672.668 83.5842 672.734 83.3738 672.792 83.1647C672.999 83.1061 673.206 83.0382 673.413 82.9596C674.244 82.6199 675.075 82.1711 675.906 81.2693C675.075 80.3649 674.244 79.9174 673.413 79.5778Z","fill","#D0F6FF"],["id","g388"],["id","path390","d","M724.621 229.528C724.413 229.448 724.204 229.379 723.994 229.321C723.936 229.112 723.87 228.902 723.791 228.694C723.455 227.851 723.014 227.009 722.128 226.167C721.244 227.009 720.803 227.851 720.467 228.694C720.387 228.904 720.32 229.115 720.262 229.327C720.053 229.385 719.845 229.453 719.636 229.534C718.805 229.874 717.974 230.32 717.145 231.219C717.974 232.116 718.805 232.562 719.636 232.903C719.842 232.983 720.049 233.051 720.256 233.108C720.314 233.32 720.38 233.532 720.46 233.745C720.795 234.585 721.238 235.427 722.128 236.27C723.02 235.427 723.461 234.585 723.797 233.745C723.877 233.534 723.943 233.324 723.999 233.115C724.208 233.056 724.415 232.988 724.621 232.91C725.453 232.57 726.284 232.121 727.113 231.219C726.284 230.315 725.453 229.867 724.621 229.528Z","fill","#D0F6FF"],["id","g392"],["id","path394","d","M722.669 226.015C722.46 225.935 722.251 225.866 722.042 225.809C721.984 225.6 721.918 225.389 721.838 225.18C721.503 224.338 721.063 223.497 720.177 222.655C719.291 223.497 718.85 224.338 718.515 225.18C718.435 225.392 718.368 225.602 718.31 225.814C718.101 225.873 717.892 225.941 717.684 226.022C716.853 226.362 716.022 226.808 715.192 227.707C716.022 228.603 716.853 229.049 717.684 229.39C717.891 229.47 718.097 229.538 718.305 229.595C718.361 229.807 718.428 230.019 718.508 230.232C718.844 231.073 719.285 231.914 720.177 232.758C721.068 231.914 721.51 231.073 721.845 230.232C721.924 230.022 721.991 229.811 722.047 229.602C722.255 229.544 722.463 229.476 722.669 229.397C723.5 229.057 724.331 228.609 725.162 227.707C724.331 226.802 723.5 226.355 722.669 226.015Z","fill","#D0F6FF"],["id","g396"],["id","path398","d","M122.37 271.837C122.161 271.756 121.952 271.688 121.742 271.63C121.686 271.421 121.619 271.211 121.54 271.002C121.203 270.16 120.763 269.318 119.877 268.476C118.991 269.318 118.551 270.16 118.215 271.002C118.135 271.213 118.068 271.424 118.01 271.636C117.801 271.694 117.594 271.762 117.385 271.842C116.554 272.183 115.723 272.629 114.892 273.527C115.723 274.425 116.554 274.871 117.385 275.212C117.591 275.291 117.797 275.36 118.005 275.417C118.062 275.629 118.129 275.841 118.209 276.052C118.544 276.894 118.986 277.736 119.877 278.578C120.768 277.736 121.211 276.894 121.545 276.052C121.624 275.843 121.691 275.633 121.748 275.422C121.955 275.365 122.163 275.297 122.37 275.217C123.2 274.878 124.031 274.43 124.862 273.527C124.031 272.624 123.2 272.176 122.37 271.837Z","fill","#ADE0EC"],["id","g400"],["id","path402","d","M30.9696 538.087C30.7606 538.007 30.5516 537.939 30.3426 537.881C30.2847 537.671 30.219 537.461 30.1401 537.252C29.8036 536.41 29.3632 535.568 28.4772 534.728C27.5911 535.568 27.1507 536.41 26.8155 537.252C26.7353 537.464 26.6683 537.674 26.6104 537.887C26.4014 537.945 26.1937 538.012 25.9847 538.094C25.1538 538.435 24.323 538.881 23.4922 539.779C24.323 540.675 25.1538 541.121 25.9847 541.462C26.1911 541.542 26.3975 541.611 26.6052 541.667C26.6617 541.88 26.7301 542.092 26.8089 542.303C27.1442 543.146 27.5859 543.988 28.4772 544.829C29.3685 543.988 29.8115 543.146 30.1454 542.303C30.2243 542.094 30.2913 541.884 30.3478 541.674C30.5555 541.615 30.7633 541.549 30.9696 541.468C31.8005 541.128 32.6313 540.68 33.4621 539.779C32.6313 538.876 31.8005 538.427 30.9696 538.087Z","fill","#ADE0EC"],["id","g404"],["id","path406","d","M384.68 138.195C384.471 138.114 384.262 138.046 384.053 137.989C383.995 137.78 383.928 137.569 383.849 137.36C383.514 136.518 383.073 135.676 382.187 134.835C381.301 135.676 380.861 136.518 380.524 137.36C380.445 137.572 380.377 137.782 380.32 137.994C380.111 138.053 379.904 138.121 379.695 138.202C378.864 138.541 378.033 138.988 377.202 139.885C378.033 140.783 378.864 141.229 379.695 141.57C379.901 141.65 380.107 141.718 380.314 141.775C380.372 141.987 380.439 142.199 380.519 142.411C380.854 143.253 381.296 144.094 382.187 144.936C383.078 144.094 383.52 143.253 383.855 142.411C383.934 142.202 384.001 141.991 384.058 141.781C384.266 141.723 384.472 141.656 384.68 141.576C385.51 141.236 386.341 140.788 387.172 139.885C386.341 138.982 385.51 138.535 384.68 138.195Z","fill","#ADE0EC"],["id","g408"],["id","path410","d","M143.253 52.4684C143.044 52.3885 142.835 52.3192 142.626 52.262C142.568 52.0528 142.501 51.8424 142.423 51.6333C142.087 50.7915 141.646 49.9497 140.76 49.1079C139.874 49.9497 139.434 50.7915 139.097 51.6333C139.019 51.8451 138.951 52.0555 138.894 52.2673C138.685 52.3259 138.477 52.3938 138.268 52.4751C137.437 52.8147 136.606 53.2609 135.775 54.1586C136.606 55.0564 137.437 55.5026 138.268 55.8436C138.474 55.9235 138.681 55.9914 138.888 56.0487C138.945 56.2605 139.012 56.4722 139.092 56.6854C139.427 57.5258 139.869 58.3676 140.76 59.2107C141.652 58.3676 142.093 57.5258 142.429 56.6854C142.507 56.4749 142.575 56.2645 142.631 56.0553C142.839 55.9967 143.045 55.9288 143.253 55.8502C144.084 55.5106 144.915 55.0617 145.745 54.1586C144.915 53.2556 144.084 52.8081 143.253 52.4684Z","fill","#ADE0EC"],["id","star4"],["id","path414","d","M659.175 279.551C658.966 279.47 658.757 279.402 658.546 279.344C658.49 279.135 658.423 278.925 658.344 278.716C658.009 277.874 657.567 277.032 656.682 276.19C655.796 277.032 655.356 277.874 655.019 278.716C654.939 278.926 654.873 279.138 654.816 279.35C654.605 279.408 654.397 279.476 654.19 279.556C653.359 279.897 652.527 280.343 651.697 281.241C652.527 282.139 653.359 282.585 654.19 282.926C654.396 283.005 654.603 283.074 654.81 283.131C654.867 283.343 654.934 283.555 655.014 283.766C655.349 284.608 655.791 285.45 656.682 286.292C657.574 285.45 658.015 284.608 658.35 283.766C658.429 283.557 658.495 283.347 658.553 283.136C658.761 283.079 658.968 283.011 659.175 282.931C660.006 282.592 660.836 282.144 661.667 281.241C660.836 280.338 660.006 279.89 659.175 279.551Z","fill","#ADE0EC"],["id","star5"],["id","path418","d","M412.477 191.341C412.268 191.26 412.059 191.192 411.85 191.133C411.793 190.924 411.727 190.715 411.647 190.506C411.311 189.664 410.871 188.822 409.985 187.98C409.099 188.822 408.659 189.664 408.323 190.506C408.243 190.718 408.176 190.928 408.118 191.14C407.909 191.197 407.7 191.266 407.492 191.346C406.662 191.687 405.831 192.133 405 193.031C405.831 193.929 406.662 194.375 407.492 194.715C407.699 194.795 407.905 194.864 408.113 194.921C408.17 195.133 408.237 195.345 408.317 195.556C408.652 196.398 409.094 197.24 409.985 198.082C410.876 197.24 411.318 196.398 411.653 195.556C411.732 195.346 411.799 195.137 411.856 194.926C412.063 194.869 412.271 194.801 412.477 194.721C413.308 194.382 414.139 193.934 414.97 193.031C414.139 192.128 413.308 191.681 412.477 191.341Z","fill","#D0F6FF"],["id","star2"],["id","path422","d","M318.495 91.4014C318.129 91.2602 317.762 91.1403 317.396 91.0391C317.295 90.6715 317.178 90.3039 317.04 89.9363C316.45 88.4605 315.678 86.9847 314.124 85.5075C312.57 86.9847 311.797 88.4605 311.208 89.9363C311.069 90.3079 310.95 90.6782 310.85 91.0484C310.483 91.151 310.117 91.2709 309.752 91.4121C308.295 92.0088 306.837 92.792 305.381 94.3663C306.837 95.9407 308.295 96.7239 309.752 97.3206C310.115 97.4604 310.476 97.5803 310.839 97.6815C310.939 98.0531 311.059 98.4234 311.198 98.795C311.786 100.272 312.56 101.749 314.124 103.225C315.687 101.749 316.463 100.272 317.049 98.795C317.187 98.4274 317.306 98.0598 317.406 97.6922C317.77 97.5896 318.132 97.4711 318.495 97.3312C319.953 96.7358 321.41 95.95 322.868 94.3663C321.41 92.7826 319.953 91.9968 318.495 91.4014Z","fill","#ADE0EC"],["id","g424"],["id","path426","d","M95.3161 198.94C94.9494 198.801 94.5826 198.679 94.2171 198.578C94.1159 198.21 93.9989 197.843 93.8609 197.475C93.2706 195.999 92.4989 194.524 90.9451 193.047C89.3912 194.524 88.6182 195.999 88.0293 197.475C87.8899 197.847 87.7703 198.217 87.6704 198.587C87.3036 198.69 86.9382 198.81 86.5727 198.951C85.1161 199.548 83.6582 200.331 82.2017 201.905C83.6582 203.48 85.1161 204.263 86.5727 204.86C86.9355 204.999 87.2971 205.119 87.6599 205.221C87.7598 205.592 87.8794 205.962 88.0188 206.334C88.6064 207.811 89.3807 209.288 90.9451 210.764C92.5081 209.288 93.2838 207.811 93.8701 206.334C94.0081 205.966 94.1264 205.599 94.2263 205.231C94.5892 205.129 94.9533 205.01 95.3161 204.87C96.774 204.275 98.2306 203.49 99.6885 201.905C98.2306 200.322 96.774 199.536 95.3161 198.94Z","fill","#ADE0EC"],["id","star3"],["id","path430","d","M567.016 163.164C566.649 163.023 566.282 162.903 565.915 162.8C565.815 162.434 565.697 162.066 565.559 161.699C564.97 160.223 564.197 158.746 562.643 157.27C561.089 158.746 560.316 160.223 559.728 161.699C559.59 162.069 559.47 162.441 559.369 162.81C559.003 162.912 558.638 163.033 558.272 163.175C556.814 163.771 555.358 164.553 553.9 166.129C555.358 167.703 556.814 168.486 558.272 169.082C558.634 169.222 558.997 169.343 559.359 169.444C559.459 169.816 559.579 170.186 559.717 170.558C560.306 172.035 561.08 173.51 562.643 174.986C564.206 173.51 564.982 172.035 565.57 170.558C565.708 170.19 565.826 169.822 565.926 169.453C566.289 169.352 566.653 169.234 567.016 169.094C568.472 168.498 569.93 167.713 571.387 166.129C569.93 164.545 568.472 163.759 567.016 163.164Z","fill","#D0F6FF"],["id","star6"],["id","path434","d","M785.486 113.408C785.119 113.267 784.752 113.147 784.385 113.045C784.285 112.678 784.167 112.31 784.03 111.943C783.44 110.467 782.667 108.99 781.113 107.514C779.559 108.99 778.786 110.467 778.198 111.943C778.059 112.314 777.94 112.685 777.839 113.055C777.473 113.157 777.108 113.277 776.742 113.418C775.284 114.015 773.828 114.798 772.37 116.373C773.828 117.947 775.284 118.73 776.742 119.327C777.104 119.467 777.467 119.587 777.829 119.688C777.929 120.06 778.049 120.43 778.187 120.801C778.776 122.279 779.55 123.756 781.113 125.231C782.676 123.756 783.452 122.279 784.04 120.801C784.178 120.434 784.296 120.066 784.396 119.697C784.759 119.596 785.123 119.477 785.486 119.338C786.942 118.742 788.4 117.956 789.857 116.373C788.4 114.789 786.942 114.003 785.486 113.408Z","fill","#D0F6FF"],["id","g436"],["id","path438","d","M556.27 45.0362C555.903 44.895 555.536 44.7752 555.169 44.6739C555.069 44.3063 554.951 43.9387 554.813 43.5711C554.224 42.0953 553.451 40.6182 551.897 39.1424C550.343 40.6182 549.57 42.0953 548.983 43.5711C548.843 43.9427 548.724 44.313 548.624 44.6833C548.257 44.7858 547.892 44.9057 547.526 45.0469C546.068 45.6436 544.612 46.4268 543.154 48.0011C544.612 49.5755 546.068 50.3587 547.526 50.9554C547.888 51.0953 548.251 51.2151 548.613 51.3164C548.713 51.688 548.833 52.0583 548.971 52.4299C549.56 53.907 550.334 55.3841 551.897 56.8599C553.46 55.3841 554.236 53.907 554.824 52.4299C554.962 52.0622 555.08 51.6946 555.18 51.327C555.543 51.2245 555.907 51.1059 556.27 50.9661C557.726 50.3707 559.184 49.5848 560.641 48.0011C559.184 46.4175 557.726 45.6316 556.27 45.0362Z","fill","#D0F6FF"],["id","contact-form","action","https://formspree.io/f/mldrlygg","method","POST",3,"submit"],[1,"title","text-center","mb-4","theme-text-primary","transition-colors","duration-300"],[1,"form-group","position-relative"],["for","name",1,"d-block"],["data-feather","user",1,"icon"],["type","text","id","name","name","name","placeholder","Name","required","",1,"form-control","form-control-lg","thick",3,"input"],["for","email",1,"d-block"],["data-feather","mail",1,"icon"],["type","email","id","email","name","email","placeholder","E-mail","required","",1,"form-control","form-control-lg","thick",3,"input"],[1,"form-group","message"],["id","message","name","message","rows","7","placeholder","Message","required","",1,"form-control","form-control-lg",3,"input"],[1,"text-center"],["type","submit",1,"btn","btn-primary"],[3,"ngClass",4,"ngIf"],[3,"ngClass"],["class","w-4 h-4","fill","none","stroke","currentColor","stroke-width","2","viewBox","0 0 24 24",4,"ngIf"],["fill","none","stroke","currentColor","stroke-width","2","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","d","M5 13l4 4L19 7"],["stroke-linecap","round","stroke-linejoin","round","d","M6 18L18 6M6 6l12 12"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"h2",1),P(2,"Contact me "),p()(),h(3,"div",2),Ct(),h(4,"svg",3)(5,"g",4)(6,"g",5)(7,"g",6)(8,"g",7),y(9,"path",8),p(),h(10,"g",9),y(11,"path",10),p(),h(12,"g",11),y(13,"path",12),p(),h(14,"g",13),y(15,"path",14),p(),h(16,"g",15),y(17,"path",16),p(),h(18,"g",17),y(19,"path",18),p(),h(20,"g",19),y(21,"path",20),p(),h(22,"g",21),y(23,"path",22),p(),h(24,"g",23),y(25,"path",24),p(),h(26,"g",25),y(27,"path",26),p(),h(28,"g",27),y(29,"path",28),p(),h(30,"g",29),y(31,"path",30),p(),h(32,"g",31),y(33,"path",32),p(),h(34,"g",33),y(35,"path",34),p(),h(36,"g",35),y(37,"path",36),p(),h(38,"g",37),y(39,"path",38),p(),h(40,"g",39),y(41,"path",40),p(),h(42,"g",41),y(43,"path",42),p(),h(44,"g",43),y(45,"path",44),p(),h(46,"g",45),y(47,"path",46),p(),h(48,"g",47),y(49,"path",48),p(),h(50,"g",49),y(51,"path",50),p(),h(52,"g",51),y(53,"path",52),p(),h(54,"g",53),y(55,"path",54),p(),h(56,"g",55),y(57,"path",56),p(),h(58,"g",57),y(59,"path",58),p(),h(60,"g",59),y(61,"path",60),p(),h(62,"g",61),y(63,"path",62),p(),h(64,"g",63),y(65,"path",64),p(),h(66,"g",65),y(67,"path",66),p(),h(68,"g",67),y(69,"path",68),p(),h(70,"g",69),y(71,"path",70),p(),h(72,"g",71),y(73,"path",72),p(),h(74,"g",73),y(75,"path",74),p(),h(76,"g",75),y(77,"path",76),p(),h(78,"g",77),y(79,"path",78),p(),h(80,"g",79),y(81,"path",80),p(),h(82,"g",81),y(83,"path",82),p(),h(84,"g",83),y(85,"path",84),p(),h(86,"g",85),y(87,"path",86),p(),y(88,"path",87)(89,"path",88)(90,"path",89)(91,"path",90)(92,"path",91),h(93,"g",92),y(94,"path",93),p(),h(95,"g",94),y(96,"path",95),p(),h(97,"g",96),y(98,"path",97),p(),h(99,"g",98),y(100,"path",99),p(),h(101,"g",100),y(102,"path",101),p(),y(103,"path",102)(104,"path",103)(105,"path",104)(106,"path",105)(107,"path",106)(108,"path",107)(109,"path",108)(110,"path",109)(111,"path",110)(112,"path",111)(113,"path",112)(114,"path",113),h(115,"g",114),y(116,"path",115),p(),h(117,"g",116),y(118,"path",117),p(),y(119,"path",118),h(120,"g",119),y(121,"path",120),p(),h(122,"g",121),y(123,"path",122),p(),y(124,"path",123),h(125,"g",124),y(126,"path",125),p(),h(127,"g",126),y(128,"path",127),p(),h(129,"g",128)(130,"g",129)(131,"g",130)(132,"g",131),y(133,"path",132),p(),h(134,"g",133),y(135,"path",134),p()()()(),h(136,"g",135)(137,"g",136)(138,"g",137)(139,"g",138),y(140,"path",139),p(),h(141,"g",140),y(142,"path",141),p()()()(),h(143,"g",142)(144,"g",143),y(145,"path",144),p(),y(146,"path",145)(147,"path",146)(148,"path",147)(149,"path",148)(150,"path",149)(151,"path",150)(152,"path",151)(153,"path",152),h(154,"g",153),y(155,"path",154),p(),h(156,"g",155),y(157,"path",156),p(),h(158,"g",157),y(159,"path",158),p()(),h(160,"g",159),y(161,"path",160),p(),h(162,"g",161),y(163,"path",162),p(),h(164,"g",163),y(165,"path",164),p(),h(166,"g",165),y(167,"path",166),p(),h(168,"g",167),y(169,"path",168),p(),h(170,"g",169),y(171,"path",170),p(),h(172,"g",171),y(173,"path",172),p(),h(174,"g",173),y(175,"path",174),p(),h(176,"g",175),y(177,"path",176),p(),h(178,"g",177),y(179,"path",178),p(),h(180,"g",179),y(181,"path",180),p(),h(182,"g",181),y(183,"path",182),p(),h(184,"g",183),y(185,"path",184),p(),h(186,"g",185),y(187,"path",186),p(),h(188,"g",187),y(189,"path",188),p(),h(190,"g",189),y(191,"path",190),p(),h(192,"g",191),y(193,"path",192),p(),h(194,"g",193),y(195,"path",194),p(),h(196,"g",195),y(197,"path",196),p(),h(198,"g",197),y(199,"path",198),p(),h(200,"g",199),y(201,"path",200),p(),h(202,"g",201),y(203,"path",202),p(),h(204,"g",203),y(205,"path",204),p()()()()(),ea(),h(206,"form",205),ct("submit",function(s){return i.onSubmit(s)}),h(207,"h1",206),P(208,"Talk to me"),p(),h(209,"div",207)(210,"label",208),y(211,"i",209),p(),h(212,"input",210),ct("input",function(){return i.onInputChange()}),p()(),h(213,"div",207)(214,"label",211),y(215,"i",212),p(),h(216,"input",213),ct("input",function(){return i.onInputChange()}),p()(),h(217,"div",214)(218,"textarea",215),ct("input",function(){return i.onInputChange()}),p()(),h(219,"div",216)(220,"button",217),P(221,"Send message"),p()()(),Ne(222,z4,5,7,"div",218),p()),r&2&&($(222),ie("ngIf",i.submissionStatus))},dependencies:[Aa,An,Py,Ay,lf],styles:['*[_ngcontent-%COMP%]{margin-bottom:1rem}.contact-header[_ngcontent-%COMP%]{text-align:center;color:var(--text-primary);padding:1rem;position:relative;font-weight:600;font-size:2rem}.contact-header[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:150px;height:4px;background-color:var(--accent-pink);border-radius:2px}.container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(2rem,8vw,9rem);max-width:100vw;padding:0 1rem}svg[_ngcontent-%COMP%]{width:30%;height:auto;animation:_ngcontent-%COMP%_float 2s ease-in-out infinite;margin-left:10%}form[_ngcontent-%COMP%]{width:35%;background-color:#fff;padding:2rem;border-radius:3rem;box-shadow:0 10px 20px #0000001a;margin-right:10%;transition:all .3s ease}.dark[_nghost-%COMP%]   form[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   form[_ngcontent-%COMP%]{background-color:var(--bg-card);box-shadow:0 10px 30px #000000b3}.title[_ngcontent-%COMP%]{font-weight:600;color:var(--text-primary);font-size:2.5rem;text-align:center;margin-bottom:1.5rem;transition:color .3s ease}.dark[_nghost-%COMP%]   .title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .title[_ngcontent-%COMP%]{color:var(--text-primary)}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:1rem 1rem 1rem 3rem;font-size:1.1rem;color:var(--text-secondary);background-color:var(--primary-pink-lightest);border:none;border-radius:2rem;box-shadow:0 7px 5px var(--shadow-pink);transition:all .3s ease}.dark[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{background-color:var(--bg-tertiary);color:var(--text-on-bg);border:1px solid var(--border-default);box-shadow:0 7px 15px #00000080}.dark[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .dark[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{border-color:var(--primary-pink, #f472b6);box-shadow:0 0 0 3px #f472b64d}.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;height:7rem}.form-group[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:var(--primary-pink)}[_ngcontent-%COMP%]::placeholder{color:var(--primary-pink-light);font-weight:600;transition:color .3s ease}.dark[_nghost-%COMP%]   [_ngcontent-%COMP%]::placeholder, .dark   [_nghost-%COMP%]   [_ngcontent-%COMP%]::placeholder{color:var(--text-muted, #94a3b8)}.btn.btn-primary[_ngcontent-%COMP%]{display:inline-block;width:100%;padding:.8rem;font-size:1.1rem;font-weight:700;border:none;border-radius:3rem;background:linear-gradient(131deg,var(--primary-pink),var(--accent-pink),var(--primary-pink-light),var(--primary-pink-dark));background-size:300% 100%;transition:all .3s ease-in-out;color:#fff;cursor:pointer}.btn.btn-primary[_ngcontent-%COMP%]:hover{box-shadow:0 7px 5px var(--shadow-pink);background-size:100% 100%;transform:translateY(-.15em)}@keyframes _ngcontent-%COMP%_float{0%{transform:translateY(0)}50%{transform:translateY(-20px)}to{transform:translateY(0)}}@keyframes _ngcontent-%COMP%_blink{0%{opacity:0}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.container[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:2rem}svg[_ngcontent-%COMP%]{width:60%;margin-left:0}form[_ngcontent-%COMP%]{width:90%;margin-right:0}.title[_ngcontent-%COMP%]{font-size:2rem}.btn.btn-primary[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 480px){.title[_ngcontent-%COMP%]{font-size:1.8rem}.contact-header[_ngcontent-%COMP%]{font-size:1.5rem}form[_ngcontent-%COMP%]{padding:1.5rem}.btn.btn-primary[_ngcontent-%COMP%]{padding:.6rem}}input[_ngcontent-%COMP%]:placeholder-shown::placeholder, textarea[_ngcontent-%COMP%]:placeholder-shown::placeholder{color:#fff}.success-message[_ngcontent-%COMP%], .error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;padding:1rem 1.25rem;border-radius:1rem;margin-top:1rem;font-size:1rem;font-weight:500;box-shadow:0 5px 15px #0000000d;animation:_ngcontent-%COMP%_fadeIn .4s ease-out;max-width:100%;transition:all .3s ease-in-out}.submission-message[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.4rem;padding:.25rem .5rem;margin-top:.5rem;font-size:.75rem;font-weight:500;border-radius:.75rem;max-width:100%;white-space:normal;word-break:break-word;animation:_ngcontent-%COMP%_fadeIn .4s ease-out;transition:all .3s ease-in-out}.success-message[_ngcontent-%COMP%]{background-color:#d1fae5;color:#065f46;border:1px solid #6ee7b7}.error-message[_ngcontent-%COMP%]{background-color:#fee2e2;color:#991b1b;border:1px solid #fca5a5}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}']})}return e})();var L1=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/skills";constructor(n){this.http=n}getSkills(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(E(on))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var W4=["skillCard"];function Z4(e,t){if(e&1&&(h(0,"div",7,0)(2,"div",8),y(3,"img",9),p(),h(4,"h3",10),P(5),p()()),e&2){let n=t.$implicit,r=t.index,i=Ge();Dt("visible",i.visible[r]),$(3),ie("src",n.icon,or)("alt",n.name),$(2),Sn(" ",n.name," ")}}var V1=(()=>{class e{skillsService;skills=[];visible=[];cards;constructor(n){this.skillsService=n}ngOnInit(){this.skillsService.getSkills().subscribe(n=>{this.skills=n.map(r=>({icon:r.photo,name:r.name})),this.visible=new Array(this.skills.length).fill(!1)},n=>console.error("Error fetching skills:",n))}ngAfterViewInit(){let n=new IntersectionObserver(r=>{r.forEach(i=>{let o=this.cards.toArray().findIndex(s=>s.nativeElement===i.target);i.isIntersecting&&o!==-1&&(this.visible[o]=!0,n.unobserve(i.target))})},{threshold:.15});this.cards.changes.subscribe(()=>{this.cards.forEach(r=>n.observe(r.nativeElement))})}static \u0275fac=function(r){return new(r||e)(B(L1))};static \u0275cmp=fe({type:e,selectors:[["app-skills"]],viewQuery:function(r,i){if(r&1&&In(W4,5),r&2){let o;Yt(o=Kt())&&(i.cards=o)}},decls:7,vars:1,consts:[["skillCard",""],["id","skills",1,"pt-16","pb-12","contact-header","transition-colors","duration-300"],[1,"text-center","pb-10"],[1,"text-5xl","font-righteous","text-pink-600","dark:text-pink-400","mb-10","relative","transition-colors","duration-300"],[1,"block","w-16","h-1","bg-pink-400","dark:bg-pink-500","mx-auto","mt-2","rounded-full","transition-colors","duration-300"],[1,"grid","grid-cols-2","sm:grid-cols-3","md:grid-cols-4","lg:grid-cols-5","gap-6","px-6","sm:px-12"],["class",`skill-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border rounded-3xl p-6 shadow-md
             hover:shadow-pink-200 dark:hover:shadow-dark-pink
             transition-all duration-300 hover:scale-105 flex flex-col items-center text-center`,3,"visible",4,"ngFor","ngForOf"],[1,"skill-card","bg-white","dark:bg-dark-bg-card","border","border-pink-200","dark:border-dark-border","rounded-3xl","p-6","shadow-md","hover:shadow-pink-200","dark:hover:shadow-dark-pink","transition-all","duration-300","hover:scale-105","flex","flex-col","items-center","text-center"],[1,"w-20","h-20","flex","items-center","justify-center","rounded-full","bg-pink-100","dark:bg-dark-bg-hover","border","border-pink-300","dark:border-dark-pink-border","shadow-inner","mb-3","transition-all","duration-300"],[1,"w-10","h-10","object-contain",3,"src","alt"],[1,"text-pink-700","dark:text-pink-400","font-semibold","text-sm","tracking-wide","transition-colors","duration-300"]],template:function(r,i){r&1&&(h(0,"div",1)(1,"div",2)(2,"h2",3),P(3," Skills "),y(4,"span",4),p()(),h(5,"div",5),Ne(6,Z4,6,5,"div",6),p()()),r&2&&($(6),ie("ngForOf",i.skills))},dependencies:[ei],styles:[".skill-card[_ngcontent-%COMP%]{opacity:0;transform:translateY(30px);transition:all .6s ease-out}.skill-card.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.skill-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px) scale(1.05);box-shadow:0 8px 16px #ec489933}"]})}return e})();var j1=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/experiences";constructor(n){this.http=n}getExperiences(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(E(on))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var K4=["card"];function X4(e,t){if(e&1&&(h(0,"div",14,0)(2,"h3",15),P(3),p(),h(4,"p",16),P(5),p(),h(6,"p",17),P(7),Li(8,"date"),Li(9,"date"),p()()),e&2){let n=Ge(),r=n.$implicit,i=n.index,o=Ge();Dt("visible",o.cardVisible[i]),$(3),ut(r.title),$(2),ut(r.company),$(2),ua(" ",Vi(8,6,r.from,"MMM yyyy")," - ",r.to?Vi(9,9,r.to,"MMM yyyy"):"Present"," ")}}function J4(e,t){if(e&1&&(h(0,"div",18,0)(2,"h3",15),P(3),p(),h(4,"p",16),P(5),p(),h(6,"p",17),P(7),Li(8,"date"),Li(9,"date"),p()()),e&2){let n=Ge(),r=n.$implicit,i=n.index,o=Ge();Dt("visible",o.cardVisible[i]),$(3),ut(r.title),$(2),ut(r.company),$(2),ua(" ",Vi(8,6,r.from,"MMM yyyy")," - ",r.to?Vi(9,9,r.to,"MMM yyyy"):"Present"," ")}}function eI(e,t){if(e&1&&(h(0,"div",8)(1,"div",9),Ne(2,X4,10,12,"div",10),p(),y(3,"div",11),h(4,"div",12),Ne(5,J4,10,12,"div",13),p()()),e&2){let n=t.index;$(2),ie("ngIf",n%2===0),$(3),ie("ngIf",n%2!==0)}}var B1=(()=>{class e{experiencesService;experiences=[];cardVisible=[];cards;constructor(n){this.experiencesService=n}ngOnInit(){this.experiencesService.getExperiences().subscribe(n=>{this.experiences=n.map(r=>({title:r.title,company:r.company,from:r.from,to:r.to})),this.cardVisible=new Array(this.experiences.length).fill(!1)},n=>{console.error("Error fetching experiences:",n)})}ngAfterViewInit(){let n=new IntersectionObserver(r=>{r.forEach(i=>{let o=this.cards.toArray().findIndex(s=>s.nativeElement===i.target);i.isIntersecting&&o!==-1&&(this.cardVisible[o]=!0,n.unobserve(i.target))})},{threshold:.2});this.cards.changes.subscribe(()=>{this.cards.forEach(r=>n.observe(r.nativeElement))})}static \u0275fac=function(r){return new(r||e)(B(j1))};static \u0275cmp=fe({type:e,selectors:[["app-experiences"]],viewQuery:function(r,i){if(r&1&&In(K4,5),r&2){let o;Yt(o=Kt())&&(i.cards=o)}},decls:8,vars:1,consts:[["card",""],["id","experience",1,"pt-10","transition-colors","duration-300"],[1,"text-center","pb-8"],[1,"text-5xl","font-righteous","text-pink-600","dark:text-pink-400","mb-10","relative","transition-colors","duration-300"],[1,"block","w-16","h-1","bg-pink-400","dark:bg-pink-500","mx-auto","mt-2","rounded-full","transition-colors","duration-300"],[1,"relative","max-w-5xl","mx-auto","px-4"],[1,"absolute","left-1/2","transform","-translate-x-1/2","h-full","border-l-2","border-pink-400","dark:border-pink-500","transition-colors","duration-300"],["class","mb-16 flex flex-col sm:flex-row items-center w-full relative",4,"ngFor","ngForOf"],[1,"mb-16","flex","flex-col","sm:flex-row","items-center","w-full","relative"],[1,"w-full","sm:w-1/2","pr-4","sm:pr-8","flex","justify-end","z-10"],["class",`timeline-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border p-6 rounded-lg shadow dark:shadow-dark-card w-full max-w-md text-right
         transition-transform transition-shadow duration-300 ease-in-out hover:scale-105 hover:shadow-xl`,3,"visible",4,"ngIf"],[1,"absolute","left-1/2","transform","-translate-x-1/2","w-5","h-5","bg-pink-400","dark:bg-pink-500","rounded-full","border-4","border-white","dark:border-dark-bg-primary","shadow","z-20","transition-all","duration-300"],[1,"w-full","sm:w-1/2","pl-4","sm:pl-8","flex","justify-start","z-10"],["class",`timeline-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border p-6 rounded-lg shadow dark:shadow-dark-card w-full max-w-md text-left
         transition-transform transition-shadow duration-300 ease-in-out hover:scale-105 hover:shadow-xl`,3,"visible",4,"ngIf"],[1,"timeline-card","bg-white","dark:bg-dark-bg-card","border","border-pink-200","dark:border-dark-border","p-6","rounded-lg","shadow","dark:shadow-dark-card","w-full","max-w-md","text-right","transition-transform","transition-shadow","duration-300","ease-in-out","hover:scale-105","hover:shadow-xl"],[1,"text-xl","font-semibold","theme-text-secondary","transition-colors","duration-300"],[1,"text-pink-600","dark:text-pink-400","transition-colors","duration-300"],[1,"text-pink-500","dark:text-pink-300","text-sm","transition-colors","duration-300"],[1,"timeline-card","bg-white","dark:bg-dark-bg-card","border","border-pink-200","dark:border-dark-border","p-6","rounded-lg","shadow","dark:shadow-dark-card","w-full","max-w-md","text-left","transition-transform","transition-shadow","duration-300","ease-in-out","hover:scale-105","hover:shadow-xl"]],template:function(r,i){r&1&&(h(0,"div",1)(1,"div",2)(2,"h2",3),P(3," Education & Experience "),y(4,"span",4),p()(),h(5,"div",5),y(6,"div",6),Ne(7,eI,6,2,"div",7),p()()),r&2&&($(7),ie("ngForOf",i.experiences))},dependencies:[ei,An,v0],styles:[".timeline-card[_ngcontent-%COMP%]{opacity:0;transform:translateY(30px);transition:all .6s ease-out}.timeline-card.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.timeline-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px) scale(1.03);box-shadow:0 12px 24px #ec489933}"]})}return e})();var U1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=fe({type:e,selectors:[["app-footer"]],decls:17,vars:0,consts:[[1,"theme-bg-gradient","dark:bg-gradient-to-r","dark:from-dark-bg-secondary","dark:to-dark-bg-tertiary","text-white","py-1","transition-all","duration-300"],[1,"flex","justify-center","gap-4"],[1,"text-sm","hover:text-pink-200","dark:hover:text-pink-400","transition-colors"],["href","mailto:<EMAIL>",1,"theme-text-secondary","dark:text-gray-300","hover:text-pink-200","dark:hover:text-pink-400","transition-colors","duration-300"],["href","https://www.instagram.com/roaaayman_10/",1,"theme-text-secondary","dark:text-gray-300","hover:text-pink-200","dark:hover:text-pink-400","transition-colors","duration-300"],["href","https://github.com/roaaayman21",1,"theme-text-secondary","dark:text-gray-300","hover:text-pink-200","dark:hover:text-pink-400","transition-colors","duration-300"],["href","https://wa.me/+2001151310078",1,"theme-text-secondary","dark:text-gray-300","hover:text-pink-200","dark:hover:text-pink-400","transition-colors","duration-300"],[1,"text-xl"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"ul",1)(2,"li",2)(3,"a",3),P(4,"Email"),p()(),h(5,"li",2)(6,"a",4),P(7,"Instagram"),p()(),h(8,"li",2)(9,"a",5),P(10,"Github"),p()(),h(11,"li",2)(12,"a",6),P(13,"WhatsApp"),p()(),h(14,"li",7)(15,"p"),P(16,"\u{1F44B}"),p()()()())},styles:["div[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:100%;width:100%;background:transparent;background-size:cover;flex-direction:column}ul[_ngcontent-%COMP%]{display:inline-grid;grid-auto-flow:row;grid-gap:12px;justify-items:center;margin:auto;padding:10px;list-style:none;text-align:center}@media (min-width: 500px){ul[_ngcontent-%COMP%]{grid-auto-flow:column}}li[_ngcontent-%COMP%]{padding:5px}a[_ngcontent-%COMP%]{color:var(--text-secondary);text-decoration:none;font-size:1.2rem;box-shadow:inset 0 -1px 0 var(--border-pink)}a[_ngcontent-%COMP%]:hover{box-shadow:inset 0 -1.2em 0 var(--primary-pink-lighter)}li[_ngcontent-%COMP%]:last-child{grid-column:1 / 2;grid-row:1 / 2}li[_ngcontent-%COMP%]:hover ~ li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_wave-animation .3s infinite}@keyframes _ngcontent-%COMP%_wave-animation{0%,to{transform:rotate(0)}25%{transform:rotate(20deg)}75%{transform:rotate(-15deg)}}@media (max-width: 500px){div[_ngcontent-%COMP%]{background-size:200% 100%;background-position:center}}"]})}return e})();var $1=(()=>{class e{title="portflio";static \u0275fac=function(r){return new(r||e)};static \u0275cmp=fe({type:e,selectors:[["app-root"]],decls:7,vars:0,consts:[["id","projects"],["id","skills"],["id","experiences"],["id","contact"]],template:function(r,i){r&1&&y(0,"app-header")(1,"router-outlet")(2,"app-projects",0)(3,"app-skills",1)(4,"app-experiences",2)(5,"app-contact",3)(6,"app-footer")},dependencies:[Bf,P1,R1,F1,V1,B1,U1],styles:["[_nghost-%COMP%]{display:block;width:100%;max-width:100vw;overflow-x:hidden}"]})}return e})();var H1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=_e({type:e,bootstrap:[$1]});static \u0275inj=we({imports:[Ba,Dy,N1,k0,Ry,Fy]})}return e})();U0().bootstrapModule(H1,{ngZoneEventCoalescing:!0}).catch(e=>console.error(e));
