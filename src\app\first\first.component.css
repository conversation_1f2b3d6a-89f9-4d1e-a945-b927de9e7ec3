

.hero-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;                    /* Ensure full viewport width */
  max-width: 100%;                 /* Prevent overflow */
  color: var(--text-primary);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  /* Use global gradient background - no need to override */
  background: transparent;
}

/* Enhanced theme transitions */
:host-context(.dark) .hero-section {
  background: transparent; /* Use global dark mode gradient */
  color: var(--text-on-bg);
}

.hero-section:hover {
  /* Removed scale transform to prevent horizontal overflow */
  filter: brightness(1.05);       /* Alternative hover effect */
}

.profile-pic {
  border-radius: 50%;
  overflow: hidden;
  width: 150px;
  height: 150px;
  margin-bottom: 20px;
  border: 4px solid var(--border-pink);
  box-shadow: 0 0 20px var(--shadow-pink);
  transition: all 0.3s ease;
}

.profile-pic:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px var(--shadow-pink);
}

/* Enhanced dark mode styles for profile picture */
:host-context(.dark) .profile-pic {
  border-color: var(--primary-pink);
  box-shadow: 0 0 25px var(--shadow-pink);
}

:host-context(.dark) .profile-pic:hover {
  box-shadow: 0 0 35px var(--shadow-pink);
  border-color: var(--primary-pink-light);
}

.profile-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px var(--shadow-pink);
  transition: all 0.3s ease;
  font-weight: 700;
  text-align: center;
}

.hero-section p {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--text-secondary);
  transition: all 0.3s ease;
  text-align: center;
  max-width: 600px;
  line-height: 1.6;
}

/* Enhanced dark mode text styles */
:host-context(.dark) .hero-section h1 {
  color: var(--primary-pink);
  text-shadow: 2px 2px 8px var(--shadow-pink);
}

:host-context(.dark) .hero-section p {
  color: var(--text-on-bg);
}

/* Responsive text sizing */
@media (max-width: 768px) {
  .hero-section h1 {
    font-size: 2.5rem;
  }

  .hero-section p {
    font-size: 1.2rem;
    padding: 0 20px;
  }
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  color: var(--text-primary);
  font-size: 1.5rem;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: var(--accent-pink);
}

/* Dark mode social icons */
:host-context(.dark) .social-icons a {
  color: var(--text-primary);
}

:host-context(.dark) .social-icons a:hover {
  color: var(--accent-pink);
  filter: drop-shadow(0 0 8px var(--shadow-pink));
}

/* Vanta.js background will be automatically applied to #vanta-birds */

/* Additional theme-aware animations */
.hero-section * {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, text-shadow 0.3s ease;
}

/* Smooth fade-in animation for content */
.hero-section > * {
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.hero-section .profile-pic {
  animation-delay: 0.2s;
}

.hero-section h1 {
  animation-delay: 0.4s;
}

.hero-section p {
  animation-delay: 0.6s;
}

.hero-section .social-icons {
  animation-delay: 0.8s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced accessibility for theme switching */
@media (prefers-reduced-motion: reduce) {
  .hero-section *,
  .hero-section > * {
    animation: none;
    transition: none;
  }

  .hero-section > * {
    opacity: 1;
    transform: none;
  }
}


