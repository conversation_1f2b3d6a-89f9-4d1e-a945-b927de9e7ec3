import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DarkModeService {
  private readonly STORAGE_KEY = 'darkMode';
  private darkModeSubject = new BehaviorSubject<boolean>(false);
  
  constructor() {
    this.initializeDarkMode();
  }

  /**
   * Observable to track dark mode state changes
   */
  get isDarkMode$(): Observable<boolean> {
    return this.darkModeSubject.asObservable();
  }

  /**
   * Get current dark mode state
   */
  get isDarkMode(): boolean {
    return this.darkModeSubject.value;
  }

  /**
   * Initialize dark mode based on user preference or system preference
   */
  private initializeDarkMode(): void {
    const savedPreference = localStorage.getItem(this.STORAGE_KEY);
    
    if (savedPreference !== null) {
      // Use saved user preference
      this.setDarkMode(savedPreference === 'true');
    } else {
      // Check system preference
      // Default to light mode when no user preference is saved
      this.setDarkMode(false);
    }

    // Listen for system theme changes
    if (window.matchMedia) {
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
        // Only auto-switch if user hasn't set a preference
        if (localStorage.getItem(this.STORAGE_KEY) === null) {
          this.setDarkMode(e.matches);
        }
      });
    }
  }

  /**
   * Toggle between light and dark mode
   */
  toggleDarkMode(): void {
    this.setDarkMode(!this.isDarkMode);
  }

  /**
   * Set dark mode state
   */
  setDarkMode(isDark: boolean): void {
    this.darkModeSubject.next(isDark);
    this.updateDocumentClass(isDark);
    this.savePreference(isDark);
  }

  /**
   * Update the document class to apply dark mode styles
   */
  private updateDocumentClass(isDark: boolean): void {
    const htmlElement = document.documentElement;
    
    if (isDark) {
      htmlElement.classList.add('dark');
    } else {
      htmlElement.classList.remove('dark');
    }
  }

  /**
   * Save user preference to localStorage
   */
  private savePreference(isDark: boolean): void {
    localStorage.setItem(this.STORAGE_KEY, isDark.toString());
  }

  /**
   * Clear saved preference (will use system preference)
   */
  clearPreference(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    // Revert to default light mode when clearing preference
    this.setDarkMode(false);
  }
}
