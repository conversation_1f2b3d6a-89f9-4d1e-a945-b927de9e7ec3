import {
  Component,
  OnInit,
  AfterViewInit,
  ElementRef,
  QueryList,
  ViewChildren
} from '@angular/core';
import { ApiService } from '../project.service';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.css']
})
export class ProjectsComponent implements OnInit, AfterViewInit {
  projects: any[] = [];
  isVisible: boolean[] = [];

  @ViewChildren('cardRef') cardElements!: QueryList<ElementRef>;

  constructor(private apiService: ApiService) {}

  ngOnInit(): void {
    this.apiService.getProjects().subscribe(
      (data: any[]) => {
        this.projects = data.map(project => ({
          photo: project.photo,
          name: project.name,
          title: project.title,
          description: project.description,
          link: project.link,
          githubLink: project.githubLink,
          skills: project.skills
        }));
        this.isVisible = new Array(this.projects.length).fill(false); // جهزي الفلاجز
      },
      error => {
        console.error('Error fetching projects:', error);
      }
    );
  }

  ngAfterViewInit(): void {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const index = this.cardElements.toArray().findIndex(
              el => el.nativeElement === entry.target
            );
            if (index !== -1) {
              this.isVisible[index] = true;
            }
          }
        });
      },
      {
        threshold: 0.2
      }
    );

    setTimeout(() => {
      this.cardElements.forEach(el => observer.observe(el.nativeElement));
    }, 500); // تأخير بسيط لضمان إن العناصر دخلت الـ DOM
  }
}
