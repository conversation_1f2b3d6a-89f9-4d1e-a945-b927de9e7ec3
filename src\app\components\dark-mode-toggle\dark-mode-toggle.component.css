/* Dark mode toggle component styles */
.dark-mode-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Smooth transitions for all elements */
button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

button:hover {
  transform: scale(1.05);
}

button:active {
  transform: scale(0.95);
}

/* Focus styles for accessibility */
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(187, 2, 98, 0.3);
}

/* Icon transition effects */
svg {
  transition: all 0.2s ease-in-out;
}

/* Hover effects for the toggle circle */
span:hover svg {
  transform: scale(1.1);
}

/* Custom animation for smooth sliding */
@keyframes slide-right {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(1.5rem);
  }
}

@keyframes slide-left {
  from {
    transform: translateX(1.5rem);
  }
  to {
    transform: translateX(0);
  }
}

/* Ensure proper z-index for the toggle */
button {
  z-index: 10;
}
