# Dark Mode Color Palette

## Your Custom Dark Mode Colors

This document outlines the custom dark mode color palette implemented in your portfolio application.

### Color Specifications

| Color Name          | Hex Code  | Usage                           | CSS Variable           |
| ------------------- | --------- | ------------------------------- | ---------------------- |
| **Background**      | `#000000` | Pure black background           | `--bg-primary`         |
| **Surface (Cards)** | `#2C2C34` | Dark grey-blue for depth        | `--bg-card`            |
| **Primary Pink**    | `#F48FB1` | Soft pink for accents & buttons | `--primary-pink`       |
| **Secondary**       | `#6A4F58` | Muted mauve for variety         | `--primary-pink-dark`  |
| **Text Primary**    | `#FFFFFF` | White text                      | `--text-on-bg`         |
| **Text Secondary**  | `#CCCCCC` | Soft grey for supporting text   | `--text-muted`         |
| **Accent Hover**    | `#FFB6C1` | Light pink for hover effect     | `--primary-pink-light` |

### Implementation

#### CSS Variables

The colors are implemented as CSS variables in `src/styles.css` under the `.dark` class:

```css
.dark {
  --bg-primary: #000000; /* Pure black background */
  --bg-card: #2c2c34; /* Surface (Cards) */
  --primary-pink: #f48fb1; /* Primary Pink */
  --primary-pink-dark: #6a4f58; /* Secondary */
  --text-on-bg: #ffffff; /* Text Primary */
  --text-muted: #cccccc; /* Text Secondary */
  --primary-pink-light: #ffb6c1; /* Accent Hover */
}
```

#### Tailwind CSS Classes

The colors are also available as Tailwind CSS classes:

```html
<!-- Background Colors -->
<div class="dark:bg-dark-bg-primary">Near-black background</div>
<div class="dark:bg-dark-bg-card">Card/surface background</div>

<!-- Text Colors -->
<p class="dark:text-dark-text-primary">White text</p>
<p class="dark:text-dark-text-secondary">Soft grey text</p>
<p class="dark:text-dark-pink-primary">Soft pink text</p>

<!-- Hover Effects -->
<button class="dark:hover:bg-dark-pink-hover">Hover for light pink</button>
```

#### Utility Classes

Additional utility classes are available for quick styling:

```html
<div class="dark:bg-dark-primary">Background color</div>
<div class="dark:bg-dark-surface">Surface/card color</div>
<p class="dark:text-dark-primary">Primary text</p>
<p class="dark:text-dark-secondary">Secondary text</p>
<span class="dark:text-pink-primary">Pink accent text</span>
<span class="dark:text-pink-secondary">Muted mauve text</span>
```

### Color Contrast & Accessibility

All colors have been chosen to maintain excellent contrast ratios:

- **White text (#FFFFFF)** on dark backgrounds provides maximum readability
- **Soft pink (#F48FB1)** provides sufficient contrast for accent elements
- **Light pink hover (#FFB6C1)** creates clear interactive feedback
- **Muted mauve (#6A4F58)** offers subtle variety while maintaining readability

### Usage Examples

#### Component Styling

```html
<!-- Card component -->
<div class="dark:bg-dark-surface dark:text-dark-primary border dark:border-dark-surface rounded-lg p-6">
  <h3 class="dark:text-pink-primary">Card Title</h3>
  <p class="dark:text-dark-secondary">Card description text</p>
  <button class="dark:bg-dark-pink-primary dark:hover:bg-pink-hover dark:text-dark-primary">Action Button</button>
</div>
```

#### Navigation

```html
<nav class="dark:bg-dark-primary dark:border-dark-surface">
  <a href="#" class="dark:text-dark-primary dark:hover:text-pink-hover">Home</a>
  <a href="#" class="dark:text-dark-secondary dark:hover:text-pink-primary">About</a>
</nav>
```

### Integration with Existing System

This color palette integrates seamlessly with your existing dark mode implementation:

- **Dark Mode Service**: Automatically applies these colors when dark mode is enabled
- **CSS Variables**: Smooth transitions between light and dark themes
- **Tailwind Configuration**: Extended color palette in `tailwind.config.js`
- **Component Support**: All existing components automatically use the new palette

### Browser Support

- Modern browsers with CSS custom properties support
- Graceful fallback for older browsers
- Consistent rendering across different devices and screen types
