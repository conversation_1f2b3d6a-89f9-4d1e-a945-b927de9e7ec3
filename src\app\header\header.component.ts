import { Component, ElementRef, ViewChild } from '@angular/core';
import { SliderService } from '../services/slider.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent {
  @ViewChild('mobileMenuCheckbox') mobileMenuCheckbox!: ElementRef<HTMLInputElement>;

  isMobileMenuOpen = false;

  constructor(private sliderService: SliderService) {}

  // Close mobile menu when a navigation link is clicked
  closeMobileMenu() {
    this.isMobileMenuOpen = false;
    if (this.mobileMenuCheckbox) {
      this.mobileMenuCheckbox.nativeElement.checked = false;
    }
  }

  // Toggle mobile menu
  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  /** Navigate via header links */
  goTo(index: number): void {
    this.sliderService.setIndex(index);
  }
}
