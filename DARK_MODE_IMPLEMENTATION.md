# Dark Mode Implementation Guide

## Overview
This portfolio application now includes a comprehensive dark mode implementation that maintains the pink/rose aesthetic while providing excellent contrast and readability in both light and dark themes.

## Features Implemented

### 1. Dark Mode Service (`src/app/services/dark-mode.service.ts`)
- Manages dark mode state using RxJS BehaviorSubject
- Persists user preference in localStorage
- Automatically detects system preference on first visit
- Listens for system theme changes
- Provides methods to toggle, set, and clear dark mode preference

### 2. Dark Mode Toggle Component (`src/app/components/dark-mode-toggle/`)
- Animated toggle switch with sun/moon icons
- Smooth transitions and hover effects
- Accessible with proper ARIA labels
- Integrated into the header navigation

### 3. Tailwind CSS Configuration
- Enabled class-based dark mode strategy
- Extended color palette with dark mode specific colors
- Added dark mode shadows and effects
- Maintained pink/rose theme consistency

### 4. CSS Variables System
- Light and dark mode color schemes
- Smooth transitions between themes
- Consistent theming across all components
- Easy maintenance and customization

### 5. Component Updates
All components have been updated with dark mode support:

#### Header/Navigation
- Dark background gradients
- Proper contrast for navigation links
- Dark mode toggle integration
- Mobile menu dark mode support

#### Hero/First Section
- Dark background compatibility
- Enhanced text shadows for dark mode
- Profile picture border adjustments
- Social icons with glow effects

#### Skills Section
- Dark card backgrounds
- Icon container styling
- Gradient background transitions
- Skill tag dark mode styling

#### Projects Section
- Dark project cards
- Button hover states
- Skill tag styling
- Image overlay adjustments

#### Experiences Section
- Timeline dark mode styling
- Card background updates
- Date and company text contrast
- Timeline connector styling

#### Contact Section
- Form input dark mode styling
- Button hover effects
- Background transitions
- SVG compatibility

#### Footer
- Dark gradient backgrounds
- Link hover states
- Social media link styling

#### About Section
- Text color transitions
- Background compatibility
- Spline animation integration

## Color Scheme

### Light Mode
- Primary Pink: #db2777 (pink-600)
- Background: #ffffff
- Text: #be185d (pink-700)
- Cards: #ffffff with pink borders

### Dark Mode
- Primary Background: #0f0f23 (dark blue-purple)
- Secondary Background: #1a1a2e
- Card Background: #1e1e3f
- Primary Pink: #f472b6 (brighter for contrast)
- Text: #f8fafc (almost white)

## Usage

### Toggling Dark Mode
```typescript
// Inject the service
constructor(private darkModeService: DarkModeService) {}

// Toggle dark mode
this.darkModeService.toggleDarkMode();

// Set specific mode
this.darkModeService.setDarkMode(true); // Enable dark mode
this.darkModeService.setDarkMode(false); // Enable light mode

// Subscribe to changes
this.darkModeService.isDarkMode$.subscribe(isDark => {
  console.log('Dark mode:', isDark);
});
```

### Using Dark Mode Classes
```html
<!-- Tailwind dark mode classes -->
<div class="bg-white dark:bg-dark-bg-card text-gray-900 dark:text-gray-100">
  Content that adapts to theme
</div>

<!-- CSS variables -->
<div class="theme-bg-primary theme-text-secondary">
  Content using CSS variables
</div>
```

## Accessibility Features
- Proper focus indicators in dark mode
- High contrast ratios maintained
- Smooth transitions to reduce jarring changes
- System preference detection
- Keyboard navigation support

## Browser Support
- Modern browsers with CSS custom properties support
- Graceful fallback for older browsers
- System preference detection where supported

## Customization
To customize colors, update the CSS variables in `src/styles.css` and the Tailwind configuration in `tailwind.config.js`.

## Testing
- Component unit tests included
- Visual testing recommended across different devices
- Accessibility testing with screen readers
- Performance testing for smooth transitions
