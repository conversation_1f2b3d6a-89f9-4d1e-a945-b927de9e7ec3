<div class="pt-5 pb-12 contact-header transition-colors duration-300" id="skills">
  <div class="text-center  pt-20">
    <h2 class="text-5xl font-righteous text-pink-600 dark:text-pink-400 mb-10 relative transition-colors duration-300">
      Skills
      <span class="block w-16 h-1 bg-pink-400 dark:bg-pink-500 mx-auto mt-2 rounded-full transition-colors duration-300"></span>
    </h2>
  </div>

  <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 px-6 sm:px-12">
    <div
      *ngFor="let skill of skills; let i = index"
      #skillCard
      class="skill-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border rounded-3xl p-6 shadow-md
             hover:shadow-pink-200 dark:hover:shadow-dark-pink
             transition-all duration-300 hover:scale-105 flex flex-col items-center text-center"
      [class.visible]="visible[i]"
    >
      <div class="w-20 h-20 flex items-center justify-center rounded-full bg-pink-100 dark:bg-dark-bg-hover border border-pink-300 dark:border-dark-pink-border shadow-inner mb-3 transition-all duration-300">
        <img
        [ngSrc]="skill.icon"
        width="64"
        height="64"
        [alt]="skill.name"
        class="w-10 h-10 object-contain" />
      </div>
      <h3 class="text-pink-700 dark:text-pink-400 font-semibold text-sm tracking-wide transition-colors duration-300">
        {{ skill.name }}
      </h3>
    </div>
  </div>
</div>
