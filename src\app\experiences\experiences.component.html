<div class="pt-5 transition-colors duration-300" id="experience">
  <div class="text-center " >
    <h2 class="text-5xl font-righteous text-pink-600 dark:text-pink-400 mt-20 relative transition-colors duration-300">
      Education & Experience
      <span class="block w-16 h-1 bg-pink-400 dark:bg-pink-500 mx-auto mt-8  mb-5 rounded-full transition-colors duration-300"></span>
    </h2>
  </div>

  <div class="relative max-w-5xl mx-auto px-4 ">
    <!-- Vertical Line -->
    <div class="absolute left-1/2 transform -translate-x-1/2 h-full border-l-2 border-pink-400 dark:border-pink-500 transition-colors duration-300"></div>

    <div *ngFor="let exp of experiences; let i = index" class="mb-16 flex flex-col sm:flex-row items-center w-full relative">

      <!-- Left Card -->
      <div class="w-full sm:w-1/2 pr-4 sm:pr-8 flex justify-end z-10">
      <div
  *ngIf="i % 2 === 0"
  #card
  class="timeline-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border p-6 rounded-lg shadow dark:shadow-dark-card w-full max-w-md text-right
         transition-transform transition-shadow duration-300 ease-in-out hover:scale-105 hover:shadow-xl"
  [class.visible]="cardVisible[i]">
          <h3 class="text-xl font-semibold theme-text-secondary transition-colors duration-300">{{ exp.title }}</h3>
          <p class="text-pink-600 dark:text-pink-400 transition-colors duration-300">{{ exp.company }}</p>
          <p class="text-pink-500 dark:text-pink-300 text-sm transition-colors duration-300">
            {{ exp.from | date:'MMM yyyy' }} - {{ exp.to ? (exp.to | date:'MMM yyyy') : 'Present' }}
          </p>
        </div>
      </div>

      <!-- Timeline Dot -->
      <div class="absolute left-1/2 transform -translate-x-1/2 w-5 h-5 bg-pink-400 dark:bg-pink-500 rounded-full border-4 border-white dark:border-dark-bg-primary shadow z-20 transition-all duration-300"></div>

      <!-- Right Card -->
      <div class="w-full sm:w-1/2 pl-4 sm:pl-8 flex justify-start z-10">
       <div
  *ngIf="i % 2 !== 0"
  #card
  class="timeline-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border p-6 rounded-lg shadow dark:shadow-dark-card w-full max-w-md text-left
         transition-transform transition-shadow duration-300 ease-in-out hover:scale-105 hover:shadow-xl"
  [class.visible]="cardVisible[i]">
          <h3 class="text-xl font-semibold theme-text-secondary transition-colors duration-300">{{ exp.title }}</h3>
          <p class="text-pink-600 dark:text-pink-400 transition-colors duration-300">{{ exp.company }}</p>
          <p class="text-pink-500 dark:text-pink-300 text-sm transition-colors duration-300">
            {{ exp.from | date:'MMM yyyy' }} - {{ exp.to ? (exp.to | date:'MMM yyyy') : 'Present' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
