var ev=Object.defineProperty,tv=Object.defineProperties;var nv=Object.getOwnPropertyDescriptors;var Ao=Object.getOwnPropertySymbols;var uh=Object.prototype.hasOwnProperty,dh=Object.prototype.propertyIsEnumerable;var ch=(e,t,n)=>t in e?ev(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,w=(e,t)=>{for(var n in t||={})uh.call(t,n)&&ch(e,n,t[n]);if(Ao)for(var n of Ao(t))dh.call(t,n)&&ch(e,n,t[n]);return e},q=(e,t)=>tv(e,nv(t));var jl=(e,t)=>{var n={};for(var r in e)uh.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&Ao)for(var r of Ao(e))t.indexOf(r)<0&&dh.call(e,r)&&(n[r]=e[r]);return n};var No=(e,t,n)=>new Promise((r,i)=>{var o=l=>{try{a(n.next(l))}catch(c){i(c)}},s=l=>{try{a(n.throw(l))}catch(c){i(c)}},a=l=>l.done?r(l.value):Promise.resolve(l.value).then(o,s);a((n=n.apply(e,t)).next())});function fh(e,t){return Object.is(e,t)}var be=null,Oo=!1,Po=1,dn=Symbol("SIGNAL");function Y(e){let t=be;return be=e,t}function hh(){return be}var yi={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function $l(e){if(Oo)throw new Error("");if(be===null)return;be.consumerOnSignalRead(e);let t=be.nextProducerIndex++;if(Lo(be),t<be.producerNode.length&&be.producerNode[t]!==e&&mi(be)){let n=be.producerNode[t];Fo(n,be.producerIndexOfThis[t])}be.producerNode[t]!==e&&(be.producerNode[t]=e,be.producerIndexOfThis[t]=mi(be)?yh(e,be,t):0),be.producerLastReadVersion[t]=e.version}function rv(){Po++}function ph(e){if(!(mi(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Po)){if(!e.producerMustRecompute(e)&&!zl(e)){e.dirty=!1,e.lastCleanEpoch=Po;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Po}}function gh(e){if(e.liveConsumerNode===void 0)return;let t=Oo;Oo=!0;try{for(let n of e.liveConsumerNode)n.dirty||iv(n)}finally{Oo=t}}function mh(){return be?.consumerAllowSignalWrites!==!1}function iv(e){e.dirty=!0,gh(e),e.consumerMarkedDirty?.(e)}function Ro(e){return e&&(e.nextProducerIndex=0),Y(e)}function Hl(e,t){if(Y(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(mi(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Fo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function zl(e){Lo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(ph(n),r!==n.version))return!0}return!1}function Gl(e){if(Lo(e),mi(e))for(let t=0;t<e.producerNode.length;t++)Fo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function yh(e,t,n){if(vh(e),e.liveConsumerNode.length===0&&Ch(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=yh(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Fo(e,t){if(vh(e),e.liveConsumerNode.length===1&&Ch(e))for(let r=0;r<e.producerNode.length;r++)Fo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],i=e.liveConsumerNode[t];Lo(i),i.producerIndexOfThis[r]=t}}function mi(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Lo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function vh(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Ch(e){return e.producerNode!==void 0}function Dh(e){let t=Object.create(ov);t.computation=e;let n=()=>{if(ph(t),$l(t),t.value===ko)throw t.error;return t.value};return n[dn]=t,n}var Bl=Symbol("UNSET"),Ul=Symbol("COMPUTING"),ko=Symbol("ERRORED"),ov=q(w({},yi),{value:Bl,dirty:!0,error:null,equal:fh,producerMustRecompute(e){return e.value===Bl||e.value===Ul},producerRecomputeValue(e){if(e.value===Ul)throw new Error("Detected cycle in computations.");let t=e.value;e.value=Ul;let n=Ro(e),r;try{r=e.computation()}catch(i){r=ko,e.error=i}finally{Hl(e,n)}if(t!==Bl&&t!==ko&&r!==ko&&e.equal(t,r)){e.value=t;return}e.value=r,e.version++}});function sv(){throw new Error}var _h=sv;function wh(){_h()}function bh(e){_h=e}var av=null;function Eh(e){let t=Object.create(Ih);t.value=e;let n=()=>($l(t),t.value);return n[dn]=t,n}function ql(e,t){mh()||wh(),e.equal(e.value,t)||(e.value=t,lv(e))}function Mh(e,t){mh()||wh(),ql(e,t(e.value))}var Ih=q(w({},yi),{equal:fh,value:void 0});function lv(e){e.version++,rv(),gh(e),av?.()}function k(e){return typeof e=="function"}function Cr(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Vo=Cr(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,i)=>`${i+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function vi(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var ue=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let o of n)o.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(k(r))try{r()}catch(o){t=o instanceof Vo?o.errors:[o]}let{_finalizers:i}=this;if(i){this._finalizers=null;for(let o of i)try{Sh(o)}catch(s){t=t??[],s instanceof Vo?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Vo(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Sh(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&vi(n,t)}remove(t){let{_finalizers:n}=this;n&&vi(n,t),t instanceof e&&t._removeParent(this)}};ue.EMPTY=(()=>{let e=new ue;return e.closed=!0,e})();var Wl=ue.EMPTY;function jo(e){return e instanceof ue||e&&"closed"in e&&k(e.remove)&&k(e.add)&&k(e.unsubscribe)}function Sh(e){k(e)?e():e.unsubscribe()}var ht={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Dr={setTimeout(e,t,...n){let{delegate:r}=Dr;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Dr;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Bo(e){Dr.setTimeout(()=>{let{onUnhandledError:t}=ht;if(t)t(e);else throw e})}function Ci(){}var xh=Zl("C",void 0,void 0);function Th(e){return Zl("E",void 0,e)}function Ah(e){return Zl("N",e,void 0)}function Zl(e,t,n){return{kind:e,value:t,error:n}}var Un=null;function _r(e){if(ht.useDeprecatedSynchronousErrorHandling){let t=!Un;if(t&&(Un={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Un;if(Un=null,n)throw r}}else e()}function Nh(e){ht.useDeprecatedSynchronousErrorHandling&&Un&&(Un.errorThrown=!0,Un.error=e)}var $n=class extends ue{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,jo(t)&&t.add(this)):this.destination=dv}static create(t,n,r){return new wr(t,n,r)}next(t){this.isStopped?Yl(Ah(t),this):this._next(t)}error(t){this.isStopped?Yl(Th(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Yl(xh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},cv=Function.prototype.bind;function Ql(e,t){return cv.call(e,t)}var Kl=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Uo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Uo(r)}else Uo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Uo(n)}}},wr=class extends $n{constructor(t,n,r){super();let i;if(k(t)||!t)i={next:t??void 0,error:n??void 0,complete:r??void 0};else{let o;this&&ht.useDeprecatedNextContext?(o=Object.create(t),o.unsubscribe=()=>this.unsubscribe(),i={next:t.next&&Ql(t.next,o),error:t.error&&Ql(t.error,o),complete:t.complete&&Ql(t.complete,o)}):i=t}this.destination=new Kl(i)}};function Uo(e){ht.useDeprecatedSynchronousErrorHandling?Nh(e):Bo(e)}function uv(e){throw e}function Yl(e,t){let{onStoppedNotification:n}=ht;n&&Dr.setTimeout(()=>n(e,t))}var dv={closed:!0,next:Ci,error:uv,complete:Ci};var br=typeof Symbol=="function"&&Symbol.observable||"@@observable";function We(e){return e}function Xl(...e){return Jl(e)}function Jl(e){return e.length===0?We:e.length===1?e[0]:function(n){return e.reduce((r,i)=>i(r),n)}}var W=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,i){let o=hv(n)?n:new wr(n,r,i);return _r(()=>{let{operator:s,source:a}=this;o.add(s?s.call(o,a):a?this._subscribe(o):this._trySubscribe(o))}),o}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Oh(r),new r((i,o)=>{let s=new wr({next:a=>{try{n(a)}catch(l){o(l),s.unsubscribe()}},error:o,complete:i});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[br](){return this}pipe(...n){return Jl(n)(this)}toPromise(n){return n=Oh(n),new n((r,i)=>{let o;this.subscribe(s=>o=s,s=>i(s),()=>r(o))})}}return e.create=t=>new e(t),e})();function Oh(e){var t;return(t=e??ht.Promise)!==null&&t!==void 0?t:Promise}function fv(e){return e&&k(e.next)&&k(e.error)&&k(e.complete)}function hv(e){return e&&e instanceof $n||fv(e)&&jo(e)}function ec(e){return k(e?.lift)}function Z(e){return t=>{if(ec(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function z(e,t,n,r,i){return new tc(e,t,n,r,i)}var tc=class extends $n{constructor(t,n,r,i,o,s){super(t),this.onFinalize=o,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){t.error(l)}}:super._next,this._error=i?function(a){try{i(a)}catch(l){t.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Er(){return Z((e,t)=>{let n=null;e._refCount++;let r=z(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let i=e._connection,o=n;n=null,i&&(!o||i===o)&&i.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Mr=class extends W{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,ec(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new ue;let n=this.getSubject();t.add(this.source.subscribe(z(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=ue.EMPTY)}return t}refCount(){return Er()(this)}};var Ph=Cr(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Ae=(()=>{class e extends W{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new $o(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Ph}next(n){_r(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){_r(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){_r(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:i,observers:o}=this;return r||i?Wl:(this.currentObservers=null,o.push(n),new ue(()=>{this.currentObservers=null,vi(o,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:i,isStopped:o}=this;r?n.error(i):o&&n.complete()}asObservable(){let n=new W;return n.source=this,n}}return e.create=(t,n)=>new $o(t,n),e})(),$o=class extends Ae{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Wl}};var pe=class extends Ae{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Ze=new W(e=>e.complete());function kh(e){return e&&k(e.schedule)}function Rh(e){return e[e.length-1]}function Ho(e){return k(Rh(e))?e.pop():void 0}function fn(e){return kh(Rh(e))?e.pop():void 0}function Lh(e,t,n,r){function i(o){return o instanceof n?o:new n(function(s){s(o)})}return new(n||(n=Promise))(function(o,s){function a(u){try{c(r.next(u))}catch(d){s(d)}}function l(u){try{c(r.throw(u))}catch(d){s(d)}}function c(u){u.done?o(u.value):i(u.value).then(a,l)}c((r=r.apply(e,t||[])).next())})}function Fh(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Hn(e){return this instanceof Hn?(this.v=e,this):new Hn(e)}function Vh(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),i,o=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),i[Symbol.asyncIterator]=function(){return this},i;function s(f){return function(m){return Promise.resolve(m).then(f,d)}}function a(f,m){r[f]&&(i[f]=function(v){return new Promise(function(_,S){o.push([f,v,_,S])>1||l(f,v)})},m&&(i[f]=m(i[f])))}function l(f,m){try{c(r[f](m))}catch(v){g(o[0][3],v)}}function c(f){f.value instanceof Hn?Promise.resolve(f.value.v).then(u,d):g(o[0][2],f)}function u(f){l("next",f)}function d(f){l("throw",f)}function g(f,m){f(m),o.shift(),o.length&&l(o[0][0],o[0][1])}}function jh(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Fh=="function"?Fh(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(o){n[o]=e[o]&&function(s){return new Promise(function(a,l){s=e[o](s),i(a,l,s.done,s.value)})}}function i(o,s,a,l){Promise.resolve(l).then(function(c){o({value:c,done:a})},s)}}var zo=e=>e&&typeof e.length=="number"&&typeof e!="function";function Go(e){return k(e?.then)}function qo(e){return k(e[br])}function Wo(e){return Symbol.asyncIterator&&k(e?.[Symbol.asyncIterator])}function Zo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function pv(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Qo=pv();function Yo(e){return k(e?.[Qo])}function Ko(e){return Vh(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:i}=yield Hn(n.read());if(i)return yield Hn(void 0);yield yield Hn(r)}}finally{n.releaseLock()}})}function Xo(e){return k(e?.getReader)}function Ce(e){if(e instanceof W)return e;if(e!=null){if(qo(e))return gv(e);if(zo(e))return mv(e);if(Go(e))return yv(e);if(Wo(e))return Bh(e);if(Yo(e))return vv(e);if(Xo(e))return Cv(e)}throw Zo(e)}function gv(e){return new W(t=>{let n=e[br]();if(k(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function mv(e){return new W(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function yv(e){return new W(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Bo)})}function vv(e){return new W(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Bh(e){return new W(t=>{Dv(e,t).catch(n=>t.error(n))})}function Cv(e){return Bh(Ko(e))}function Dv(e,t){var n,r,i,o;return Lh(this,void 0,void 0,function*(){try{for(n=jh(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=n.return)&&(yield o.call(n))}finally{if(i)throw i.error}}t.complete()})}function je(e,t,n,r=0,i=!1){let o=t.schedule(function(){n(),i?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(o),!i)return o}function Jo(e,t=0){return Z((n,r)=>{n.subscribe(z(r,i=>je(r,e,()=>r.next(i),t),()=>je(r,e,()=>r.complete(),t),i=>je(r,e,()=>r.error(i),t)))})}function es(e,t=0){return Z((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Uh(e,t){return Ce(e).pipe(es(t),Jo(t))}function $h(e,t){return Ce(e).pipe(es(t),Jo(t))}function Hh(e,t){return new W(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function zh(e,t){return new W(n=>{let r;return je(n,t,()=>{r=e[Qo](),je(n,t,()=>{let i,o;try{({value:i,done:o}=r.next())}catch(s){n.error(s);return}o?n.complete():n.next(i)},0,!0)}),()=>k(r?.return)&&r.return()})}function ts(e,t){if(!e)throw new Error("Iterable cannot be null");return new W(n=>{je(n,t,()=>{let r=e[Symbol.asyncIterator]();je(n,t,()=>{r.next().then(i=>{i.done?n.complete():n.next(i.value)})},0,!0)})})}function Gh(e,t){return ts(Ko(e),t)}function qh(e,t){if(e!=null){if(qo(e))return Uh(e,t);if(zo(e))return Hh(e,t);if(Go(e))return $h(e,t);if(Wo(e))return ts(e,t);if(Yo(e))return zh(e,t);if(Xo(e))return Gh(e,t)}throw Zo(e)}function ce(e,t){return t?qh(e,t):Ce(e)}function A(...e){let t=fn(e);return ce(e,t)}function Ir(e,t){let n=k(e)?e:()=>e,r=i=>i.error(n());return new W(t?i=>t.schedule(r,0,i):r)}function nc(e){return!!e&&(e instanceof W||k(e.lift)&&k(e.subscribe))}var Ut=Cr(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function R(e,t){return Z((n,r)=>{let i=0;n.subscribe(z(r,o=>{r.next(e.call(t,o,i++))}))})}var{isArray:_v}=Array;function wv(e,t){return _v(t)?e(...t):e(t)}function ns(e){return R(t=>wv(e,t))}var{isArray:bv}=Array,{getPrototypeOf:Ev,prototype:Mv,keys:Iv}=Object;function rs(e){if(e.length===1){let t=e[0];if(bv(t))return{args:t,keys:null};if(Sv(t)){let n=Iv(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Sv(e){return e&&typeof e=="object"&&Ev(e)===Mv}function is(e,t){return e.reduce((n,r,i)=>(n[r]=t[i],n),{})}function Di(...e){let t=fn(e),n=Ho(e),{args:r,keys:i}=rs(e);if(r.length===0)return ce([],t);let o=new W(xv(r,t,i?s=>is(i,s):We));return n?o.pipe(ns(n)):o}function xv(e,t,n=We){return r=>{Wh(t,()=>{let{length:i}=e,o=new Array(i),s=i,a=i;for(let l=0;l<i;l++)Wh(t,()=>{let c=ce(e[l],t),u=!1;c.subscribe(z(r,d=>{o[l]=d,u||(u=!0,a--),a||r.next(n(o.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Wh(e,t,n){e?je(n,e,t):t()}function Zh(e,t,n,r,i,o,s,a){let l=[],c=0,u=0,d=!1,g=()=>{d&&!l.length&&!c&&t.complete()},f=v=>c<r?m(v):l.push(v),m=v=>{o&&t.next(v),c++;let _=!1;Ce(n(v,u++)).subscribe(z(t,S=>{i?.(S),o?f(S):t.next(S)},()=>{_=!0},void 0,()=>{if(_)try{for(c--;l.length&&c<r;){let S=l.shift();s?je(t,s,()=>m(S)):m(S)}g()}catch(S){t.error(S)}}))};return e.subscribe(z(t,f,()=>{d=!0,g()})),()=>{a?.()}}function De(e,t,n=1/0){return k(t)?De((r,i)=>R((o,s)=>t(r,o,i,s))(Ce(e(r,i))),n):(typeof t=="number"&&(n=t),Z((r,i)=>Zh(r,i,e,n)))}function Sr(e=1/0){return De(We,e)}function Qh(){return Sr(1)}function xr(...e){return Qh()(ce(e,fn(e)))}function os(e){return new W(t=>{Ce(e()).subscribe(t)})}function rc(...e){let t=Ho(e),{args:n,keys:r}=rs(e),i=new W(o=>{let{length:s}=n;if(!s){o.complete();return}let a=new Array(s),l=s,c=s;for(let u=0;u<s;u++){let d=!1;Ce(n[u]).subscribe(z(o,g=>{d||(d=!0,c--),a[u]=g},()=>l--,void 0,()=>{(!l||!d)&&(c||o.next(r?is(r,a):a),o.complete())}))}});return t?i.pipe(ns(t)):i}function Be(e,t){return Z((n,r)=>{let i=0;n.subscribe(z(r,o=>e.call(t,o,i++)&&r.next(o)))})}function hn(e){return Z((t,n)=>{let r=null,i=!1,o;r=t.subscribe(z(n,void 0,void 0,s=>{o=Ce(e(s,hn(e)(t))),r?(r.unsubscribe(),r=null,o.subscribe(n)):i=!0})),i&&(r.unsubscribe(),r=null,o.subscribe(n))})}function Yh(e,t,n,r,i){return(o,s)=>{let a=n,l=t,c=0;o.subscribe(z(s,u=>{let d=c++;l=a?e(l,u,d):(a=!0,u),r&&s.next(l)},i&&(()=>{a&&s.next(l),s.complete()})))}}function $t(e,t){return k(t)?De(e,t,1):De(e,1)}function pn(e){return Z((t,n)=>{let r=!1;t.subscribe(z(n,i=>{r=!0,n.next(i)},()=>{r||n.next(e),n.complete()}))})}function Ht(e){return e<=0?()=>Ze:Z((t,n)=>{let r=0;t.subscribe(z(n,i=>{++r<=e&&(n.next(i),e<=r&&n.complete())}))})}function ic(e){return R(()=>e)}function ss(e=Tv){return Z((t,n)=>{let r=!1;t.subscribe(z(n,i=>{r=!0,n.next(i)},()=>r?n.complete():n.error(e())))})}function Tv(){return new Ut}function gn(e){return Z((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function St(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Be((i,o)=>e(i,o,r)):We,Ht(1),n?pn(t):ss(()=>new Ut))}function Tr(e){return e<=0?()=>Ze:Z((t,n)=>{let r=[];t.subscribe(z(n,i=>{r.push(i),e<r.length&&r.shift()},()=>{for(let i of r)n.next(i);n.complete()},void 0,()=>{r=null}))})}function oc(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Be((i,o)=>e(i,o,r)):We,Tr(1),n?pn(t):ss(()=>new Ut))}function sc(e,t){return Z(Yh(e,t,arguments.length>=2,!0))}function ac(...e){let t=fn(e);return Z((n,r)=>{(t?xr(e,n,t):xr(e,n)).subscribe(r)})}function Ue(e,t){return Z((n,r)=>{let i=null,o=0,s=!1,a=()=>s&&!i&&r.complete();n.subscribe(z(r,l=>{i?.unsubscribe();let c=0,u=o++;Ce(e(l,u)).subscribe(i=z(r,d=>r.next(t?t(l,d,u,c++):d),()=>{i=null,a()}))},()=>{s=!0,a()}))})}function lc(e){return Z((t,n)=>{Ce(e).subscribe(z(n,()=>n.complete(),Ci)),!n.closed&&t.subscribe(n)})}function xe(e,t,n){let r=k(e)||t||n?{next:e,error:t,complete:n}:e;return r?Z((i,o)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;i.subscribe(z(o,l=>{var c;(c=r.next)===null||c===void 0||c.call(r,l),o.next(l)},()=>{var l;a=!1,(l=r.complete)===null||l===void 0||l.call(r),o.complete()},l=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,l),o.error(l)},()=>{var l,c;a&&((l=r.unsubscribe)===null||l===void 0||l.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):We}var Fp="https://g.co/ng/security#xss",D=class extends Error{constructor(t,n){super(qs(t,n)),this.code=t}};function qs(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function Ai(e){return{toString:e}.toString()}var as="__parameters__";function Av(e){return function(...n){if(e){let r=e(...n);for(let i in r)this[i]=r[i]}}}function Lp(e,t,n){return Ai(()=>{let r=Av(t);function i(...o){if(this instanceof i)return r.apply(this,o),this;let s=new i(...o);return a.annotation=s,a;function a(l,c,u){let d=l.hasOwnProperty(as)?l[as]:Object.defineProperty(l,as,{value:[]})[as];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}var ke=globalThis;function ie(e){for(let t in e)if(e[t]===ie)return t;throw Error("Could not find renamed property on target object.")}function Nv(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Fe(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(Fe).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Kh(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var Ov=ie({__forward_ref__:ie});function Ws(e){return e.__forward_ref__=Ws,e.toString=function(){return Fe(this())},e}function Re(e){return Vp(e)?e():e}function Vp(e){return typeof e=="function"&&e.hasOwnProperty(Ov)&&e.__forward_ref__===Ws}function b(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ge(e){return{providers:e.providers||[],imports:e.imports||[]}}function Zs(e){return Xh(e,Bp)||Xh(e,Up)}function jp(e){return Zs(e)!==null}function Xh(e,t){return e.hasOwnProperty(t)?e[t]:null}function Pv(e){let t=e&&(e[Bp]||e[Up]);return t||null}function Jh(e){return e&&(e.hasOwnProperty(ep)||e.hasOwnProperty(kv))?e[ep]:null}var Bp=ie({\u0275prov:ie}),ep=ie({\u0275inj:ie}),Up=ie({ngInjectableDef:ie}),kv=ie({ngInjectorDef:ie}),E=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=b({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function $p(e){return e&&!!e.\u0275providers}var Rv=ie({\u0275cmp:ie}),Fv=ie({\u0275dir:ie}),Lv=ie({\u0275pipe:ie}),Vv=ie({\u0275mod:ie}),vs=ie({\u0275fac:ie}),wi=ie({__NG_ELEMENT_ID__:ie}),tp=ie({__NG_ENV_ID__:ie});function Fr(e){return typeof e=="string"?e:e==null?"":String(e)}function jv(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Fr(e)}function Bv(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new D(-200,e)}function yu(e,t){throw new D(-201,!1)}var j=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(j||{}),bc;function Hp(){return bc}function $e(e){let t=bc;return bc=e,t}function zp(e,t,n){let r=Zs(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&j.Optional)return null;if(t!==void 0)return t;yu(e,"Injector")}var Uv={},Ei=Uv,Ec="__NG_DI_FLAG__",Cs="ngTempTokenPath",$v="ngTokenPath",Hv=/\n/gm,zv="\u0275",np="__source",kr;function Gv(){return kr}function mn(e){let t=kr;return kr=e,t}function qv(e,t=j.Default){if(kr===void 0)throw new D(-203,!1);return kr===null?zp(e,void 0,t):kr.get(e,t&j.Optional?null:void 0,t)}function M(e,t=j.Default){return(Hp()||qv)(Re(e),t)}function C(e,t=j.Default){return M(e,Qs(t))}function Qs(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Mc(e){let t=[];for(let n=0;n<e.length;n++){let r=Re(e[n]);if(Array.isArray(r)){if(r.length===0)throw new D(900,!1);let i,o=j.Default;for(let s=0;s<r.length;s++){let a=r[s],l=Wv(a);typeof l=="number"?l===-1?i=a.token:o|=l:i=a}t.push(M(i,o))}else t.push(M(r))}return t}function Gp(e,t){return e[Ec]=t,e.prototype[Ec]=t,e}function Wv(e){return e[Ec]}function Zv(e,t,n,r){let i=e[Cs];throw t[np]&&i.unshift(t[np]),e.message=Qv(`
`+e.message,i,n,r),e[$v]=i,e[Cs]=null,e}function Qv(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==zv?e.slice(2):e;let i=Fe(t);if(Array.isArray(t))i=t.map(Fe).join(" -> ");else if(typeof t=="object"){let o=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];o.push(s+":"+(typeof a=="string"?JSON.stringify(a):Fe(a)))}i=`{${o.join(", ")}}`}return`${n}${r?"("+r+")":""}[${i}]: ${e.replace(Hv,`
  `)}`}var Ys=Gp(Lp("Optional"),8);var vu=Gp(Lp("SkipSelf"),4);function qn(e,t){let n=e.hasOwnProperty(vs);return n?e[vs]:null}function Yv(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let i=e[r],o=t[r];if(n&&(i=n(i),o=n(o)),o!==i)return!1}return!0}function Kv(e){return e.flat(Number.POSITIVE_INFINITY)}function Cu(e,t){e.forEach(n=>Array.isArray(n)?Cu(n,t):t(n))}function qp(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ds(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Xv(e,t,n,r){let i=e.length;if(i==t)e.push(n,r);else if(i===1)e.push(r,e[0]),e[0]=n;else{for(i--,e.push(e[i-1],e[i]);i>t;){let o=i-2;e[i]=e[o],i--}e[t]=n,e[t+1]=r}}function Jv(e,t,n){let r=Ni(e,t);return r>=0?e[r|1]=n:(r=~r,Xv(e,r,t,n)),r}function cc(e,t){let n=Ni(e,t);if(n>=0)return e[n|1]}function Ni(e,t){return eC(e,t,1)}function eC(e,t,n){let r=0,i=e.length>>n;for(;i!==r;){let o=r+(i-r>>1),s=e[o<<n];if(t===s)return o<<n;s>t?i=o:r=o+1}return~(i<<n)}var Lr={},rt=[],Vr=new E(""),Wp=new E("",-1),Zp=new E(""),_s=class{get(t,n=Ei){if(n===Ei){let r=new Error(`NullInjectorError: No provider for ${Fe(t)}!`);throw r.name="NullInjectorError",r}return n}},Qp=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Qp||{}),At=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(At||{}),Cn=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Cn||{});function tC(e,t,n){let r=e.length;for(;;){let i=e.indexOf(t,n);if(i===-1)return i;if(i===0||e.charCodeAt(i-1)<=32){let o=t.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}n=i+1}}function Ic(e,t,n){let r=0;for(;r<n.length;){let i=n[r];if(typeof i=="number"){if(i!==0)break;r++;let o=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,o)}else{let o=i,s=n[++r];rC(o)?e.setProperty(t,o,s):e.setAttribute(t,o,s),r++}}return r}function nC(e){return e===3||e===4||e===6}function rC(e){return e.charCodeAt(0)===64}function Mi(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let i=t[r];typeof i=="number"?n=i:n===0||(n===-1||n===2?rp(e,n,i,null,t[++r]):rp(e,n,i,null,null))}}return e}function rp(e,t,n,r,i){let o=0,s=e.length;if(t===-1)s=-1;else for(;o<e.length;){let a=e[o++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=o-1;break}}}for(;o<e.length;){let a=e[o];if(typeof a=="number")break;if(a===n){if(r===null){i!==null&&(e[o+1]=i);return}else if(r===e[o+1]){e[o+2]=i;return}}o++,r!==null&&o++,i!==null&&o++}s!==-1&&(e.splice(s,0,t),o=s+1),e.splice(o++,0,n),r!==null&&e.splice(o++,0,r),i!==null&&e.splice(o++,0,i)}var Yp="ng-template";function iC(e,t,n,r){let i=0;if(r){for(;i<t.length&&typeof t[i]=="string";i+=2)if(t[i]==="class"&&tC(t[i+1].toLowerCase(),n,0)!==-1)return!0}else if(Du(e))return!1;if(i=t.indexOf(1,i),i>-1){let o;for(;++i<t.length&&typeof(o=t[i])=="string";)if(o.toLowerCase()===n)return!0}return!1}function Du(e){return e.type===4&&e.value!==Yp}function oC(e,t,n){let r=e.type===4&&!n?Yp:e.value;return t===r}function sC(e,t,n){let r=4,i=e.attrs,o=i!==null?cC(i):0,s=!1;for(let a=0;a<t.length;a++){let l=t[a];if(typeof l=="number"){if(!s&&!pt(r)&&!pt(l))return!1;if(s&&pt(l))continue;s=!1,r=l|r&1;continue}if(!s)if(r&4){if(r=2|r&1,l!==""&&!oC(e,l,n)||l===""&&t.length===1){if(pt(r))return!1;s=!0}}else if(r&8){if(i===null||!iC(e,i,l,n)){if(pt(r))return!1;s=!0}}else{let c=t[++a],u=aC(l,i,Du(e),n);if(u===-1){if(pt(r))return!1;s=!0;continue}if(c!==""){let d;if(u>o?d="":d=i[u+1].toLowerCase(),r&2&&c!==d){if(pt(r))return!1;s=!0}}}}return pt(r)||s}function pt(e){return(e&1)===0}function aC(e,t,n,r){if(t===null)return-1;let i=0;if(r||!n){let o=!1;for(;i<t.length;){let s=t[i];if(s===e)return i;if(s===3||s===6)o=!0;else if(s===1||s===2){let a=t[++i];for(;typeof a=="string";)a=t[++i];continue}else{if(s===4)break;if(s===0){i+=4;continue}}i+=o?1:2}return-1}else return uC(t,e)}function lC(e,t,n=!1){for(let r=0;r<t.length;r++)if(sC(e,t[r],n))return!0;return!1}function cC(e){for(let t=0;t<e.length;t++){let n=e[t];if(nC(n))return t}return e.length}function uC(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function ip(e,t){return e?":not("+t.trim()+")":t}function dC(e){let t=e[0],n=1,r=2,i="",o=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];i+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?i+="."+s:r&4&&(i+=" "+s);else i!==""&&!pt(s)&&(t+=ip(o,i),i=""),r=s,o=o||!pt(r);n++}return i!==""&&(t+=ip(o,i)),t}function fC(e){return e.map(dC).join(",")}function hC(e){let t=[],n=[],r=1,i=2;for(;r<e.length;){let o=e[r];if(typeof o=="string")i===2?o!==""&&t.push(o,e[++r]):i===8&&n.push(o);else{if(!pt(i))break;i=o}r++}return{attrs:t,classes:n}}function de(e){return Ai(()=>{let t=ng(e),n=q(w({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Qp.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||At.Emulated,styles:e.styles||rt,_:null,schemas:e.schemas||null,tView:null,id:""});rg(n);let r=e.dependencies;return n.directiveDefs=sp(r,!1),n.pipeDefs=sp(r,!0),n.id=mC(n),n})}function pC(e){return Dn(e)||Xp(e)}function gC(e){return e!==null}function me(e){return Ai(()=>({type:e.type,bootstrap:e.bootstrap||rt,declarations:e.declarations||rt,imports:e.imports||rt,exports:e.exports||rt,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function op(e,t){if(e==null)return Lr;let n={};for(let r in e)if(e.hasOwnProperty(r)){let i=e[r],o,s,a=Cn.None;Array.isArray(i)?(a=i[0],o=i[1],s=i[2]??o):(o=i,s=i),t?(n[o]=a!==Cn.None?[r,a]:r,t[o]=s):n[o]=r}return n}function mt(e){return Ai(()=>{let t=ng(e);return rg(t),t})}function Kp(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Dn(e){return e[Rv]||null}function Xp(e){return e[Fv]||null}function Jp(e){return e[Lv]||null}function eg(e){let t=Dn(e)||Xp(e)||Jp(e);return t!==null?t.standalone:!1}function tg(e,t){let n=e[Vv]||null;if(!n&&t===!0)throw new Error(`Type ${Fe(e)} does not have '\u0275mod' property.`);return n}function ng(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Lr,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||rt,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:op(e.inputs,t),outputs:op(e.outputs),debugInfo:null}}function rg(e){e.features?.forEach(t=>t(e))}function sp(e,t){if(!e)return null;let n=t?Jp:pC;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(gC)}function mC(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let i of n)t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=**********,"c"+t}function Ks(e){return{\u0275providers:e}}function yC(...e){return{\u0275providers:ig(!0,e),\u0275fromNgModule:!0}}function ig(e,...t){let n=[],r=new Set,i,o=s=>{n.push(s)};return Cu(t,s=>{let a=s;Sc(a,o,[],r)&&(i||=[],i.push(a))}),i!==void 0&&og(i,o),n}function og(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:i}=e[n];_u(i,o=>{t(o,r)})}}function Sc(e,t,n,r){if(e=Re(e),!e)return!1;let i=null,o=Jh(e),s=!o&&Dn(e);if(!o&&!s){let l=e.ngModule;if(o=Jh(l),o)i=l;else return!1}else{if(s&&!s.standalone)return!1;i=e}let a=r.has(i);if(s){if(a)return!1;if(r.add(i),s.dependencies){let l=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of l)Sc(c,t,n,r)}}else if(o){if(o.imports!=null&&!a){r.add(i);let c;try{Cu(o.imports,u=>{Sc(u,t,n,r)&&(c||=[],c.push(u))})}finally{}c!==void 0&&og(c,t)}if(!a){let c=qn(i)||(()=>new i);t({provide:i,useFactory:c,deps:rt},i),t({provide:Zp,useValue:i,multi:!0},i),t({provide:Vr,useValue:()=>M(i),multi:!0},i)}let l=o.providers;if(l!=null&&!a){let c=e;_u(l,u=>{t(u,c)})}}else return!1;return i!==e&&e.providers!==void 0}function _u(e,t){for(let n of e)$p(n)&&(n=n.\u0275providers),Array.isArray(n)?_u(n,t):t(n)}var vC=ie({provide:String,useValue:ie});function sg(e){return e!==null&&typeof e=="object"&&vC in e}function CC(e){return!!(e&&e.useExisting)}function DC(e){return!!(e&&e.useFactory)}function jr(e){return typeof e=="function"}function _C(e){return!!e.useClass}var Xs=new E(""),hs={},wC={},uc;function wu(){return uc===void 0&&(uc=new _s),uc}var Le=class{},Ii=class extends Le{get destroyed(){return this._destroyed}constructor(t,n,r,i){super(),this.parent=n,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Tc(t,s=>this.processProvider(s)),this.records.set(Wp,Ar(void 0,this)),i.has("environment")&&this.records.set(Le,Ar(void 0,this));let o=this.records.get(Xs);o!=null&&typeof o.value=="string"&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(Zp,rt,j.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=Y(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),Y(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=mn(this),r=$e(void 0),i;try{return t()}finally{mn(n),$e(r)}}get(t,n=Ei,r=j.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(tp))return t[tp](this);r=Qs(r);let i,o=mn(this),s=$e(void 0);try{if(!(r&j.SkipSelf)){let l=this.records.get(t);if(l===void 0){let c=SC(t)&&Zs(t);c&&this.injectableDefInScope(c)?l=Ar(xc(t),hs):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l)}let a=r&j.Self?wu():this.parent;return n=r&j.Optional&&n===Ei?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Cs]=a[Cs]||[]).unshift(Fe(t)),o)throw a;return Zv(a,t,"R3InjectorError",this.source)}else throw a}finally{$e(s),mn(o)}}resolveInjectorInitializers(){let t=Y(null),n=mn(this),r=$e(void 0),i;try{let o=this.get(Vr,rt,j.Self);for(let s of o)s()}finally{mn(n),$e(r),Y(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Fe(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new D(205,!1)}processProvider(t){t=Re(t);let n=jr(t)?t:Re(t&&t.provide),r=EC(t);if(!jr(t)&&t.multi===!0){let i=this.records.get(n);i||(i=Ar(void 0,hs,!0),i.factory=()=>Mc(i.multi),this.records.set(n,i)),n=t,i.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=Y(null);try{return n.value===hs&&(n.value=wC,n.value=n.factory()),typeof n.value=="object"&&n.value&&IC(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{Y(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=Re(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function xc(e){let t=Zs(e),n=t!==null?t.factory:qn(e);if(n!==null)return n;if(e instanceof E)throw new D(204,!1);if(e instanceof Function)return bC(e);throw new D(204,!1)}function bC(e){if(e.length>0)throw new D(204,!1);let n=Pv(e);return n!==null?()=>n.factory(e):()=>new e}function EC(e){if(sg(e))return Ar(void 0,e.useValue);{let t=ag(e);return Ar(t,hs)}}function ag(e,t,n){let r;if(jr(e)){let i=Re(e);return qn(i)||xc(i)}else if(sg(e))r=()=>Re(e.useValue);else if(DC(e))r=()=>e.useFactory(...Mc(e.deps||[]));else if(CC(e))r=()=>M(Re(e.useExisting));else{let i=Re(e&&(e.useClass||e.provide));if(MC(e))r=()=>new i(...Mc(e.deps));else return qn(i)||xc(i)}return r}function Ar(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function MC(e){return!!e.deps}function IC(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function SC(e){return typeof e=="function"||typeof e=="object"&&e instanceof E}function Tc(e,t){for(let n of e)Array.isArray(n)?Tc(n,t):n&&$p(n)?Tc(n.\u0275providers,t):t(n)}function ot(e,t){e instanceof Ii&&e.assertNotDestroyed();let n,r=mn(e),i=$e(void 0);try{return t()}finally{mn(r),$e(i)}}function lg(){return Hp()!==void 0||Gv()!=null}function xC(e){if(!lg())throw new D(-203,!1)}function TC(e){let t=ke.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function AC(e){return typeof e=="function"}var Wt=0,F=1,P=2,Pe=3,gt=4,yt=5,ws=6,bs=7,Nt=8,Br=9,Ot=10,Ne=11,Si=12,ap=13,qr=14,Pt=15,Wn=16,Nr=17,zt=18,Js=19,cg=20,yn=21,dc=22,it=23,kt=25,ug=1;var Zn=7,Es=8,Ur=9,Qe=10,Ms=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(Ms||{});function vn(e){return Array.isArray(e)&&typeof e[ug]=="object"}function Zt(e){return Array.isArray(e)&&e[ug]===!0}function dg(e){return(e.flags&4)!==0}function ea(e){return e.componentOffset>-1}function bu(e){return(e.flags&1)===1}function _n(e){return!!e.template}function Ac(e){return(e[P]&512)!==0}var Nc=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function fg(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function Wr(){return hg}function hg(e){return e.type.prototype.ngOnChanges&&(e.setInput=OC),NC}Wr.ngInherit=!0;function NC(){let e=gg(this),t=e?.current;if(t){let n=e.previous;if(n===Lr)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function OC(e,t,n,r,i){let o=this.declaredInputs[r],s=gg(e)||PC(e,{previous:Lr,current:null}),a=s.current||(s.current={}),l=s.previous,c=l[o];a[o]=new Nc(c&&c.currentValue,n,l===Lr),fg(e,t,i,n)}var pg="__ngSimpleChanges__";function gg(e){return e[pg]||null}function PC(e,t){return e[pg]=t}var lp=null;var xt=function(e,t,n){lp?.(e,t,n)},mg="svg",kC="math";function Rt(e){for(;Array.isArray(e);)e=e[Wt];return e}function yg(e,t){return Rt(t[e])}function st(e,t){return Rt(t[e.index])}function vg(e,t){return e.data[t]}function RC(e,t){return e[t]}function En(e,t){let n=t[e];return vn(n)?n:n[Wt]}function FC(e){return(e[P]&4)===4}function Eu(e){return(e[P]&128)===128}function LC(e){return Zt(e[Pe])}function Is(e,t){return t==null?null:e[t]}function Cg(e){e[Nr]=0}function Dg(e){e[P]&1024||(e[P]|=1024,Eu(e)&&na(e))}function VC(e,t){for(;e>0;)t=t[qr],e--;return t}function ta(e){return!!(e[P]&9216||e[it]?.dirty)}function Oc(e){e[Ot].changeDetectionScheduler?.notify(8),e[P]&64&&(e[P]|=1024),ta(e)&&na(e)}function na(e){e[Ot].changeDetectionScheduler?.notify(0);let t=Qn(e);for(;t!==null&&!(t[P]&8192||(t[P]|=8192,!Eu(t)));)t=Qn(t)}function _g(e,t){if((e[P]&256)===256)throw new D(911,!1);e[yn]===null&&(e[yn]=[]),e[yn].push(t)}function jC(e,t){if(e[yn]===null)return;let n=e[yn].indexOf(t);n!==-1&&e[yn].splice(n,1)}function Qn(e){let t=e[Pe];return Zt(t)?t[Pe]:t}var B={lFrame:Ng(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var wg=!1;function BC(){return B.lFrame.elementDepthCount}function UC(){B.lFrame.elementDepthCount++}function $C(){B.lFrame.elementDepthCount--}function bg(){return B.bindingsEnabled}function HC(){return B.skipHydrationRootTNode!==null}function zC(e){return B.skipHydrationRootTNode===e}function GC(){B.skipHydrationRootTNode=null}function ne(){return B.lFrame.lView}function ze(){return B.lFrame.tView}function Ye(){let e=Eg();for(;e!==null&&e.type===64;)e=e.parent;return e}function Eg(){return B.lFrame.currentTNode}function qC(){let e=B.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Oi(e,t){let n=B.lFrame;n.currentTNode=e,n.isParent=t}function Mg(){return B.lFrame.isParent}function WC(){B.lFrame.isParent=!1}function Ig(){return wg}function cp(e){wg=e}function Mu(){let e=B.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ZC(){return B.lFrame.bindingIndex}function QC(e){return B.lFrame.bindingIndex=e}function Iu(){return B.lFrame.bindingIndex++}function Sg(e){let t=B.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function YC(){return B.lFrame.inI18n}function KC(e,t){let n=B.lFrame;n.bindingIndex=n.bindingRootIndex=e,Pc(t)}function XC(){return B.lFrame.currentDirectiveIndex}function Pc(e){B.lFrame.currentDirectiveIndex=e}function JC(e){let t=B.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function xg(){return B.lFrame.currentQueryIndex}function Su(e){B.lFrame.currentQueryIndex=e}function eD(e){let t=e[F];return t.type===2?t.declTNode:t.type===1?e[yt]:null}function Tg(e,t,n){if(n&j.SkipSelf){let i=t,o=e;for(;i=i.parent,i===null&&!(n&j.Host);)if(i=eD(o),i===null||(o=o[qr],i.type&10))break;if(i===null)return!1;t=i,e=o}let r=B.lFrame=Ag();return r.currentTNode=t,r.lView=e,!0}function xu(e){let t=Ag(),n=e[F];B.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Ag(){let e=B.lFrame,t=e===null?null:e.child;return t===null?Ng(e):t}function Ng(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Og(){let e=B.lFrame;return B.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Pg=Og;function Tu(){let e=Og();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function tD(e){return(B.lFrame.contextLView=VC(e,B.lFrame.contextLView))[Nt]}function ir(){return B.lFrame.selectedIndex}function Yn(e){B.lFrame.selectedIndex=e}function kg(){let e=B.lFrame;return vg(e.tView,e.selectedIndex)}function vt(){B.lFrame.currentNamespace=mg}function ra(){nD()}function nD(){B.lFrame.currentNamespace=null}function rD(){return B.lFrame.currentNamespace}var Rg=!0;function Au(){return Rg}function Nu(e){Rg=e}function iD(e,t,n){let{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=t.type.prototype;if(r){let s=hg(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}i&&(n.preOrderHooks??=[]).push(0-e,i),o&&((n.preOrderHooks??=[]).push(e,o),(n.preOrderCheckHooks??=[]).push(e,o))}function Ou(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let o=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=o;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),l&&(e.viewHooks??=[]).push(-n,l),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function ps(e,t,n){Fg(e,t,3,n)}function gs(e,t,n,r){(e[P]&3)===n&&Fg(e,t,n,r)}function fc(e,t){let n=e[P];(n&3)===t&&(n&=16383,n+=1,e[P]=n)}function Fg(e,t,n,r){let i=r!==void 0?e[Nr]&65535:0,o=r??-1,s=t.length-1,a=0;for(let l=i;l<s;l++)if(typeof t[l+1]=="number"){if(a=t[l],r!=null&&a>=r)break}else t[l]<0&&(e[Nr]+=65536),(a<o||o==-1)&&(oD(e,n,t,l),e[Nr]=(e[Nr]&**********)+l+2),l++}function up(e,t){xt(4,e,t);let n=Y(null);try{t.call(e)}finally{Y(n),xt(5,e,t)}}function oD(e,t,n,r){let i=n[r]<0,o=n[r+1],s=i?-n[r]:n[r],a=e[s];i?e[P]>>14<e[Nr]>>16&&(e[P]&3)===t&&(e[P]+=16384,up(a,o)):up(a,o)}var Rr=-1,Kn=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function sD(e){return e instanceof Kn}function aD(e){return(e.flags&8)!==0}function lD(e){return(e.flags&16)!==0}var hc={},kc=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Qs(r);let i=this.injector.get(t,hc,r);return i!==hc||n===hc?i:this.parentInjector.get(t,n,r)}};function Lg(e){return e!==Rr}function Ss(e){return e&32767}function cD(e){return e>>16}function xs(e,t){let n=cD(e),r=t;for(;n>0;)r=r[qr],n--;return r}var Rc=!0;function Ts(e){let t=Rc;return Rc=e,t}var uD=256,Vg=uD-1,jg=5,dD=0,Tt={};function fD(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(wi)&&(r=n[wi]),r==null&&(r=n[wi]=dD++);let i=r&Vg,o=1<<i;t.data[e+(i>>jg)]|=o}function As(e,t){let n=Bg(e,t);if(n!==-1)return n;let r=t[F];r.firstCreatePass&&(e.injectorIndex=t.length,pc(r.data,e),pc(t,null),pc(r.blueprint,null));let i=Pu(e,t),o=e.injectorIndex;if(Lg(i)){let s=Ss(i),a=xs(i,t),l=a[F].data;for(let c=0;c<8;c++)t[o+c]=a[s+c]|l[s+c]}return t[o+8]=i,o}function pc(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Bg(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Pu(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,i=t;for(;i!==null;){if(r=Gg(i),r===null)return Rr;if(n++,i=i[qr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Rr}function Fc(e,t,n){fD(e,t,n)}function Ug(e,t,n){if(n&j.Optional||e!==void 0)return e;yu(t,"NodeInjector")}function $g(e,t,n,r){if(n&j.Optional&&r===void 0&&(r=null),!(n&(j.Self|j.Host))){let i=e[Br],o=$e(void 0);try{return i?i.get(t,r,n&j.Optional):zp(t,r,n&j.Optional)}finally{$e(o)}}return Ug(r,t,n)}function Hg(e,t,n,r=j.Default,i){if(e!==null){if(t[P]&2048&&!(r&j.Self)){let s=mD(e,t,n,r,Tt);if(s!==Tt)return s}let o=zg(e,t,n,r,Tt);if(o!==Tt)return o}return $g(t,n,r,i)}function zg(e,t,n,r,i){let o=pD(n);if(typeof o=="function"){if(!Tg(t,e,r))return r&j.Host?Ug(i,n,r):$g(t,n,r,i);try{let s;if(s=o(r),s==null&&!(r&j.Optional))yu(n);else return s}finally{Pg()}}else if(typeof o=="number"){let s=null,a=Bg(e,t),l=Rr,c=r&j.Host?t[Pt][yt]:null;for((a===-1||r&j.SkipSelf)&&(l=a===-1?Pu(e,t):t[a+8],l===Rr||!fp(r,!1)?a=-1:(s=t[F],a=Ss(l),t=xs(l,t)));a!==-1;){let u=t[F];if(dp(o,a,u.data)){let d=hD(a,t,n,s,r,c);if(d!==Tt)return d}l=t[a+8],l!==Rr&&fp(r,t[F].data[a+8]===c)&&dp(o,a,t)?(s=u,a=Ss(l),t=xs(l,t)):a=-1}}return i}function hD(e,t,n,r,i,o){let s=t[F],a=s.data[e+8],l=r==null?ea(a)&&Rc:r!=s&&(a.type&3)!==0,c=i&j.Host&&o===a,u=ms(a,s,n,l,c);return u!==null?Xn(t,s,u,a):Tt}function ms(e,t,n,r,i){let o=e.providerIndexes,s=t.data,a=o&1048575,l=e.directiveStart,c=e.directiveEnd,u=o>>20,d=r?a:a+u,g=i?a+u:c;for(let f=d;f<g;f++){let m=s[f];if(f<l&&n===m||f>=l&&m.type===n)return f}if(i){let f=s[l];if(f&&_n(f)&&f.type===n)return l}return null}function Xn(e,t,n,r){let i=e[n],o=t.data;if(sD(i)){let s=i;s.resolving&&Bv(jv(o[n]));let a=Ts(s.canSeeViewProviders);s.resolving=!0;let l,c=s.injectImpl?$e(s.injectImpl):null,u=Tg(e,r,j.Default);try{i=e[n]=s.factory(void 0,o,e,r),t.firstCreatePass&&n>=r.directiveStart&&iD(n,o[n],t)}finally{c!==null&&$e(c),Ts(a),s.resolving=!1,Pg()}}return i}function pD(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(wi)?e[wi]:void 0;return typeof t=="number"?t>=0?t&Vg:gD:t}function dp(e,t,n){let r=1<<e;return!!(n[t+(e>>jg)]&r)}function fp(e,t){return!(e&j.Self)&&!(e&j.Host&&t)}var Gn=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Hg(this._tNode,this._lView,t,Qs(r),n)}};function gD(){return new Gn(Ye(),ne())}function ia(e){return Ai(()=>{let t=e.prototype.constructor,n=t[vs]||Lc(t),r=Object.prototype,i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){let o=i[vs]||Lc(i);if(o&&o!==n)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function Lc(e){return Vp(e)?()=>{let t=Lc(Re(e));return t&&t()}:qn(e)}function mD(e,t,n,r,i){let o=e,s=t;for(;o!==null&&s!==null&&s[P]&2048&&!(s[P]&512);){let a=zg(o,s,n,r|j.Self,Tt);if(a!==Tt)return a;let l=o.parent;if(!l){let c=s[cg];if(c){let u=c.get(n,Tt,r);if(u!==Tt)return u}l=Gg(s),s=s[qr]}o=l}return i}function Gg(e){let t=e[F],n=t.type;return n===2?t.declTNode:n===1?e[yt]:null}function hp(e,t=null,n=null,r){let i=qg(e,t,n,r);return i.resolveInjectorInitializers(),i}function qg(e,t=null,n=null,r,i=new Set){let o=[n||rt,yC(e)];return r=r||(typeof e=="object"?void 0:Fe(e)),new Ii(o,t||wu(),r||null,i)}var He=class e{static{this.THROW_IF_NOT_FOUND=Ei}static{this.NULL=new _s}static create(t,n){if(Array.isArray(t))return hp({name:""},n,t,"");{let r=t.name??"";return hp({name:r},t.parent,t.providers,r)}}static{this.\u0275prov=b({token:e,providedIn:"any",factory:()=>M(Wp)})}static{this.__NG_ELEMENT_ID__=-1}};var yD=new E("");yD.__NG_ELEMENT_ID__=e=>{let t=Ye();if(t===null)throw new D(204,!1);if(t.type&2)return t.value;if(e&j.Optional)return null;throw new D(204,!1)};var vD="ngOriginalError";function gc(e){return e[vD]}var Wg=!0,ku=(()=>{class e{static{this.__NG_ELEMENT_ID__=CD}static{this.__NG_ENV_ID__=n=>n}}return e})(),Vc=class extends ku{constructor(t){super(),this._lView=t}onDestroy(t){return _g(this._lView,t),()=>jC(this._lView,t)}};function CD(){return new Vc(ne())}var Qt=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new pe(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}}return e})();var jc=class extends Ae{constructor(t=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=t,lg()&&(this.destroyRef=C(ku,{optional:!0})??void 0,this.pendingTasks=C(Qt,{optional:!0})??void 0)}emit(t){let n=Y(null);try{super.next(t)}finally{Y(n)}}subscribe(t,n,r){let i=t,o=n||(()=>null),s=r;if(t&&typeof t=="object"){let l=t;i=l.next?.bind(l),o=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(o=this.wrapInTimeout(o),i&&(i=this.wrapInTimeout(i)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:i,error:o,complete:s});return t instanceof ue&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},_e=jc;function Ns(...e){}function Zg(e){let t,n;function r(){e=Ns;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function pp(e){return queueMicrotask(()=>e()),()=>{e=Ns}}var Ru="isAngularZone",Os=Ru+"_ID",DD=0,K=class e{constructor(t){this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new _e(!1),this.onMicrotaskEmpty=new _e(!1),this.onStable=new _e(!1),this.onError=new _e(!1);let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:i=!1,scheduleInRootZone:o=Wg}=t;if(typeof Zone>"u")throw new D(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!i&&r,s.shouldCoalesceRunChangeDetection=i,s.callbackScheduled=!1,s.scheduleInRootZone=o,bD(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Ru)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new D(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new D(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,i){let o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,t,_D,Ns,Ns);try{return o.runTask(s,n,r)}finally{o.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},_D={};function Fu(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function wD(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Zg(()=>{e.callbackScheduled=!1,Bc(e),e.isCheckStableRunning=!0,Fu(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Bc(e)}function bD(e){let t=()=>{wD(e)},n=DD++;e._inner=e._inner.fork({name:"angular",properties:{[Ru]:!0,[Os]:n,[Os+n]:!0},onInvokeTask:(r,i,o,s,a,l)=>{if(ED(l))return r.invokeTask(o,s,a,l);try{return gp(e),r.invokeTask(o,s,a,l)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),mp(e)}},onInvoke:(r,i,o,s,a,l,c)=>{try{return gp(e),r.invoke(o,s,a,l,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!MD(l)&&t(),mp(e)}},onHasTask:(r,i,o,s)=>{r.hasTask(o,s),i===o&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Bc(e),Fu(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,i,o,s)=>(r.handleError(o,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Bc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function gp(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function mp(e){e._nesting--,Fu(e)}var Ps=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new _e,this.onMicrotaskEmpty=new _e,this.onStable=new _e,this.onError=new _e}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,i){return t.apply(n,r)}};function ED(e){return Qg(e,"__ignore_ng_zone__")}function MD(e){return Qg(e,"__scheduler_tick__")}function Qg(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function ID(e="zone.js",t){return e==="noop"?new Ps:e==="zone.js"?new K(t):e}var Gt=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&gc(t);for(;n&&gc(n);)n=gc(n);return n||null}},SD=new E("",{providedIn:"root",factory:()=>{let e=C(K),t=C(Gt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function xD(){return Zr(Ye(),ne())}function Zr(e,t){return new at(st(e,t))}var at=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=xD}}return e})();function TD(e){return e instanceof at?e.nativeElement:e}function AD(){return this._results[Symbol.iterator]()}var Uc=class e{get changes(){return this._changes??=new _e}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=e.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=AD)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Kv(t);(this._changesDetected=!Yv(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function Yg(e){return(e.flags&128)===128}var Kg=new Map,ND=0;function OD(){return ND++}function PD(e){Kg.set(e[Js],e)}function $c(e){Kg.delete(e[Js])}var yp="__ngContext__";function Jn(e,t){vn(t)?(e[yp]=t[Js],PD(t)):e[yp]=t}function Xg(e){return em(e[Si])}function Jg(e){return em(e[gt])}function em(e){for(;e!==null&&!Zt(e);)e=e[gt];return e}var Hc;function tm(e){Hc=e}function kD(){if(Hc!==void 0)return Hc;if(typeof document<"u")return document;throw new D(210,!1)}var oa=new E("",{providedIn:"root",factory:()=>RD}),RD="ng",Lu=new E(""),Ct=new E("",{providedIn:"platform",factory:()=>"unknown"});var Vu=new E(""),ju=new E("",{providedIn:"root",factory:()=>kD().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null}),Bu={breakpoints:[16,32,48,64,96,128,256,384,640,750,828,1080,1200,1920,2048,3840],placeholderResolution:30,disableImageSizeWarning:!1,disableImageLazyLoadWarning:!1},nm=new E("",{providedIn:"root",factory:()=>Bu});var FD="h",LD="b";var VD=()=>null;function Uu(e,t,n=!1){return VD(e,t,n)}var rm=!1,jD=new E("",{providedIn:"root",factory:()=>rm});var ls;function BD(){if(ls===void 0&&(ls=null,ke.trustedTypes))try{ls=ke.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ls}function UD(e){return BD()?.createScriptURL(e)||e}var ks=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Fp})`}};function Pi(e){return e instanceof ks?e.changingThisBreaksApplicationSecurity:e}function im(e,t){let n=$D(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Fp})`)}return n===t}function $D(e){return e instanceof ks&&e.getTypeName()||null}var HD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function om(e){return e=String(e),e.match(HD)?e:"unsafe:"+e}var $u=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}($u||{});function Hu(e){let t=zD();return t?t.sanitize($u.URL,e)||"":im(e,"URL")?Pi(e):om(Fr(e))}function sm(e){return UD(e[0])}function zD(){let e=ne();return e&&e[Ot].sanitizer}function am(e){return e.ownerDocument.defaultView}function lm(e){return e instanceof Function?e():e}function GD(e){return(e??C(He)).get(Ct)==="browser"}var qt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(qt||{}),qD;function zu(e,t){return qD(e,t)}function Or(e,t,n,r,i){if(r!=null){let o,s=!1;Zt(r)?o=r:vn(r)&&(s=!0,r=r[Wt]);let a=Rt(r);e===0&&n!==null?i==null?hm(t,n,a):Rs(t,n,a,i||null,!0):e===1&&n!==null?Rs(t,n,a,i||null,!0):e===2?a2(t,a,s):e===3&&t.destroyNode(a),o!=null&&c2(t,e,o,n,i)}}function WD(e,t){return e.createText(t)}function ZD(e,t,n){e.setValue(t,n)}function cm(e,t,n){return e.createElement(t,n)}function QD(e,t){um(e,t),t[Wt]=null,t[yt]=null}function YD(e,t,n,r,i,o){r[Wt]=i,r[yt]=t,sa(e,r,n,1,i,o)}function um(e,t){t[Ot].changeDetectionScheduler?.notify(9),sa(e,t,t[Ne],2,null,null)}function KD(e){let t=e[Si];if(!t)return mc(e[F],e);for(;t;){let n=null;if(vn(t))n=t[Si];else{let r=t[Qe];r&&(n=r)}if(!n){for(;t&&!t[gt]&&t!==e;)vn(t)&&mc(t[F],t),t=t[Pe];t===null&&(t=e),vn(t)&&mc(t[F],t),n=t&&t[gt]}t=n}}function XD(e,t,n,r){let i=Qe+r,o=n.length;r>0&&(n[i-1][gt]=t),r<o-Qe?(t[gt]=n[i],qp(n,Qe+r,t)):(n.push(t),t[gt]=null),t[Pe]=n;let s=t[Wn];s!==null&&n!==s&&dm(s,t);let a=t[zt];a!==null&&a.insertView(e),Oc(t),t[P]|=128}function dm(e,t){let n=e[Ur],r=t[Pe];if(vn(r))e[P]|=Ms.HasTransplantedViews;else{let i=r[Pe][Pt];t[Pt]!==i&&(e[P]|=Ms.HasTransplantedViews)}n===null?e[Ur]=[t]:n.push(t)}function Gu(e,t){let n=e[Ur],r=n.indexOf(t);n.splice(r,1)}function zc(e,t){if(e.length<=Qe)return;let n=Qe+t,r=e[n];if(r){let i=r[Wn];i!==null&&i!==e&&Gu(i,r),t>0&&(e[n-1][gt]=r[gt]);let o=Ds(e,Qe+t);QD(r[F],r);let s=o[zt];s!==null&&s.detachView(o[F]),r[Pe]=null,r[gt]=null,r[P]&=-129}return r}function fm(e,t){if(!(t[P]&256)){let n=t[Ne];n.destroyNode&&sa(e,t,n,3,null,null),KD(t)}}function mc(e,t){if(t[P]&256)return;let n=Y(null);try{t[P]&=-129,t[P]|=256,t[it]&&Gl(t[it]),e2(e,t),JD(e,t),t[F].type===1&&t[Ne].destroy();let r=t[Wn];if(r!==null&&Zt(t[Pe])){r!==t[Pe]&&Gu(r,t);let i=t[zt];i!==null&&i.detachView(e)}$c(t)}finally{Y(n)}}function JD(e,t){let n=e.cleanup,r=t[bs];if(n!==null)for(let o=0;o<n.length-1;o+=2)if(typeof n[o]=="string"){let s=n[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else{let s=r[n[o+1]];n[o].call(s)}r!==null&&(t[bs]=null);let i=t[yn];if(i!==null){t[yn]=null;for(let o=0;o<i.length;o++){let s=i[o];s()}}}function e2(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let i=t[n[r]];if(!(i instanceof Kn)){let o=n[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){let a=i[o[s]],l=o[s+1];xt(4,a,l);try{l.call(a)}finally{xt(5,a,l)}}else{xt(4,i,o);try{o.call(i)}finally{xt(5,i,o)}}}}}function t2(e,t,n){return n2(e,t.parent,n)}function n2(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Wt];{let{componentOffset:i}=r;if(i>-1){let{encapsulation:o}=e.data[r.directiveStart+i];if(o===At.None||o===At.Emulated)return null}return st(r,n)}}function Rs(e,t,n,r,i){e.insertBefore(t,n,r,i)}function hm(e,t,n){e.appendChild(t,n)}function vp(e,t,n,r,i){r!==null?Rs(e,t,n,r,i):hm(e,t,n)}function pm(e,t){return e.parentNode(t)}function r2(e,t){return e.nextSibling(t)}function i2(e,t,n){return s2(e,t,n)}function o2(e,t,n){return e.type&40?st(e,n):null}var s2=o2,Cp;function qu(e,t,n,r){let i=t2(e,r,t),o=t[Ne],s=r.parent||t[yt],a=i2(s,r,t);if(i!=null)if(Array.isArray(n))for(let l=0;l<n.length;l++)vp(o,i,n[l],a,!1);else vp(o,i,n,a,!1);Cp!==void 0&&Cp(o,r,t,n,i)}function _i(e,t){if(t!==null){let n=t.type;if(n&3)return st(t,e);if(n&4)return Gc(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return _i(e,r);{let i=e[t.index];return Zt(i)?Gc(-1,i):Rt(i)}}else{if(n&128)return _i(e,t.next);if(n&32)return zu(t,e)()||Rt(e[t.index]);{let r=gm(e,t);if(r!==null){if(Array.isArray(r))return r[0];let i=Qn(e[Pt]);return _i(i,r)}else return _i(e,t.next)}}}return null}function gm(e,t){if(t!==null){let r=e[Pt][yt],i=t.projection;return r.projection[i]}return null}function Gc(e,t){let n=Qe+e+1;if(n<t.length){let r=t[n],i=r[F].firstChild;if(i!==null)return _i(r,i)}return t[Zn]}function a2(e,t,n){e.removeChild(null,t,n)}function Wu(e,t,n,r,i,o,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],l=n.type;if(s&&t===0&&(a&&Jn(Rt(a),r),n.flags|=2),(n.flags&32)!==32)if(l&8)Wu(e,t,n.child,r,i,o,!1),Or(t,e,i,a,o);else if(l&32){let c=zu(n,r),u;for(;u=c();)Or(t,e,i,u,o);Or(t,e,i,a,o)}else l&16?l2(e,t,r,n,i,o):Or(t,e,i,a,o);n=s?n.projectionNext:n.next}}function sa(e,t,n,r,i,o){Wu(n,r,e.firstChild,t,i,o,!1)}function l2(e,t,n,r,i,o){let s=n[Pt],l=s[yt].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++){let u=l[c];Or(t,e,i,u,o)}else{let c=l,u=s[Pe];Yg(r)&&(c.flags|=128),Wu(e,t,c,u,i,o,!0)}}function c2(e,t,n,r,i){let o=n[Zn],s=Rt(n);o!==s&&Or(t,e,r,o,i);for(let a=Qe;a<n.length;a++){let l=n[a];sa(l[F],l,e,t,r,o)}}function u2(e,t,n,r,i){if(t)i?e.addClass(n,r):e.removeClass(n,r);else{let o=r.indexOf("-")===-1?void 0:qt.DashCase;i==null?e.removeStyle(n,r,o):(typeof i=="string"&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=qt.Important),e.setStyle(n,r,i,o))}}function d2(e,t,n){e.setAttribute(t,"style",n)}function mm(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function ym(e,t,n){let{mergedAttrs:r,classes:i,styles:o}=n;r!==null&&Ic(e,t,r),i!==null&&mm(e,t,i),o!==null&&d2(e,t,o)}var Yt={};function H(e=1){vm(ze(),ne(),ir()+e,!1)}function vm(e,t,n,r){if(!r)if((t[P]&3)===3){let o=e.preOrderCheckHooks;o!==null&&ps(t,o,n)}else{let o=e.preOrderHooks;o!==null&&gs(t,o,0,n)}Yn(n)}function U(e,t=j.Default){let n=ne();if(n===null)return M(e,t);let r=Ye();return Hg(r,n,Re(e),t)}function Cm(){let e="invalid";throw new Error(e)}function Dm(e,t,n,r,i,o){let s=Y(null);try{let a=null;i&Cn.SignalBased&&(a=t[r][dn]),a!==null&&a.transformFn!==void 0&&(o=a.transformFn(o)),i&Cn.HasDecoratorInputTransform&&(o=e.inputTransforms[r].call(t,o)),e.setInput!==null?e.setInput(t,a,o,n,r):fg(t,a,r,o)}finally{Y(s)}}function f2(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let i=n[r];if(i<0)Yn(~i);else{let o=i,s=n[++r],a=n[++r];KC(s,o);let l=t[o];a(2,l)}}}finally{Yn(-1)}}function aa(e,t,n,r,i,o,s,a,l,c,u){let d=t.blueprint.slice();return d[Wt]=i,d[P]=r|4|128|8|64,(c!==null||e&&e[P]&2048)&&(d[P]|=2048),Cg(d),d[Pe]=d[qr]=e,d[Nt]=n,d[Ot]=s||e&&e[Ot],d[Ne]=a||e&&e[Ne],d[Br]=l||e&&e[Br]||null,d[yt]=o,d[Js]=OD(),d[ws]=u,d[cg]=c,d[Pt]=t.type==2?e[Pt]:d,d}function la(e,t,n,r,i){let o=e.data[t];if(o===null)o=h2(e,t,n,r,i),YC()&&(o.flags|=32);else if(o.type&64){o.type=n,o.value=r,o.attrs=i;let s=qC();o.injectorIndex=s===null?-1:s.injectorIndex}return Oi(o,!0),o}function h2(e,t,n,r,i){let o=Eg(),s=Mg(),a=s?o:o&&o.parent,l=e.data[t]=C2(e,a,n,t,r,i);return e.firstChild===null&&(e.firstChild=l),o!==null&&(s?o.child==null&&l.parent!==null&&(o.child=l):o.next===null&&(o.next=l,l.prev=o)),l}function _m(e,t,n,r){if(n===0)return-1;let i=t.length;for(let o=0;o<n;o++)t.push(r),e.blueprint.push(r),e.data.push(null);return i}function wm(e,t,n,r,i){let o=ir(),s=r&2;try{Yn(-1),s&&t.length>kt&&vm(e,t,kt,!1),xt(s?2:0,i),n(r,i)}finally{Yn(o),xt(s?3:1,i)}}function bm(e,t,n){if(dg(t)){let r=Y(null);try{let i=t.directiveStart,o=t.directiveEnd;for(let s=i;s<o;s++){let a=e.data[s];if(a.contentQueries){let l=n[s];a.contentQueries(1,l,s)}}}finally{Y(r)}}}function Em(e,t,n){bg()&&(I2(e,t,n,st(n,t)),(n.flags&64)===64&&Tm(e,t,n))}function Mm(e,t,n=st){let r=t.localNames;if(r!==null){let i=t.index+1;for(let o=0;o<r.length;o+=2){let s=r[o+1],a=s===-1?n(t,e):e[s];e[i++]=a}}}function Im(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Zu(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Zu(e,t,n,r,i,o,s,a,l,c,u){let d=kt+r,g=d+i,f=p2(d,g),m=typeof c=="function"?c():c;return f[F]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:g,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof o=="function"?o():o,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:l,consts:m,incompleteFirstPass:!1,ssrId:u}}function p2(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Yt);return n}function g2(e,t,n,r){let o=r.get(jD,rm)||n===At.ShadowDom,s=e.selectRootElement(t,o);return m2(s),s}function m2(e){y2(e)}var y2=()=>null;function v2(e,t,n,r){let i=Om(t);i.push(n),e.firstCreatePass&&Pm(e).push(r,i.length-1)}function C2(e,t,n,r,i,o){let s=t?t.injectorIndex:-1,a=0;return HC()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function Dp(e,t,n,r,i){for(let o in t){if(!t.hasOwnProperty(o))continue;let s=t[o];if(s===void 0)continue;r??={};let a,l=Cn.None;Array.isArray(s)?(a=s[0],l=s[1]):a=s;let c=o;if(i!==null){if(!i.hasOwnProperty(o))continue;c=i[o]}e===0?_p(r,n,c,a,l):_p(r,n,c,a)}return r}function _p(e,t,n,r,i){let o;e.hasOwnProperty(n)?(o=e[n]).push(t,r):o=e[n]=[t,r],i!==void 0&&o.push(i)}function D2(e,t,n){let r=t.directiveStart,i=t.directiveEnd,o=e.data,s=t.attrs,a=[],l=null,c=null;for(let u=r;u<i;u++){let d=o[u],g=n?n.get(d):null,f=g?g.inputs:null,m=g?g.outputs:null;l=Dp(0,d.inputs,u,l,f),c=Dp(1,d.outputs,u,c,m);let v=l!==null&&s!==null&&!Du(t)?L2(l,u,s):null;a.push(v)}l!==null&&(l.hasOwnProperty("class")&&(t.flags|=8),l.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=l,t.outputs=c}function _2(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function w2(e,t,n,r,i,o,s,a){let l=st(t,n),c=t.inputs,u;!a&&c!=null&&(u=c[r])?(Qu(e,n,u,r,i),ea(t)&&b2(n,t.index)):t.type&3?(r=_2(r),i=s!=null?s(i,t.value||"",r):i,o.setProperty(l,r,i)):t.type&12}function b2(e,t){let n=En(t,e);n[P]&16||(n[P]|=64)}function Sm(e,t,n,r){if(bg()){let i=r===null?null:{"":-1},o=x2(e,n),s,a;o===null?s=a=null:[s,a]=o,s!==null&&xm(e,t,n,s,i,a),i&&T2(n,r,i)}n.mergedAttrs=Mi(n.mergedAttrs,n.attrs)}function xm(e,t,n,r,i,o){for(let c=0;c<r.length;c++)Fc(As(n,t),e,r[c].type);N2(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let u=r[c];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,l=_m(e,t,r.length,null);for(let c=0;c<r.length;c++){let u=r[c];n.mergedAttrs=Mi(n.mergedAttrs,u.hostAttrs),O2(e,n,t,l,u),A2(l,u,i),u.contentQueries!==null&&(n.flags|=4),(u.hostBindings!==null||u.hostAttrs!==null||u.hostVars!==0)&&(n.flags|=64);let d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}D2(e,n,o)}function E2(e,t,n,r,i){let o=i.hostBindings;if(o){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;M2(s)!=a&&s.push(a),s.push(n,r,o)}}function M2(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function I2(e,t,n,r){let i=n.directiveStart,o=n.directiveEnd;ea(n)&&P2(t,n,e.data[i+n.componentOffset]),e.firstCreatePass||As(n,t),Jn(r,t);let s=n.initialInputs;for(let a=i;a<o;a++){let l=e.data[a],c=Xn(t,e,a,n);if(Jn(c,t),s!==null&&F2(t,a-i,c,l,n,s),_n(l)){let u=En(n.index,t);u[Nt]=Xn(t,e,a,n)}}}function Tm(e,t,n){let r=n.directiveStart,i=n.directiveEnd,o=n.index,s=XC();try{Yn(o);for(let a=r;a<i;a++){let l=e.data[a],c=t[a];Pc(a),(l.hostBindings!==null||l.hostVars!==0||l.hostAttrs!==null)&&S2(l,c)}}finally{Yn(-1),Pc(s)}}function S2(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function x2(e,t){let n=e.directiveRegistry,r=null,i=null;if(n)for(let o=0;o<n.length;o++){let s=n[o];if(lC(t,s.selectors,!1))if(r||(r=[]),_n(s))if(s.findHostDirectiveDefs!==null){let a=[];i=i||new Map,s.findHostDirectiveDefs(s,a,i),r.unshift(...a,s);let l=a.length;qc(e,t,l)}else r.unshift(s),qc(e,t,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return r===null?null:[r,i]}function qc(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function T2(e,t,n){if(t){let r=e.localNames=[];for(let i=0;i<t.length;i+=2){let o=n[t[i+1]];if(o==null)throw new D(-301,!1);r.push(t[i],o)}}}function A2(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;_n(t)&&(n[""]=e)}}function N2(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function O2(e,t,n,r,i){e.data[r]=i;let o=i.factory||(i.factory=qn(i.type,!0)),s=new Kn(o,_n(i),U);e.blueprint[r]=s,n[r]=s,E2(e,t,r,_m(e,n,i.hostVars,Yt),i)}function P2(e,t,n){let r=st(t,e),i=Im(n),o=e[Ot].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=ca(e,aa(e,i,null,s,r,t,null,o.createRenderer(r,n),null,null,null));e[t.index]=a}function k2(e,t,n,r,i,o){let s=st(e,t);R2(t[Ne],s,o,e.value,n,r,i)}function R2(e,t,n,r,i,o,s){if(o==null)e.removeAttribute(t,i,n);else{let a=s==null?Fr(o):s(o,r||"",i);e.setAttribute(t,i,a,n)}}function F2(e,t,n,r,i,o){let s=o[t];if(s!==null)for(let a=0;a<s.length;){let l=s[a++],c=s[a++],u=s[a++],d=s[a++];Dm(r,n,l,c,u,d)}}function L2(e,t,n){let r=null,i=0;for(;i<n.length;){let o=n[i];if(o===0){i+=4;continue}else if(o===5){i+=2;continue}if(typeof o=="number")break;if(e.hasOwnProperty(o)){r===null&&(r=[]);let s=e[o];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(o,s[a+1],s[a+2],n[i+1]);break}}i+=2}return r}function Am(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Nm(e,t){let n=e.contentQueries;if(n!==null){let r=Y(null);try{for(let i=0;i<n.length;i+=2){let o=n[i],s=n[i+1];if(s!==-1){let a=e.data[s];Su(o),a.contentQueries(2,t[s],s)}}}finally{Y(r)}}}function ca(e,t){return e[Si]?e[ap][gt]=t:e[Si]=t,e[ap]=t,t}function Wc(e,t,n){Su(0);let r=Y(null);try{t(e,n)}finally{Y(r)}}function Om(e){return e[bs]??=[]}function Pm(e){return e.cleanup??=[]}function km(e,t){let n=e[Br],r=n?n.get(Gt,null):null;r&&r.handleError(t)}function Qu(e,t,n,r,i){for(let o=0;o<n.length;){let s=n[o++],a=n[o++],l=n[o++],c=t[s],u=e.data[s];Dm(u,c,r,a,l,i)}}function Rm(e,t,n){let r=yg(t,e);ZD(e[Ne],r,n)}function V2(e,t){let n=En(t,e),r=n[F];j2(r,n);let i=n[Wt];i!==null&&n[ws]===null&&(n[ws]=Uu(i,n[Br])),Yu(r,n,n[Nt])}function j2(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Yu(e,t,n){xu(t);try{let r=e.viewQuery;r!==null&&Wc(1,r,n);let i=e.template;i!==null&&wm(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[zt]?.finishViewCreation(e),e.staticContentQueries&&Nm(e,t),e.staticViewQueries&&Wc(2,e.viewQuery,n);let o=e.components;o!==null&&B2(t,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[P]&=-5,Tu()}}function B2(e,t){for(let n=0;n<t.length;n++)V2(e,t[n])}function U2(e,t,n,r){let i=Y(null);try{let o=t.tView,a=e[P]&4096?4096:16,l=aa(e,o,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];l[Wn]=c;let u=e[zt];return u!==null&&(l[zt]=u.createEmbeddedView(o)),Yu(o,l,n),l}finally{Y(i)}}function wp(e,t){return!t||t.firstChild===null||Yg(e)}function $2(e,t,n,r=!0){let i=t[F];if(XD(i,t,e,n),r){let s=Gc(n,e),a=t[Ne],l=pm(a,e[Zn]);l!==null&&YD(i,e[yt],a,t,l,s)}let o=t[ws];o!==null&&o.firstChild!==null&&(o.firstChild=null)}function Fs(e,t,n,r,i=!1){for(;n!==null;){if(n.type===128){n=i?n.projectionNext:n.next;continue}let o=t[n.index];o!==null&&r.push(Rt(o)),Zt(o)&&H2(o,r);let s=n.type;if(s&8)Fs(e,t,n.child,r);else if(s&32){let a=zu(n,t),l;for(;l=a();)r.push(l)}else if(s&16){let a=gm(t,n);if(Array.isArray(a))r.push(...a);else{let l=Qn(t[Pt]);Fs(l[F],l,a,r,!0)}}n=i?n.projectionNext:n.next}return r}function H2(e,t){for(let n=Qe;n<e.length;n++){let r=e[n],i=r[F].firstChild;i!==null&&Fs(r[F],r,i,t)}e[Zn]!==e[Wt]&&t.push(e[Zn])}var Fm=[];function z2(e){return e[it]??G2(e)}function G2(e){let t=Fm.pop()??Object.create(W2);return t.lView=e,t}function q2(e){e.lView[it]!==e&&(e.lView=null,Fm.push(e))}var W2=q(w({},yi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{na(e.lView)},consumerOnSignalRead(){this.lView[it]=this}});function Z2(e){let t=e[it]??Object.create(Q2);return t.lView=e,t}var Q2=q(w({},yi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let t=Qn(e.lView);for(;t&&!Lm(t[F]);)t=Qn(t);t&&Dg(t)},consumerOnSignalRead(){this.lView[it]=this}});function Lm(e){return e.type!==2}var Y2=100;function Vm(e,t=!0,n=0){let r=e[Ot],i=r.rendererFactory,o=!1;o||i.begin?.();try{K2(e,n)}catch(s){throw t&&km(e,s),s}finally{o||(i.end?.(),r.inlineEffectRunner?.flush())}}function K2(e,t){let n=Ig();try{cp(!0),Zc(e,t);let r=0;for(;ta(e);){if(r===Y2)throw new D(103,!1);r++,Zc(e,1)}}finally{cp(n)}}function X2(e,t,n,r){let i=t[P];if((i&256)===256)return;let o=!1,s=!1;!o&&t[Ot].inlineEffectRunner?.flush(),xu(t);let a=!0,l=null,c=null;o||(Lm(e)?(c=z2(t),l=Ro(c)):hh()===null?(a=!1,c=Z2(t),l=Ro(c)):t[it]&&(Gl(t[it]),t[it]=null));try{Cg(t),QC(e.bindingStartIndex),n!==null&&wm(e,t,n,2,r);let u=(i&3)===3;if(!o)if(u){let f=e.preOrderCheckHooks;f!==null&&ps(t,f,null)}else{let f=e.preOrderHooks;f!==null&&gs(t,f,0,null),fc(t,0)}if(s||J2(t),jm(t,0),e.contentQueries!==null&&Nm(e,t),!o)if(u){let f=e.contentCheckHooks;f!==null&&ps(t,f)}else{let f=e.contentHooks;f!==null&&gs(t,f,1),fc(t,1)}f2(e,t);let d=e.components;d!==null&&Um(t,d,0);let g=e.viewQuery;if(g!==null&&Wc(2,g,r),!o)if(u){let f=e.viewCheckHooks;f!==null&&ps(t,f)}else{let f=e.viewHooks;f!==null&&gs(t,f,2),fc(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[dc]){for(let f of t[dc])f();t[dc]=null}o||(t[P]&=-73)}catch(u){throw o||na(t),u}finally{c!==null&&(Hl(c,l),a&&q2(c)),Tu()}}function jm(e,t){for(let n=Xg(e);n!==null;n=Jg(n))for(let r=Qe;r<n.length;r++){let i=n[r];Bm(i,t)}}function J2(e){for(let t=Xg(e);t!==null;t=Jg(t)){if(!(t[P]&Ms.HasTransplantedViews))continue;let n=t[Ur];for(let r=0;r<n.length;r++){let i=n[r];Dg(i)}}}function e_(e,t,n){let r=En(t,e);Bm(r,n)}function Bm(e,t){Eu(e)&&Zc(e,t)}function Zc(e,t){let r=e[F],i=e[P],o=e[it],s=!!(t===0&&i&16);if(s||=!!(i&64&&t===0),s||=!!(i&1024),s||=!!(o?.dirty&&zl(o)),s||=!1,o&&(o.dirty=!1),e[P]&=-9217,s)X2(r,e,r.template,e[Nt]);else if(i&8192){jm(e,1);let a=r.components;a!==null&&Um(e,a,1)}}function Um(e,t,n){for(let r=0;r<t.length;r++)e_(e,t[r],n)}function Ku(e,t){let n=Ig()?64:1088;for(e[Ot].changeDetectionScheduler?.notify(t);e;){e[P]|=n;let r=Qn(e);if(Ac(e)&&!r)return e;e=r}return null}var er=class{get rootNodes(){let t=this._lView,n=t[F];return Fs(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[Nt]}set context(t){this._lView[Nt]=t}get destroyed(){return(this._lView[P]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Pe];if(Zt(t)){let n=t[Es],r=n?n.indexOf(this):-1;r>-1&&(zc(t,r),Ds(n,r))}this._attachedToViewContainer=!1}fm(this._lView[F],this._lView)}onDestroy(t){_g(this._lView,t)}markForCheck(){Ku(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[P]&=-129}reattach(){Oc(this._lView),this._lView[P]|=128}detectChanges(){this._lView[P]|=1024,Vm(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new D(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Ac(this._lView),n=this._lView[Wn];n!==null&&!t&&Gu(n,this._lView),um(this._lView[F],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new D(902,!1);this._appRef=t;let n=Ac(this._lView),r=this._lView[Wn];r!==null&&!n&&dm(r,this._lView),Oc(this._lView)}},tr=(()=>{class e{static{this.__NG_ELEMENT_ID__=r_}}return e})(),t_=tr,n_=class extends t_{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let i=U2(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new er(i)}};function r_(){return Xu(Ye(),ne())}function Xu(e,t){return e.type&4?new n_(t,e,Zr(e,t)):null}var yA=new RegExp(`^(\\d+)*(${LD}|${FD})*(.*)`);var i_=()=>null;function bp(e,t){return i_(e,t)}var $r=class{},Ju=new E("",{providedIn:"root",factory:()=>!1});var $m=new E(""),Hm=new E(""),Qc=class{},Ls=class{};function o_(e){let t=Error(`No component factory found for ${Fe(e)}.`);return t[s_]=e,t}var s_="ngComponent";var Yc=class{resolveComponentFactory(t){throw o_(t)}},Hr=class{static{this.NULL=new Yc}},wn=class{},Qr=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>a_()}}return e})();function a_(){let e=ne(),t=Ye(),n=En(t.index,e);return(vn(n)?n:e)[Ne]}var l_=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>null})}}return e})();function Kc(e,t,n){let r=n?e.styles:null,i=n?e.classes:null,o=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")o=a;else if(o==1)i=Kh(i,a);else if(o==2){let l=a,c=t[++s];r=Kh(r,l+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=i:e.classesWithoutHost=i}var Vs=class extends Hr{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Dn(t);return new zr(n,this.ngModule)}};function Ep(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let i=e[r];if(i===void 0)continue;let o=Array.isArray(i),s=o?i[0]:i,a=o?i[1]:Cn.None;t?n.push({propName:s,templateName:r,isSignal:(a&Cn.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function c_(e){let t=e.toLowerCase();return t==="svg"?mg:t==="math"?kC:null}var zr=class extends Ls{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=Ep(t.inputs,!0);if(n!==null)for(let i of r)n.hasOwnProperty(i.propName)&&(i.transform=n[i.propName]);return r}get outputs(){return Ep(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=fC(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,i){let o=Y(null);try{i=i||this.ngModule;let s=i instanceof Le?i:i?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new kc(t,s):t,l=a.get(wn,null);if(l===null)throw new D(407,!1);let c=a.get(l_,null),u=a.get($r,null),d={rendererFactory:l,sanitizer:c,inlineEffectRunner:null,changeDetectionScheduler:u},g=l.createRenderer(null,this.componentDef),f=this.componentDef.selectors[0][0]||"div",m=r?g2(g,r,this.componentDef.encapsulation,a):cm(g,f,c_(f)),v=512;this.componentDef.signals?v|=4096:this.componentDef.onPush||(v|=16);let _=null;m!==null&&(_=Uu(m,a,!0));let S=Zu(0,null,null,1,0,null,null,null,null,null,null),V=aa(null,S,null,v,null,null,d,g,a,null,_);xu(V);let N,G,he=null;try{let te=this.componentDef,le,Ie=null;te.findHostDirectiveDefs?(le=[],Ie=new Map,te.findHostDirectiveDefs(te,le,Ie),le.push(te)):le=[te];let Bt=u_(V,m);he=d_(Bt,m,te,le,V,d,g),G=vg(S,kt),m&&p_(g,te,m,r),n!==void 0&&g_(G,this.ngContentSelectors,n),N=h_(he,te,le,Ie,V,[m_]),Yu(S,V,null)}catch(te){throw he!==null&&$c(he),$c(V),te}finally{Tu()}return new Xc(this.componentType,N,Zr(G,V),V,G)}finally{Y(o)}}},Xc=class extends Qc{constructor(t,n,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new er(i,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,i;if(r!==null&&(i=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView;Qu(o[F],o,i,t,n),this.previousInputValues.set(t,n);let s=En(this._tNode.index,o);Ku(s,1)}}get injector(){return new Gn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function u_(e,t){let n=e[F],r=kt;return e[r]=t,la(n,r,2,"#host",null)}function d_(e,t,n,r,i,o,s){let a=i[F];f_(r,e,t,s);let l=null;t!==null&&(l=Uu(t,i[Br]));let c=o.rendererFactory.createRenderer(t,n),u=16;n.signals?u=4096:n.onPush&&(u=64);let d=aa(i,Im(n),null,u,i[e.index],e,o,c,null,null,l);return a.firstCreatePass&&qc(a,e,r.length-1),ca(i,d),i[e.index]=d}function f_(e,t,n,r){for(let i of e)t.mergedAttrs=Mi(t.mergedAttrs,i.hostAttrs);t.mergedAttrs!==null&&(Kc(t,t.mergedAttrs,!0),n!==null&&ym(r,n,t))}function h_(e,t,n,r,i,o){let s=Ye(),a=i[F],l=st(s,i);xm(a,i,s,n,null,r);for(let u=0;u<n.length;u++){let d=s.directiveStart+u,g=Xn(i,a,d,s);Jn(g,i)}Tm(a,i,s),l&&Jn(l,i);let c=Xn(i,a,s.directiveStart+s.componentOffset,s);if(e[Nt]=i[Nt]=c,o!==null)for(let u of o)u(c,t);return bm(a,s,i),c}function p_(e,t,n,r){if(r)Ic(e,n,["ng-version","18.2.13"]);else{let{attrs:i,classes:o}=hC(t.selectors[0]);i&&Ic(e,n,i),o&&o.length>0&&mm(e,n,o.join(" "))}}function g_(e,t,n){let r=e.projection=[];for(let i=0;i<t.length;i++){let o=n[i];r.push(o!=null?Array.from(o):null)}}function m_(){let e=Ye();Ou(ne()[F],e)}var Mn=(()=>{class e{static{this.__NG_ELEMENT_ID__=y_}}return e})();function y_(){let e=Ye();return Gm(e,ne())}var v_=Mn,zm=class extends v_{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Zr(this._hostTNode,this._hostLView)}get injector(){return new Gn(this._hostTNode,this._hostLView)}get parentInjector(){let t=Pu(this._hostTNode,this._hostLView);if(Lg(t)){let n=xs(t,this._hostLView),r=Ss(t),i=n[F].data[r+8];return new Gn(i,n)}else return new Gn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Mp(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Qe}createEmbeddedView(t,n,r){let i,o;typeof r=="number"?i=r:r!=null&&(i=r.index,o=r.injector);let s=bp(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},o,s);return this.insertImpl(a,i,wp(this._hostTNode,s)),a}createComponent(t,n,r,i,o){let s=t&&!AC(t),a;if(s)a=n;else{let m=n||{};a=m.index,r=m.injector,i=m.projectableNodes,o=m.environmentInjector||m.ngModuleRef}let l=s?t:new zr(Dn(t)),c=r||this.parentInjector;if(!o&&l.ngModule==null){let v=(s?c:this.parentInjector).get(Le,null);v&&(o=v)}let u=Dn(l.componentType??{}),d=bp(this._lContainer,u?.id??null),g=d?.firstChild??null,f=l.create(c,i,g,o);return this.insertImpl(f.hostView,a,wp(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let i=t._lView;if(LC(i)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let l=i[Pe],c=new zm(l,l[yt],l[Pe]);c.detach(c.indexOf(t))}}let o=this._adjustIndex(n),s=this._lContainer;return $2(s,i,o,r),t.attachToViewContainerRef(),qp(yc(s),o,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Mp(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=zc(this._lContainer,n);r&&(Ds(yc(this._lContainer),n),fm(r[F],r))}detach(t){let n=this._adjustIndex(t,-1),r=zc(this._lContainer,n);return r&&Ds(yc(this._lContainer),n)!=null?new er(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Mp(e){return e[Es]}function yc(e){return e[Es]||(e[Es]=[])}function Gm(e,t){let n,r=t[e.index];return Zt(r)?n=r:(n=Am(r,t,null,e),t[e.index]=n,ca(t,n)),D_(n,t,e,r),new zm(n,e,t)}function C_(e,t){let n=e[Ne],r=n.createComment(""),i=st(t,e),o=pm(n,i);return Rs(n,o,r,r2(n,i),!1),r}var D_=b_,__=()=>!1;function w_(e,t,n){return __(e,t,n)}function b_(e,t,n,r){if(e[Zn])return;let i;n.type&8?i=Rt(r):i=C_(t,n),e[Zn]=i}var Jc=class e{constructor(t){this.queryList=t,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},eu=class e{constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,i=[];for(let o=0;o<r;o++){let s=n.getByIndex(o),a=this.queries[s.indexInDeclarationView];i.push(a.clone())}return new e(i)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)ed(t,n).matches!==null&&this.queries[n].setDirty()}},tu=class{constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=N_(t):this.predicate=t}},nu=class e{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let i=n!==null?n.length:0,o=this.getByIndex(r).embeddedTView(t,i);o&&(o.indexInDeclarationView=r,n!==null?n.push(o):n=[o])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},ru=class e{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){let o=r[i];this.matchTNodeWithReadOption(t,n,E_(n,o)),this.matchTNodeWithReadOption(t,n,ms(n,t,o,!1,!1))}else r===tr?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,ms(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let i=this.metadata.read;if(i!==null)if(i===at||i===Mn||i===tr&&n.type&4)this.addMatch(n.index,-2);else{let o=ms(n,t,i,!1,!1);o!==null&&this.addMatch(n.index,o)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function E_(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function M_(e,t){return e.type&11?Zr(e,t):e.type&4?Xu(e,t):null}function I_(e,t,n,r){return n===-1?M_(t,e):n===-2?S_(e,t,r):Xn(e,e[F],n,t)}function S_(e,t,n){if(n===at)return Zr(t,e);if(n===tr)return Xu(t,e);if(n===Mn)return Gm(t,e)}function qm(e,t,n,r){let i=t[zt].queries[r];if(i.matches===null){let o=e.data,s=n.matches,a=[];for(let l=0;s!==null&&l<s.length;l+=2){let c=s[l];if(c<0)a.push(null);else{let u=o[c];a.push(I_(t,u,s[l+1],n.metadata.read))}}i.matches=a}return i.matches}function iu(e,t,n,r){let i=e.queries.getByIndex(n),o=i.matches;if(o!==null){let s=qm(e,t,i,n);for(let a=0;a<o.length;a+=2){let l=o[a];if(l>0)r.push(s[a/2]);else{let c=o[a+1],u=t[-l];for(let d=Qe;d<u.length;d++){let g=u[d];g[Wn]===g[Pe]&&iu(g[F],g,c,r)}if(u[Ur]!==null){let d=u[Ur];for(let g=0;g<d.length;g++){let f=d[g];iu(f[F],f,c,r)}}}}}return r}function x_(e,t){return e[zt].queries[t].queryList}function T_(e,t,n){let r=new Uc((n&4)===4);return v2(e,t,r,r.destroy),(t[zt]??=new eu).queries.push(new Jc(r))-1}function A_(e,t,n){let r=ze();return r.firstCreatePass&&(O_(r,new tu(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),T_(r,ne(),t)}function N_(e){return e.split(",").map(t=>t.trim())}function O_(e,t,n){e.queries===null&&(e.queries=new nu),e.queries.track(new ru(t,n))}function ed(e,t){return e.queries.getByIndex(t)}function P_(e,t){let n=e[F],r=ed(n,t);return r.crossesNgTemplate?iu(n,e,t,[]):qm(n,e,r,t)}var Ip=new Set;function In(e){Ip.has(e)||(Ip.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function ki(e,t){In("NgSignals");let n=Eh(e),r=n[dn];return t?.equal&&(r.equal=t.equal),n.set=i=>ql(r,i),n.update=i=>Mh(r,i),n.asReadonly=k_.bind(n),n}function k_(){let e=this[dn];if(e.readonlyFn===void 0){let t=()=>this();t[dn]=e,e.readonlyFn=t}return e.readonlyFn}function R_(e){let t=[],n=new Map;function r(i){let o=n.get(i);if(!o){let s=e(i);n.set(i,o=s.then(j_))}return o}return js.forEach((i,o)=>{let s=[];i.templateUrl&&s.push(r(i.templateUrl).then(c=>{i.template=c}));let a=typeof i.styles=="string"?[i.styles]:i.styles||[];if(i.styles=a,i.styleUrl&&i.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(i.styleUrls?.length){let c=i.styles.length,u=i.styleUrls;i.styleUrls.forEach((d,g)=>{a.push(""),s.push(r(d).then(f=>{a[c+g]=f,u.splice(u.indexOf(d),1),u.length==0&&(i.styleUrls=void 0)}))})}else i.styleUrl&&s.push(r(i.styleUrl).then(c=>{a.push(c),i.styleUrl=void 0}));let l=Promise.all(s).then(()=>B_(o));t.push(l)}),L_(),Promise.all(t).then(()=>{})}var js=new Map,F_=new Set;function L_(){let e=js;return js=new Map,e}function V_(){return js.size===0}function j_(e){return typeof e=="string"?e:e.text()}function B_(e){F_.delete(e)}function U_(e){return Object.getPrototypeOf(e.prototype).constructor}function ua(e){let t=U_(e.type),n=!0,r=[e];for(;t;){let i;if(_n(e))i=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new D(903,!1);i=t.\u0275dir}if(i){if(n){r.push(i);let s=e;s.inputs=cs(e.inputs),s.inputTransforms=cs(e.inputTransforms),s.declaredInputs=cs(e.declaredInputs),s.outputs=cs(e.outputs);let a=i.hostBindings;a&&q_(e,a);let l=i.viewQuery,c=i.contentQueries;if(l&&z_(e,l),c&&G_(e,c),$_(e,i),Nv(e.outputs,i.outputs),_n(i)&&i.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(i.data.animation)}}let o=i.features;if(o)for(let s=0;s<o.length;s++){let a=o[s];a&&a.ngInherit&&a(e),a===ua&&(n=!1)}}t=Object.getPrototypeOf(t)}H_(r)}function $_(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let i=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(i))continue;e.inputTransforms??={},e.inputTransforms[i]=t.inputTransforms[i]}}}function H_(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let i=e[r];i.hostVars=t+=i.hostVars,i.hostAttrs=Mi(i.hostAttrs,n=Mi(n,i.hostAttrs))}}function cs(e){return e===Lr?{}:e===rt?[]:e}function z_(e,t){let n=e.viewQuery;n?e.viewQuery=(r,i)=>{t(r,i),n(r,i)}:e.viewQuery=t}function G_(e,t){let n=e.contentQueries;n?e.contentQueries=(r,i,o)=>{t(r,i,o),n(r,i,o)}:e.contentQueries=t}function q_(e,t){let n=e.hostBindings;n?e.hostBindings=(r,i)=>{t(r,i),n(r,i)}:e.hostBindings=t}function td(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let i=t[r];Array.isArray(i)&&i[3]&&(n[r]=i[3])}e.inputTransforms=n}var bn=class{},xi=class{};var Bs=class extends bn{constructor(t,n,r,i=!0){super(),this.ngModuleType=t,this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Vs(this);let o=tg(t);this._bootstrapComponents=lm(o.bootstrap),this._r3Injector=qg(t,n,[{provide:bn,useValue:this},{provide:Hr,useValue:this.componentFactoryResolver},...r],Fe(t),new Set(["environment"])),i&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Us=class extends xi{constructor(t){super(),this.moduleType=t}create(t){return new Bs(this.moduleType,t,[])}};function W_(e,t,n){return new Bs(e,t,n,!1)}var ou=class extends bn{constructor(t){super(),this.componentFactoryResolver=new Vs(this),this.instance=null;let n=new Ii([...t.providers,{provide:bn,useValue:this},{provide:Hr,useValue:this.componentFactoryResolver}],t.parent||wu(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function da(e,t,n=null){return new ou({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}function Wm(e){return Q_(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Z_(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Q_(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Zm(e,t,n){return e[t]=n}function nr(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Qm(e,t,n,r){let i=nr(e,t,n);return nr(e,t+1,r)||i}function Y_(e){return(e.flags&32)===32}function K_(e,t,n,r,i,o,s,a,l){let c=t.consts,u=la(t,e,4,s||null,a||null);Sm(t,n,u,Is(c,l)),Ou(t,u);let d=u.tView=Zu(2,u,r,i,o,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function X_(e,t,n,r,i,o,s,a,l,c){let u=n+kt,d=t.firstCreatePass?K_(u,t,e,r,i,o,s,a,l):t.data[u];Oi(d,!1);let g=J_(t,e,d,n);Au()&&qu(t,e,g,d),Jn(g,e);let f=Am(g,e,g,d);return e[u]=f,ca(e,f),w_(f,d,e),bu(d)&&Em(t,e,d),l!=null&&Mm(e,d,c),d}function Ee(e,t,n,r,i,o,s,a){let l=ne(),c=ze(),u=Is(c.consts,o);return X_(l,c,e,t,n,r,i,u,s,a),Ee}var J_=ew;function ew(e,t,n,r){return Nu(!0),t[Ne].createComment("")}var Pr=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Pr||{}),Ym=(()=>{class e{constructor(){this.impl=null}execute(){this.impl?.execute()}static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}}return e})(),su=class e{constructor(){this.ngZone=C(K),this.scheduler=C($r),this.errorHandler=C(Gt,{optional:!0}),this.sequences=new Set,this.deferredRegistrations=new Set,this.executing=!1}static{this.PHASES=[Pr.EarlyRead,Pr.Write,Pr.MixedReadWrite,Pr.Read]}execute(){this.executing=!0;for(let t of e.PHASES)for(let n of this.sequences)if(!(n.erroredOrDestroyed||!n.hooks[t]))try{n.pipelinedValue=this.ngZone.runOutsideAngular(()=>n.hooks[t](n.pipelinedValue))}catch(r){n.erroredOrDestroyed=!0,this.errorHandler?.handleError(r)}this.executing=!1;for(let t of this.sequences)t.afterRun(),t.once&&(this.sequences.delete(t),t.destroy());for(let t of this.deferredRegistrations)this.sequences.add(t);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear()}register(t){this.executing?this.deferredRegistrations.add(t):(this.sequences.add(t),this.scheduler.notify(6))}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>new e})}},au=class{constructor(t,n,r,i){this.impl=t,this.hooks=n,this.once=r,this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.()}};function nd(e,t){!t?.injector&&xC(nd);let n=t?.injector??C(He);return GD(n)?(In("NgAfterNextRender"),nw(e,n,t,!0)):rw}function tw(e,t){if(e instanceof Function){let n=[void 0,void 0,void 0,void 0];return n[t]=e,n}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function nw(e,t,n,r){let i=t.get(Ym);i.impl??=t.get(su);let o=n?.phase??Pr.MixedReadWrite,s=n?.manualCleanup!==!0?t.get(ku):null,a=new au(i.impl,tw(e,o),r,s);return i.impl.register(a),a}var rw={destroy(){}};function Ri(e,t,n,r){let i=ne(),o=Iu();if(nr(i,o,t)){let s=ze(),a=kg();k2(a,i,e,t,n,r)}return Ri}function iw(e,t,n,r){return nr(e,Iu(),n)?t+Fr(n)+r:Yt}function ow(e,t,n,r,i,o){let s=ZC(),a=Qm(e,s,n,i);return Sg(2),a?t+Fr(n)+r+Fr(i)+o:Yt}function us(e,t){return e<<17|t<<2}function rr(e){return e>>17&32767}function sw(e){return(e&2)==2}function aw(e,t){return e&131071|t<<17}function lu(e){return e|2}function Gr(e){return(e&131068)>>2}function vc(e,t){return e&-131069|t<<2}function lw(e){return(e&1)===1}function cu(e){return e|1}function cw(e,t,n,r,i,o){let s=o?t.classBindings:t.styleBindings,a=rr(s),l=Gr(s);e[r]=n;let c=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Ni(d,u)>0)&&(c=!0)}else u=n;if(i)if(l!==0){let g=rr(e[a+1]);e[r+1]=us(g,a),g!==0&&(e[g+1]=vc(e[g+1],r)),e[a+1]=aw(e[a+1],r)}else e[r+1]=us(a,0),a!==0&&(e[a+1]=vc(e[a+1],r)),a=r;else e[r+1]=us(l,0),a===0?a=r:e[l+1]=vc(e[l+1],r),l=r;c&&(e[r+1]=lu(e[r+1])),Sp(e,u,r,!0),Sp(e,u,r,!1),uw(t,u,e,r,o),s=us(a,l),o?t.classBindings=s:t.styleBindings=s}function uw(e,t,n,r,i){let o=i?e.residualClasses:e.residualStyles;o!=null&&typeof t=="string"&&Ni(o,t)>=0&&(n[r+1]=cu(n[r+1]))}function Sp(e,t,n,r){let i=e[n+1],o=t===null,s=r?rr(i):Gr(i),a=!1;for(;s!==0&&(a===!1||o);){let l=e[s],c=e[s+1];dw(l,t)&&(a=!0,e[s+1]=r?cu(c):lu(c)),s=r?rr(c):Gr(c)}a&&(e[n+1]=r?lu(i):cu(i))}function dw(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Ni(e,t)>=0:!1}function X(e,t,n){let r=ne(),i=Iu();if(nr(r,i,t)){let o=ze(),s=kg();w2(o,s,r,e,t,r[Ne],n,!1)}return X}function xp(e,t,n,r,i){let o=t.inputs,s=i?"class":"style";Qu(e,n,o[s],s,r)}function rd(e,t,n){return Km(e,t,n,!1),rd}function Dt(e,t){return Km(e,t,null,!0),Dt}function Km(e,t,n,r){let i=ne(),o=ze(),s=Sg(2);if(o.firstUpdatePass&&hw(o,e,s,r),t!==Yt&&nr(i,s,t)){let a=o.data[ir()];vw(o,a,i,i[Ne],e,i[s+1]=Cw(t,n),r,s)}}function fw(e,t){return t>=e.expandoStartIndex}function hw(e,t,n,r){let i=e.data;if(i[n+1]===null){let o=i[ir()],s=fw(e,n);Dw(o,r)&&t===null&&!s&&(t=!1),t=pw(i,o,t,r),cw(i,o,t,n,s,r)}}function pw(e,t,n,r){let i=JC(e),o=r?t.residualClasses:t.residualStyles;if(i===null)(r?t.classBindings:t.styleBindings)===0&&(n=Cc(null,e,t,n,r),n=Ti(n,t.attrs,r),o=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==i)if(n=Cc(i,e,t,n,r),o===null){let l=gw(e,t,r);l!==void 0&&Array.isArray(l)&&(l=Cc(null,e,t,l[1],r),l=Ti(l,t.attrs,r),mw(e,t,r,l))}else o=yw(e,t,r)}return o!==void 0&&(r?t.residualClasses=o:t.residualStyles=o),n}function gw(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Gr(r)!==0)return e[rr(r)]}function mw(e,t,n,r){let i=n?t.classBindings:t.styleBindings;e[rr(i)]=r}function yw(e,t,n){let r,i=t.directiveEnd;for(let o=1+t.directiveStylingLast;o<i;o++){let s=e[o].hostAttrs;r=Ti(r,s,n)}return Ti(r,t.attrs,n)}function Cc(e,t,n,r,i){let o=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(o=t[a],r=Ti(r,o.hostAttrs,i),o!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Ti(e,t,n){let r=n?1:2,i=-1;if(t!==null)for(let o=0;o<t.length;o++){let s=t[o];typeof s=="number"?i=s:i===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Jv(e,s,n?!0:t[++o]))}return e===void 0?null:e}function vw(e,t,n,r,i,o,s,a){if(!(t.type&3))return;let l=e.data,c=l[a+1],u=lw(c)?Tp(l,t,n,i,Gr(c),s):void 0;if(!$s(u)){$s(o)||sw(c)&&(o=Tp(l,null,n,i,a,s));let d=yg(ir(),n);u2(r,s,d,i,o)}}function Tp(e,t,n,r,i,o){let s=t===null,a;for(;i>0;){let l=e[i],c=Array.isArray(l),u=c?l[1]:l,d=u===null,g=n[i+1];g===Yt&&(g=d?rt:void 0);let f=d?cc(g,r):u===r?g:void 0;if(c&&!$s(f)&&(f=cc(l,r)),$s(f)&&(a=f,s))return a;let m=e[i+1];i=s?rr(m):Gr(m)}if(t!==null){let l=o?t.residualClasses:t.residualStyles;l!=null&&(a=cc(l,r))}return a}function $s(e){return e!==void 0}function Cw(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Fe(Pi(e)))),e}function Dw(e,t){return(e.flags&(t?8:16))!==0}function _w(e,t,n,r,i,o){let s=t.consts,a=Is(s,i),l=la(t,e,2,r,a);return Sm(t,n,l,Is(s,o)),l.attrs!==null&&Kc(l,l.attrs,!1),l.mergedAttrs!==null&&Kc(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function h(e,t,n,r){let i=ne(),o=ze(),s=kt+e,a=i[Ne],l=o.firstCreatePass?_w(s,o,i,t,n,r):o.data[s],c=ww(o,i,l,a,t,e);i[s]=c;let u=bu(l);return Oi(l,!0),ym(a,c,l),!Y_(l)&&Au()&&qu(o,i,c,l),BC()===0&&Jn(c,i),UC(),u&&(Em(o,i,l),bm(o,l,i)),r!==null&&Mm(i,l),h}function p(){let e=Ye();Mg()?WC():(e=e.parent,Oi(e,!1));let t=e;zC(t)&&GC(),$C();let n=ze();return n.firstCreatePass&&(Ou(n,e),dg(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&aD(t)&&xp(n,t,ne(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&lD(t)&&xp(n,t,ne(),t.stylesWithoutHost,!1),p}function y(e,t,n,r){return h(e,t,n,r),p(),y}var ww=(e,t,n,r,i,o)=>(Nu(!0),cm(r,i,rD()));var zn=void 0;function bw(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var Ew=["en",[["a","p"],["AM","PM"],zn],[["AM","PM"],zn,zn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],zn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],zn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",zn,"{1} 'at' {0}",zn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",bw],Dc={};function lt(e){let t=Mw(e),n=Ap(t);if(n)return n;let r=t.split("-")[0];if(n=Ap(r),n)return n;if(r==="en")return Ew;throw new D(701,!1)}function Ap(e){return e in Dc||(Dc[e]=ke.ng&&ke.ng.common&&ke.ng.common.locales&&ke.ng.common.locales[e]),Dc[e]}var ye=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ye||{});function Mw(e){return e.toLowerCase().replace(/_/g,"-")}var Hs="en-US";var Iw=Hs;function Sw(e){typeof e=="string"&&(Iw=e.toLowerCase().replace(/_/g,"-"))}var xw=(e,t,n)=>{};function ve(e,t,n,r){let i=ne(),o=ze(),s=Ye();return Aw(o,i,i[Ne],s,e,t,r),ve}function Tw(e,t,n,r){let i=e.cleanup;if(i!=null)for(let o=0;o<i.length-1;o+=2){let s=i[o];if(s===n&&i[o+1]===r){let a=t[bs],l=i[o+2];return a.length>l?a[l]:null}typeof s=="string"&&(o+=2)}return null}function Aw(e,t,n,r,i,o,s){let a=bu(r),c=e.firstCreatePass&&Pm(e),u=t[Nt],d=Om(t),g=!0;if(r.type&3||s){let v=st(r,t),_=s?s(v):v,S=d.length,V=s?G=>s(Rt(G[r.index])):r.index,N=null;if(!s&&a&&(N=Tw(e,t,i,r.index)),N!==null){let G=N.__ngLastListenerFn__||N;G.__ngNextListenerFn__=o,N.__ngLastListenerFn__=o,g=!1}else{o=Op(r,t,u,o),xw(v,i,o);let G=n.listen(_,i,o);d.push(o,G),c&&c.push(i,V,S,S+1)}}else o=Op(r,t,u,o);let f=r.outputs,m;if(g&&f!==null&&(m=f[i])){let v=m.length;if(v)for(let _=0;_<v;_+=2){let S=m[_],V=m[_+1],he=t[S][V].subscribe(o),te=d.length;d.push(o,he),c&&c.push(i,r.index,te,-(te+1))}}}function Np(e,t,n,r){let i=Y(null);try{return xt(6,t,n),n(r)!==!1}catch(o){return km(e,o),!1}finally{xt(7,t,n),Y(i)}}function Op(e,t,n,r){return function i(o){if(o===Function)return r;let s=e.componentOffset>-1?En(e.index,t):t;Ku(s,5);let a=Np(t,n,r,o),l=i.__ngNextListenerFn__;for(;l;)a=Np(t,n,l,o)&&a,l=l.__ngNextListenerFn__;return a}}function Ge(e=1){return tD(e)}function Sn(e,t,n){A_(e,t,n)}function Kt(e){let t=ne(),n=ze(),r=xg();Su(r+1);let i=ed(n,r);if(e.dirty&&FC(t)===((i.metadata.flags&2)===2)){if(i.matches===null)e.reset([]);else{let o=P_(t,r);e.reset(o,TD),e.notifyOnChanges()}return!0}return!1}function Xt(){return x_(ne(),xg())}function Nw(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function O(e,t=""){let n=ne(),r=ze(),i=e+kt,o=r.firstCreatePass?la(r,i,1,t,null):r.data[i],s=Ow(r,n,o,t,e);n[i]=s,Au()&&qu(r,n,s,o),Oi(o,!1)}var Ow=(e,t,n,r,i)=>(Nu(!0),WD(t[Ne],r));function ct(e){return xn("",e,""),ct}function xn(e,t,n){let r=ne(),i=iw(r,e,t,n);return i!==Yt&&Rm(r,ir(),i),xn}function fa(e,t,n,r,i){let o=ne(),s=ow(o,e,t,n,r,i);return s!==Yt&&Rm(o,ir(),s),fa}function Pw(e,t,n){let r=ze();if(r.firstCreatePass){let i=_n(e);uu(n,r.data,r.blueprint,i,!0),uu(t,r.data,r.blueprint,i,!1)}}function uu(e,t,n,r,i){if(e=Re(e),Array.isArray(e))for(let o=0;o<e.length;o++)uu(e[o],t,n,r,i);else{let o=ze(),s=ne(),a=Ye(),l=jr(e)?e:Re(e.provide),c=ag(e),u=a.providerIndexes&1048575,d=a.directiveStart,g=a.providerIndexes>>20;if(jr(e)||!e.multi){let f=new Kn(c,i,U),m=wc(l,t,i?u:u+g,d);m===-1?(Fc(As(a,s),o,l),_c(o,e,t.length),t.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[m]=f,s[m]=f)}else{let f=wc(l,t,u+g,d),m=wc(l,t,u,u+g),v=f>=0&&n[f],_=m>=0&&n[m];if(i&&!_||!i&&!v){Fc(As(a,s),o,l);let S=Fw(i?Rw:kw,n.length,i,r,c);!i&&_&&(n[m].providerFactory=S),_c(o,e,t.length,0),t.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),n.push(S),s.push(S)}else{let S=Xm(n[i?m:f],c,!i&&r);_c(o,e,f>-1?f:m,S)}!i&&r&&_&&n[m].componentProviders++}}}function _c(e,t,n,r){let i=jr(t),o=_C(t);if(i||o){let l=(o?Re(t.useClass):t).prototype.ngOnDestroy;if(l){let c=e.destroyHooks||(e.destroyHooks=[]);if(!i&&t.multi){let u=c.indexOf(n);u===-1?c.push(n,[r,l]):c[u+1].push(r,l)}else c.push(n,l)}}}function Xm(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function wc(e,t,n,r){for(let i=n;i<r;i++)if(t[i]===e)return i;return-1}function kw(e,t,n,r){return du(this.multi,[])}function Rw(e,t,n,r){let i=this.multi,o;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Xn(n,n[F],this.providerFactory.index,r);o=a.slice(0,s),du(i,o);for(let l=s;l<a.length;l++)o.push(a[l])}else o=[],du(i,o);return o}function du(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Fw(e,t,n,r,i){let o=new Kn(e,n,U);return o.multi=[],o.index=t,o.componentProviders=0,Xm(o,i,r&&!n),o}function Jm(e,t=[]){return n=>{n.providersResolver=(r,i)=>Pw(r,i?i(e):e,t)}}var Lw=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=ig(!1,n.type),i=r.length>0?da([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=b({token:e,providedIn:"environment",factory:()=>new e(M(Le))})}}return e})();function e0(e){In("NgStandalone"),e.getStandaloneInjector=t=>t.get(Lw).getOrCreateStandaloneInjector(e)}function t0(e,t,n,r){return Vw(ne(),Mu(),e,t,n,r)}function n0(e,t,n,r,i){return i0(ne(),Mu(),e,t,n,r,i)}function r0(e,t){let n=e[t];return n===Yt?void 0:n}function Vw(e,t,n,r,i,o){let s=t+n;return nr(e,s,i)?Zm(e,s+1,o?r.call(o,i):r(i)):r0(e,s+1)}function i0(e,t,n,r,i,o,s){let a=t+n;return Qm(e,a,i,o)?Zm(e,a+2,s?r.call(s,i,o):r(i,o)):r0(e,a+2)}function Fi(e,t){let n=ze(),r,i=e+kt;n.firstCreatePass?(r=jw(t,n.pipeRegistry),n.data[i]=r,r.onDestroy&&(n.destroyHooks??=[]).push(i,r.onDestroy)):r=n.data[i];let o=r.factory||(r.factory=qn(r.type,!0)),s,a=$e(U);try{let l=Ts(!1),c=o();return Ts(l),Nw(n,ne(),i,c),c}finally{$e(a)}}function jw(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function Li(e,t,n,r){let i=e+kt,o=ne(),s=RC(o,i);return Bw(o,i)?i0(o,Mu(),t,s.transform,n,r,s):s.transform(n,r)}function Bw(e,t){return e[F].data[t].pure}var ds=null;function Uw(e){ds!==null&&(e.defaultEncapsulation!==ds.defaultEncapsulation||e.preserveWhitespaces!==ds.preserveWhitespaces)||(ds=e)}var ha=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var id=new E(""),Vi=new E(""),pa=(()=>{class e{constructor(n,r,i){this._ngZone=n,this.registry=r,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,od||($w(i),i.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{K.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,i){let o=-1;r&&r>0&&(o=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==o),n()},r)),this._callbacks.push({doneCb:n,timeoutId:o,updateCb:i})}whenStable(n,r,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,i),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,i){return[]}static{this.\u0275fac=function(r){return new(r||e)(M(K),M(ga),M(Vi))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),ga=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return od?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function $w(e){od=e}var od;function or(e){return!!e&&typeof e.then=="function"}function o0(e){return!!e&&typeof e.subscribe=="function"}var ma=new E(""),s0=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=C(ma,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let i of this.appInits){let o=i();if(or(o))n.push(o);else if(o0(o)){let s=new Promise((a,l)=>{o.subscribe({complete:a,error:l})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(i=>{this.reject(i)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),ya=new E("");function Hw(){bh(()=>{throw new D(600,!1)})}function zw(e){return e.isBoundToModule}var Gw=10;function qw(e,t,n){try{let r=n();return or(r)?r.catch(i=>{throw t.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function a0(e,t){return Array.isArray(t)?t.reduce(a0,e):w(w({},e),t)}var Jt=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=C(SD),this.afterRenderManager=C(Ym),this.zonelessEnabled=C(Ju),this.dirtyFlags=0,this.deferredDirtyFlags=0,this.externalTestViews=new Set,this.beforeRender=new Ae,this.afterTick=new Ae,this.componentTypes=[],this.components=[],this.isStable=C(Qt).hasPendingTasks.pipe(R(n=>!n)),this._injector=C(Le)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:i=>{i&&r()}})}).finally(()=>{n.unsubscribe()})}get injector(){return this._injector}bootstrap(n,r){let i=n instanceof Ls;if(!this._injector.get(s0).done){let g=!i&&eg(n),f=!1;throw new D(405,f)}let s;i?s=n:s=this._injector.get(Hr).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=zw(s)?void 0:this._injector.get(bn),l=r||s.selector,c=s.create(He.NULL,[],l,a),u=c.location.nativeElement,d=c.injector.get(id,null);return d?.registerApplication(u),c.onDestroy(()=>{this.detachView(c.hostView),ys(this.components,c),d?.unregisterApplication(u)}),this._loadComponent(c),c}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){if(this._runningTick)throw new D(101,!1);let n=Y(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,Y(n),this.afterTick.next()}}synchronize(){let n=null;this._injector.destroyed||(n=this._injector.get(wn,null,{optional:!0})),this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0;let r=0;for(;this.dirtyFlags!==0&&r++<Gw;)this.synchronizeOnce(n)}synchronizeOnce(n){if(this.dirtyFlags|=this.deferredDirtyFlags,this.deferredDirtyFlags=0,this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8,this.beforeRender.next(r);for(let{_lView:i,notifyErrorHandler:o}of this._views)Ww(i,o,r,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&7)return}else n?.begin?.(),n?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ta(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;ys(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(ya,[]);[...this._bootstrapListeners,...r].forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ys(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new D(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function ys(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Ww(e,t,n,r){if(!n&&!ta(e))return;Vm(e,t,n&&!r?0:1)}var fu=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},va=(()=>{class e{compileModuleSync(n){return new Us(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),i=tg(n),o=lm(i.declarations).reduce((s,a)=>{let l=Dn(a);return l&&s.push(new zr(l)),s},[]);return new fu(r,o)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Zw=new E("");function Qw(e,t,n){let r=new Us(n);return Promise.resolve(r)}function Pp(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var Yw=(()=>{class e{constructor(){this.zone=C(K),this.changeDetectionScheduler=C($r),this.applicationRef=C(Jt)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Kw({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new K(q(w({},l0()),{scheduleInRootZone:n})),[{provide:K,useFactory:e},{provide:Vr,multi:!0,useFactory:()=>{let r=C(Yw,{optional:!0});return()=>r.initialize()}},{provide:Vr,multi:!0,useFactory:()=>{let r=C(Xw);return()=>{r.initialize()}}},t===!0?{provide:$m,useValue:!0}:[],{provide:Hm,useValue:n??Wg}]}function l0(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Xw=(()=>{class e{constructor(){this.subscription=new ue,this.initialized=!1,this.zone=C(K),this.pendingTasks=C(Qt)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{K.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{K.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var Jw=(()=>{class e{constructor(){this.appRef=C(Jt),this.taskService=C(Qt),this.ngZone=C(K),this.zonelessEnabled=C(Ju),this.disableScheduling=C($m,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new ue,this.angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Os):null,this.scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(C(Hm,{optional:!0})??!1),this.cancelScheduledCallback=null,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ps||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 7:{this.appRef.deferredDirtyFlags|=8;break}case 9:case 8:case 6:case 10:default:this.appRef.dirtyFlags|=8}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?pp:Zg;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>r(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>r(()=>this.tick()))}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Os+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(n),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,pp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function eb(){return typeof $localize<"u"&&$localize.locale||Hs}var Ca=new E("",{providedIn:"root",factory:()=>C(Ca,j.Optional|j.SkipSelf)||eb()});var zs=new E("");function fs(e){return!e.moduleRef}function tb(e){let t=fs(e)?e.r3Injector:e.moduleRef.injector,n=t.get(K);return n.run(()=>{fs(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Gt,null),i;if(n.runOutsideAngular(()=>{i=n.onError.subscribe({next:o=>{r.handleError(o)}})}),fs(e)){let o=()=>t.destroy(),s=e.platformInjector.get(zs);s.add(o),t.onDestroy(()=>{i.unsubscribe(),s.delete(o)})}else{let o=()=>e.moduleRef.destroy(),s=e.platformInjector.get(zs);s.add(o),e.moduleRef.onDestroy(()=>{ys(e.allPlatformModules,e.moduleRef),i.unsubscribe(),s.delete(o)})}return qw(r,n,()=>{let o=t.get(s0);return o.runInitializers(),o.donePromise.then(()=>{let s=t.get(Ca,Hs);if(Sw(s||Hs),fs(e)){let a=t.get(Jt);return e.rootComponent!==void 0&&a.bootstrap(e.rootComponent),a}else return nb(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function nb(e,t){let n=e.injector.get(Jt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(n);else throw new D(-403,!1);t.push(e)}var c0=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){let i=r?.scheduleInRootZone,o=()=>ID(r?.ngZone,q(w({},l0({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing})),{scheduleInRootZone:i})),s=r?.ignoreChangesOutsideZone,a=[Kw({ngZoneFactory:o,ignoreChangesOutsideZone:s}),{provide:$r,useExisting:Jw}],l=W_(n.moduleType,this.injector,a);return tb({moduleRef:l,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(n,r=[]){let i=a0({},r);return Qw(this.injector,i,n).then(o=>this.bootstrapModuleFactory(o,i))}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new D(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(zs,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(M(He))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),bi=null,u0=new E("");function rb(e){if(bi&&!bi.get(u0,!1))throw new D(400,!1);Hw(),bi=e;let t=e.get(c0);return sb(e),t}function sd(e,t,n=[]){let r=`Platform: ${t}`,i=new E(r);return(o=[])=>{let s=d0();if(!s||s.injector.get(u0,!1)){let a=[...n,...o,{provide:i,useValue:!0}];e?e(a):rb(ib(a,r))}return ob(i)}}function ib(e=[],t){return He.create({name:t,providers:[{provide:Xs,useValue:"platform"},{provide:zs,useValue:new Set([()=>bi=null])},...e]})}function ob(e){let t=d0();if(!t)throw new D(401,!1);return t}function d0(){return bi?.get(c0)??null}function sb(e){e.get(Lu,null)?.forEach(n=>n())}var sr=(()=>{class e{static{this.__NG_ELEMENT_ID__=ab}}return e})();function ab(e){return lb(Ye(),ne(),(e&16)===16)}function lb(e,t,n){if(ea(e)&&!n){let r=En(e.index,t);return new er(r,r)}else if(e.type&175){let r=t[Pt];return new er(r,t)}return null}var hu=class{constructor(){}supports(t){return Wm(t)}create(t){return new pu(t)}},cb=(e,t)=>t,pu=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||cb}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,i=0,o=null;for(;n||r;){let s=!r||n&&n.currentIndex<kp(r,i,o)?n:r,a=kp(s,i,o),l=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)i++;else{o||(o=[]);let c=a-i,u=l-i;if(c!=u){for(let g=0;g<c;g++){let f=g<o.length?o[g]:o[g]=0,m=f+g;u<=m&&m<c&&(o[g]=f+1)}let d=s.previousIndex;o[d]=u-c}}a!==l&&t(s,a,l)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Wm(t))throw new D(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,i,o,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)o=t[a],s=this._trackByFn(a,o),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,o,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,o,s,a)),Object.is(n.item,o)||this._addIdentityChange(n,o)),n=n._next}else i=0,Z_(t,a=>{s=this._trackByFn(i,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,i),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,i)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,i){let o;return t===null?o=this._itTail:(o=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,o,i)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,i),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,o,i)):t=this._addAfter(new gu(n,r),o,i)),t}_verifyReinsertion(t,n,r,i){let o=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return o!==null?t=this._reinsertAfter(o,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let i=t._prevRemoved,o=t._nextRemoved;return i===null?this._removalsHead=o:i._nextRemoved=o,o===null?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let i=n===null?this._itHead:n._next;return t._next=i,t._prev=n,i===null?this._itTail=t:i._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Gs),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Gs),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},gu=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},mu=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Gs=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new mu,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,i=this.map.get(r);return i?i.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function kp(e,t,n){let r=e.previousIndex;if(r===null)return r;let i=0;return n&&r<n.length&&(i=n[r]),r+t+i}function Rp(){return new ad([new hu])}var ad=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:Rp})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Rp()),deps:[[e,new vu,new Ys]]}}find(n){let r=this.factories.find(i=>i.supports(n));if(r!=null)return r;throw new D(901,!1)}}return e})();var f0=sd(null,"core",[]),h0=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(M(Jt))}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({})}}return e})();function ar(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function ld(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function ji(e,t){In("NgSignals");let n=Dh(e);return t?.equal&&(n[dn].equal=t.equal),n}function en(e){let t=Y(null);try{return e()}finally{Y(t)}}function p0(e){let t=Dn(e);if(!t)return null;let n=new zr(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var _0=null;function lr(){return _0}function w0(e){_0??=e}var xa=class{};var Me=new E(""),vd=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>C(fb),providedIn:"platform"})}}return e})(),b0=new E(""),fb=(()=>{class e extends vd{constructor(){super(),this._doc=C(Me),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return lr().getBaseHref(this._doc)}onPopState(n){let r=lr().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=lr().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,i){this._history.pushState(n,r,i)}replaceState(n,r,i){this._history.replaceState(n,r,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function Cd(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function g0(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function nn(e){return e&&e[0]!=="?"?"?"+e:e}var cr=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>C(Dd),providedIn:"root"})}}return e})(),E0=new E(""),Dd=(()=>{class e extends cr{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??C(Me).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Cd(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+nn(this._platformLocation.search),i=this._platformLocation.hash;return i&&n?`${r}${i}`:r}pushState(n,r,i,o){let s=this.prepareExternalUrl(i+nn(o));this._platformLocation.pushState(n,r,s)}replaceState(n,r,i,o){let s=this.prepareExternalUrl(i+nn(o));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(M(vd),M(E0,8))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),M0=(()=>{class e extends cr{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Cd(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,i,o){let s=this.prepareExternalUrl(i+nn(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,i,o){let s=this.prepareExternalUrl(i+nn(o));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(M(vd),M(E0,8))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Kr=(()=>{class e{constructor(n){this._subject=new _e,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=gb(g0(m0(r))),this._locationStrategy.onPopState(i=>{this._subject.emit({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+nn(r))}normalize(n){return e.stripTrailingSlash(pb(this._basePath,m0(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",i=null){this._locationStrategy.pushState(i,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+nn(r)),i)}replaceState(n,r="",i=null){this._locationStrategy.replaceState(i,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+nn(r)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(i=>i(n,r))}subscribe(n,r,i){return this._subject.subscribe({next:n,error:r,complete:i})}static{this.normalizeQueryParams=nn}static{this.joinWithSlash=Cd}static{this.stripTrailingSlash=g0}static{this.\u0275fac=function(r){return new(r||e)(M(cr))}}static{this.\u0275prov=b({token:e,factory:()=>hb(),providedIn:"root"})}}return e})();function hb(){return new Kr(M(cr))}function pb(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function m0(e){return e.replace(/\/index.html$/,"")}function gb(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Ve=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(Ve||{}),oe=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(oe||{}),Ke=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Ke||{}),Tn={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function mb(e){return lt(e)[ye.LocaleId]}function yb(e,t,n){let r=lt(e),i=[r[ye.DayPeriodsFormat],r[ye.DayPeriodsStandalone]],o=ut(i,t);return ut(o,n)}function vb(e,t,n){let r=lt(e),i=[r[ye.DaysFormat],r[ye.DaysStandalone]],o=ut(i,t);return ut(o,n)}function Cb(e,t,n){let r=lt(e),i=[r[ye.MonthsFormat],r[ye.MonthsStandalone]],o=ut(i,t);return ut(o,n)}function Db(e,t){let r=lt(e)[ye.Eras];return ut(r,t)}function Da(e,t){let n=lt(e);return ut(n[ye.DateFormat],t)}function _a(e,t){let n=lt(e);return ut(n[ye.TimeFormat],t)}function wa(e,t){let r=lt(e)[ye.DateTimeFormat];return ut(r,t)}function Aa(e,t){let n=lt(e),r=n[ye.NumberSymbols][t];if(typeof r>"u"){if(t===Tn.CurrencyDecimal)return n[ye.NumberSymbols][Tn.Decimal];if(t===Tn.CurrencyGroup)return n[ye.NumberSymbols][Tn.Group]}return r}function I0(e){if(!e[ye.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ye.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function _b(e){let t=lt(e);return I0(t),(t[ye.ExtraData][2]||[]).map(r=>typeof r=="string"?cd(r):[cd(r[0]),cd(r[1])])}function wb(e,t,n){let r=lt(e);I0(r);let i=[r[ye.ExtraData][0],r[ye.ExtraData][1]],o=ut(i,t)||[];return ut(o,n)||[]}function ut(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function cd(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var bb=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,ba={},Eb=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,rn=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(rn||{}),ee=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(ee||{}),J=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(J||{});function Mb(e,t,n,r){let i=kb(e);t=tn(n,t)||t;let s=[],a;for(;t;)if(a=Eb.exec(t),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;t=u}else{s.push(t);break}let l=i.getTimezoneOffset();r&&(l=x0(r,l),i=Pb(i,r,!0));let c="";return s.forEach(u=>{let d=Nb(u);c+=d?d(i,n,l):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function Ta(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function tn(e,t){let n=mb(e);if(ba[n]??={},ba[n][t])return ba[n][t];let r="";switch(t){case"shortDate":r=Da(e,Ke.Short);break;case"mediumDate":r=Da(e,Ke.Medium);break;case"longDate":r=Da(e,Ke.Long);break;case"fullDate":r=Da(e,Ke.Full);break;case"shortTime":r=_a(e,Ke.Short);break;case"mediumTime":r=_a(e,Ke.Medium);break;case"longTime":r=_a(e,Ke.Long);break;case"fullTime":r=_a(e,Ke.Full);break;case"short":let i=tn(e,"shortTime"),o=tn(e,"shortDate");r=Ea(wa(e,Ke.Short),[i,o]);break;case"medium":let s=tn(e,"mediumTime"),a=tn(e,"mediumDate");r=Ea(wa(e,Ke.Medium),[s,a]);break;case"long":let l=tn(e,"longTime"),c=tn(e,"longDate");r=Ea(wa(e,Ke.Long),[l,c]);break;case"full":let u=tn(e,"fullTime"),d=tn(e,"fullDate");r=Ea(wa(e,Ke.Full),[u,d]);break}return r&&(ba[n][t]=r),r}function Ea(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function _t(e,t,n="-",r,i){let o="";(e<0||i&&e<=0)&&(i?e=-e+1:(e=-e,o=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),o+s}function Ib(e,t){return _t(e,3).substring(0,t)}function we(e,t,n=0,r=!1,i=!1){return function(o,s){let a=Sb(e,o);if((n>0||a>-n)&&(a+=n),e===ee.Hours)a===0&&n===-12&&(a=12);else if(e===ee.FractionalSeconds)return Ib(a,t);let l=Aa(s,Tn.MinusSign);return _t(a,t,l,r,i)}}function Sb(e,t){switch(e){case ee.FullYear:return t.getFullYear();case ee.Month:return t.getMonth();case ee.Date:return t.getDate();case ee.Hours:return t.getHours();case ee.Minutes:return t.getMinutes();case ee.Seconds:return t.getSeconds();case ee.FractionalSeconds:return t.getMilliseconds();case ee.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function ae(e,t,n=Ve.Format,r=!1){return function(i,o){return xb(i,o,e,t,n,r)}}function xb(e,t,n,r,i,o){switch(n){case J.Months:return Cb(t,i,r)[e.getMonth()];case J.Days:return vb(t,i,r)[e.getDay()];case J.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(o){let c=_b(t),u=wb(t,i,r),d=c.findIndex(g=>{if(Array.isArray(g)){let[f,m]=g,v=s>=f.hours&&a>=f.minutes,_=s<m.hours||s===m.hours&&a<m.minutes;if(f.hours<m.hours){if(v&&_)return!0}else if(v||_)return!0}else if(g.hours===s&&g.minutes===a)return!0;return!1});if(d!==-1)return u[d]}return yb(t,i,r)[s<12?0:1];case J.Eras:return Db(t,r)[e.getFullYear()<=0?0:1];default:let l=n;throw new Error(`unexpected translation type ${l}`)}}function Ma(e){return function(t,n,r){let i=-1*r,o=Aa(n,Tn.MinusSign),s=i>0?Math.floor(i/60):Math.ceil(i/60);switch(e){case rn.Short:return(i>=0?"+":"")+_t(s,2,o)+_t(Math.abs(i%60),2,o);case rn.ShortGMT:return"GMT"+(i>=0?"+":"")+_t(s,1,o);case rn.Long:return"GMT"+(i>=0?"+":"")+_t(s,2,o)+":"+_t(Math.abs(i%60),2,o);case rn.Extended:return r===0?"Z":(i>=0?"+":"")+_t(s,2,o)+":"+_t(Math.abs(i%60),2,o);default:throw new Error(`Unknown zone width "${e}"`)}}}var Tb=0,Sa=4;function Ab(e){let t=Ta(e,Tb,1).getDay();return Ta(e,0,1+(t<=Sa?Sa:Sa+7)-t)}function S0(e){let t=e.getDay(),n=t===0?-3:Sa-t;return Ta(e.getFullYear(),e.getMonth(),e.getDate()+n)}function ud(e,t=!1){return function(n,r){let i;if(t){let o=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();i=1+Math.floor((s+o)/7)}else{let o=S0(n),s=Ab(o.getFullYear()),a=o.getTime()-s.getTime();i=1+Math.round(a/6048e5)}return _t(i,e,Aa(r,Tn.MinusSign))}}function Ia(e,t=!1){return function(n,r){let o=S0(n).getFullYear();return _t(o,e,Aa(r,Tn.MinusSign),t)}}var dd={};function Nb(e){if(dd[e])return dd[e];let t;switch(e){case"G":case"GG":case"GGG":t=ae(J.Eras,oe.Abbreviated);break;case"GGGG":t=ae(J.Eras,oe.Wide);break;case"GGGGG":t=ae(J.Eras,oe.Narrow);break;case"y":t=we(ee.FullYear,1,0,!1,!0);break;case"yy":t=we(ee.FullYear,2,0,!0,!0);break;case"yyy":t=we(ee.FullYear,3,0,!1,!0);break;case"yyyy":t=we(ee.FullYear,4,0,!1,!0);break;case"Y":t=Ia(1);break;case"YY":t=Ia(2,!0);break;case"YYY":t=Ia(3);break;case"YYYY":t=Ia(4);break;case"M":case"L":t=we(ee.Month,1,1);break;case"MM":case"LL":t=we(ee.Month,2,1);break;case"MMM":t=ae(J.Months,oe.Abbreviated);break;case"MMMM":t=ae(J.Months,oe.Wide);break;case"MMMMM":t=ae(J.Months,oe.Narrow);break;case"LLL":t=ae(J.Months,oe.Abbreviated,Ve.Standalone);break;case"LLLL":t=ae(J.Months,oe.Wide,Ve.Standalone);break;case"LLLLL":t=ae(J.Months,oe.Narrow,Ve.Standalone);break;case"w":t=ud(1);break;case"ww":t=ud(2);break;case"W":t=ud(1,!0);break;case"d":t=we(ee.Date,1);break;case"dd":t=we(ee.Date,2);break;case"c":case"cc":t=we(ee.Day,1);break;case"ccc":t=ae(J.Days,oe.Abbreviated,Ve.Standalone);break;case"cccc":t=ae(J.Days,oe.Wide,Ve.Standalone);break;case"ccccc":t=ae(J.Days,oe.Narrow,Ve.Standalone);break;case"cccccc":t=ae(J.Days,oe.Short,Ve.Standalone);break;case"E":case"EE":case"EEE":t=ae(J.Days,oe.Abbreviated);break;case"EEEE":t=ae(J.Days,oe.Wide);break;case"EEEEE":t=ae(J.Days,oe.Narrow);break;case"EEEEEE":t=ae(J.Days,oe.Short);break;case"a":case"aa":case"aaa":t=ae(J.DayPeriods,oe.Abbreviated);break;case"aaaa":t=ae(J.DayPeriods,oe.Wide);break;case"aaaaa":t=ae(J.DayPeriods,oe.Narrow);break;case"b":case"bb":case"bbb":t=ae(J.DayPeriods,oe.Abbreviated,Ve.Standalone,!0);break;case"bbbb":t=ae(J.DayPeriods,oe.Wide,Ve.Standalone,!0);break;case"bbbbb":t=ae(J.DayPeriods,oe.Narrow,Ve.Standalone,!0);break;case"B":case"BB":case"BBB":t=ae(J.DayPeriods,oe.Abbreviated,Ve.Format,!0);break;case"BBBB":t=ae(J.DayPeriods,oe.Wide,Ve.Format,!0);break;case"BBBBB":t=ae(J.DayPeriods,oe.Narrow,Ve.Format,!0);break;case"h":t=we(ee.Hours,1,-12);break;case"hh":t=we(ee.Hours,2,-12);break;case"H":t=we(ee.Hours,1);break;case"HH":t=we(ee.Hours,2);break;case"m":t=we(ee.Minutes,1);break;case"mm":t=we(ee.Minutes,2);break;case"s":t=we(ee.Seconds,1);break;case"ss":t=we(ee.Seconds,2);break;case"S":t=we(ee.FractionalSeconds,1);break;case"SS":t=we(ee.FractionalSeconds,2);break;case"SSS":t=we(ee.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=Ma(rn.Short);break;case"ZZZZZ":t=Ma(rn.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=Ma(rn.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=Ma(rn.Long);break;default:return null}return dd[e]=t,t}function x0(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function Ob(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function Pb(e,t,n){let r=n?-1:1,i=e.getTimezoneOffset(),o=x0(t,i);return Ob(e,r*(o-i))}function kb(e){if(y0(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[i,o=1,s=1]=e.split("-").map(a=>+a);return Ta(i,o-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(bb))return Rb(r)}let t=new Date(e);if(!y0(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function Rb(e){let t=new Date(0),n=0,r=0,i=e[8]?t.setUTCFullYear:t.setFullYear,o=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),i.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,l=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return o.call(t,s,a,l,c),t}function y0(e){return e instanceof Date&&!isNaN(e.valueOf())}function Na(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[i,o]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(i.trim()===t)return decodeURIComponent(o)}return null}var fd=/\s+/,v0=[],Oa=(()=>{class e{constructor(n,r){this._ngEl=n,this._renderer=r,this.initialClasses=v0,this.stateMap=new Map}set klass(n){this.initialClasses=n!=null?n.trim().split(fd):v0}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(fd):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let i=this.stateMap.get(n);i!==void 0?(i.enabled!==r&&(i.changed=!0,i.enabled=r),i.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],i=n[1];i.changed?(this._toggleClass(r,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),i.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(fd).forEach(i=>{r?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}static{this.\u0275fac=function(r){return new(r||e)(U(at),U(Qr))}}static{this.\u0275dir=mt({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return e})();var hd=class{constructor(t,n,r,i){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=i}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Xr=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,i){this._viewContainer=n,this._template=r,this._differs=i,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((i,o,s)=>{if(i.previousIndex==null)r.createEmbeddedView(this._template,new hd(i.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(o===null?void 0:o);else if(o!==null){let a=r.get(o);r.move(a,s),C0(a,i)}});for(let i=0,o=r.length;i<o;i++){let a=r.get(i).context;a.index=i,a.count=o,a.ngForOf=this._ngForOf}n.forEachIdentityChange(i=>{let o=r.get(i.currentIndex);C0(o,i)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(U(Mn),U(tr),U(ad))}}static{this.\u0275dir=mt({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function C0(e,t){e.context.$implicit=t.item}var Ft=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new pd,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){D0("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){D0("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(U(Mn),U(tr))}}static{this.\u0275dir=mt({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),pd=class{constructor(){this.$implicit=null,this.ngIf=null}};function D0(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${Fe(t)}'.`)}function Fb(e,t){return new D(2100,!1)}var Lb="mediumDate",Vb=new E(""),jb=new E(""),T0=(()=>{class e{constructor(n,r,i){this.locale=n,this.defaultTimezone=r,this.defaultOptions=i}transform(n,r,i,o){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??Lb,a=i??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return Mb(n,s,o||this.locale,a)}catch(s){throw Fb(e,s.message)}}static{this.\u0275fac=function(r){return new(r||e)(U(Ca,16),U(Vb,24),U(jb,24))}}static{this.\u0275pipe=Kp({name:"date",type:e,pure:!0,standalone:!0})}}return e})();var Pa=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({})}}return e})(),_d="browser",Bb="server";function Ub(e){return e===_d}function Bi(e){return e===Bb}var A0=(()=>{class e{static{this.\u0275prov=b({token:e,providedIn:"root",factory:()=>Ub(C(Ct))?new gd(C(Me),window):new md})}}return e})(),gd=class{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=$b(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,i=n.top+this.window.pageYOffset,o=this.offset();this.window.scrollTo(r-o[0],i-o[1])}};function $b(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),i=r.currentNode;for(;i;){let o=i.shadowRoot;if(o){let s=o.getElementById(t)||o.querySelector(`[name="${t}"]`);if(s)return s}i=r.nextNode()}}return null}var md=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},Yr=class{};var N0=e=>e.src,Hb=new E("",{providedIn:"root",factory:()=>N0});var zb=new E("NG_OPTIMIZED_PRELOADED_IMAGES",{providedIn:"root",factory:()=>new Set}),Gb=(()=>{class e{constructor(){this.preloadedImages=C(zb),this.document=C(Me)}createPreloadLinkTag(n,r,i,o){if(this.preloadedImages.has(r))return;this.preloadedImages.add(r);let s=n.createElement("link");n.setAttribute(s,"as","image"),n.setAttribute(s,"href",r),n.setAttribute(s,"rel","preload"),n.setAttribute(s,"fetchpriority","high"),o&&n.setAttribute(s,"imageSizes",o),i&&n.setAttribute(s,"imageSrcset",i),n.appendChild(this.document.head,s)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var qb=/^((\s*\d+w\s*(,|$)){1,})$/;var Wb=[1,2],Zb=640;var Qb=1920,Yb=1080;var Jr=(()=>{class e{constructor(){this.imageLoader=C(Hb),this.config=Kb(C(nm)),this.renderer=C(Qr),this.imgElement=C(at).nativeElement,this.injector=C(He),this.isServer=Bi(C(Ct)),this.preloadLinkCreator=C(Gb),this.lcpObserver=null,this._renderedSrc=null,this.priority=!1,this.disableOptimizedSrcset=!1,this.fill=!1}ngOnInit(){In("NgOptimizedImage"),this.placeholder&&this.removePlaceholderOnLoad(this.imgElement),this.setHostAttributes()}setHostAttributes(){this.fill?this.sizes||="100vw":(this.setHostAttribute("width",this.width.toString()),this.setHostAttribute("height",this.height.toString())),this.setHostAttribute("loading",this.getLoadingBehavior()),this.setHostAttribute("fetchpriority",this.getFetchPriority()),this.setHostAttribute("ng-img","true");let n=this.updateSrcAndSrcset();this.sizes&&this.setHostAttribute("sizes",this.sizes),this.isServer&&this.priority&&this.preloadLinkCreator.createPreloadLinkTag(this.renderer,this.getRewrittenSrc(),n,this.sizes)}ngOnChanges(n){if(n.ngSrc&&!n.ngSrc.isFirstChange()){let r=this._renderedSrc;this.updateSrcAndSrcset(!0);let i=this._renderedSrc;this.lcpObserver!==null&&r&&i&&r!==i&&this.injector.get(K).runOutsideAngular(()=>{this.lcpObserver?.updateImage(r,i)})}}callImageLoader(n){let r=n;return this.loaderParams&&(r.loaderParams=this.loaderParams),this.imageLoader(r)}getLoadingBehavior(){return!this.priority&&this.loading!==void 0?this.loading:this.priority?"eager":"lazy"}getFetchPriority(){return this.priority?"high":"auto"}getRewrittenSrc(){if(!this._renderedSrc){let n={src:this.ngSrc};this._renderedSrc=this.callImageLoader(n)}return this._renderedSrc}getRewrittenSrcset(){let n=qb.test(this.ngSrcset);return this.ngSrcset.split(",").filter(i=>i!=="").map(i=>{i=i.trim();let o=n?parseFloat(i):parseFloat(i)*this.width;return`${this.callImageLoader({src:this.ngSrc,width:o})} ${i}`}).join(", ")}getAutomaticSrcset(){return this.sizes?this.getResponsiveSrcset():this.getFixedSrcset()}getResponsiveSrcset(){let{breakpoints:n}=this.config,r=n;return this.sizes?.trim()==="100vw"&&(r=n.filter(o=>o>=Zb)),r.map(o=>`${this.callImageLoader({src:this.ngSrc,width:o})} ${o}w`).join(", ")}updateSrcAndSrcset(n=!1){n&&(this._renderedSrc=null);let r=this.getRewrittenSrc();this.setHostAttribute("src",r);let i;return this.ngSrcset?i=this.getRewrittenSrcset():this.shouldGenerateAutomaticSrcset()&&(i=this.getAutomaticSrcset()),i&&this.setHostAttribute("srcset",i),i}getFixedSrcset(){return Wb.map(r=>`${this.callImageLoader({src:this.ngSrc,width:this.width*r})} ${r}x`).join(", ")}shouldGenerateAutomaticSrcset(){let n=!1;return this.sizes||(n=this.width>Qb||this.height>Yb),!this.disableOptimizedSrcset&&!this.srcset&&this.imageLoader!==N0&&!n}generatePlaceholder(n){let{placeholderResolution:r}=this.config;return n===!0?`url(${this.callImageLoader({src:this.ngSrc,width:r,isPlaceholder:!0})})`:typeof n=="string"?`url(${n})`:null}shouldBlurPlaceholder(n){return!n||!n.hasOwnProperty("blur")?!0:!!n.blur}removePlaceholderOnLoad(n){let r=()=>{let s=this.injector.get(sr);i(),o(),this.placeholder=!1,s.markForCheck()},i=this.renderer.listen(n,"load",r),o=this.renderer.listen(n,"error",r);Xb(n,r)}ngOnDestroy(){}setHostAttribute(n,r){this.renderer.setAttribute(this.imgElement,n,r)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=mt({type:e,selectors:[["img","ngSrc",""]],hostVars:18,hostBindings:function(r,i){r&2&&rd("position",i.fill?"absolute":null)("width",i.fill?"100%":null)("height",i.fill?"100%":null)("inset",i.fill?"0":null)("background-size",i.placeholder?"cover":null)("background-position",i.placeholder?"50% 50%":null)("background-repeat",i.placeholder?"no-repeat":null)("background-image",i.placeholder?i.generatePlaceholder(i.placeholder):null)("filter",i.placeholder&&i.shouldBlurPlaceholder(i.placeholderConfig)?"blur(15px)":null)},inputs:{ngSrc:[2,"ngSrc","ngSrc",Jb],ngSrcset:"ngSrcset",sizes:"sizes",width:[2,"width","width",ld],height:[2,"height","height",ld],loading:"loading",priority:[2,"priority","priority",ar],loaderParams:"loaderParams",disableOptimizedSrcset:[2,"disableOptimizedSrcset","disableOptimizedSrcset",ar],fill:[2,"fill","fill",ar],placeholder:[2,"placeholder","placeholder",eE],placeholderConfig:"placeholderConfig",src:"src",srcset:"srcset"},standalone:!0,features:[td,Wr]})}}return e})();function Kb(e){let t={};return e.breakpoints&&(t.breakpoints=e.breakpoints.sort((n,r)=>n-r)),Object.assign({},Bu,e,t)}function Xb(e,t){e.complete&&e.naturalWidth&&t()}function Jb(e){return typeof e=="string"?e:Pi(e)}function eE(e){return typeof e=="string"&&e!=="true"&&e!=="false"&&e!==""?e:ar(e)}var $i=class{},Ra=class{},on=class e{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let i=n.slice(0,r),o=i.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(i,o),this.headers.has(o)?this.headers.get(o).push(s):this.headers.set(o,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let i=(t.op==="a"?this.headers.get(n):void 0)||[];i.push(...r),this.headers.set(n,i);break;case"d":let o=t.value;if(!o)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>o.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(o=>o.toString()),i=t.toLowerCase();this.headers.set(i,r),this.maybeSetNormalizedName(t,i)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var bd=class{encodeKey(t){return O0(t)}encodeValue(t){return O0(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function tE(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(i=>{let o=i.indexOf("="),[s,a]=o==-1?[t.decodeKey(i),""]:[t.decodeKey(i.slice(0,o)),t.decodeValue(i.slice(o+1))],l=n.get(s)||[];l.push(a),n.set(s,l)}),n}var nE=/%(\d[a-f0-9])/gi,rE={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function O0(e){return encodeURIComponent(e).replace(nE,(t,n)=>rE[n]??t)}function ka(e){return`${e}`}var On=class e{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new bd,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=tE(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],i=Array.isArray(r)?r.map(ka):[ka(r)];this.map.set(n,i)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let i=t[r];Array.isArray(i)?i.forEach(o=>{n.push({param:r,value:o,op:"a"})}):n.push({param:r,value:i,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(ka(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],i=r.indexOf(ka(t.value));i!==-1&&r.splice(i,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var Ed=class{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function iE(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function P0(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function k0(e){return typeof Blob<"u"&&e instanceof Blob}function R0(e){return typeof FormData<"u"&&e instanceof FormData}function oE(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Ui=class e{constructor(t,n,r,i){this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase();let o;if(iE(this.method)||i?(this.body=r!==void 0?r:null,o=i):o=r,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new on,this.context??=new Ed,!this.params)this.params=new On,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),l=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+l+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||P0(this.body)||k0(this.body)||R0(this.body)||oE(this.body)?this.body:this.body instanceof On?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||R0(this.body)?null:k0(this.body)?this.body.type||null:P0(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof On?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,i=t.responseType||this.responseType,o=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,l=t.reportProgress??this.reportProgress,c=t.headers||this.headers,u=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(c=Object.keys(t.setHeaders).reduce((g,f)=>g.set(f,t.setHeaders[f]),c)),t.setParams&&(u=Object.keys(t.setParams).reduce((g,f)=>g.set(f,t.setParams[f]),u)),new e(n,r,s,{params:u,headers:c,context:d,reportProgress:l,responseType:i,withCredentials:a,transferCache:o})}},Pn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Pn||{}),Hi=class{constructor(t,n=200,r="OK"){this.headers=t.headers||new on,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Fa=class e extends Hi{constructor(t={}){super(t),this.type=Pn.ResponseHeader}clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},zi=class e extends Hi{constructor(t={}){super(t),this.type=Pn.Response,this.body=t.body!==void 0?t.body:null}clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Nn=class extends Hi{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},B0=200,sE=204;function wd(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var sn=(()=>{class e{constructor(n){this.handler=n}request(n,r,i={}){let o;if(n instanceof Ui)o=n;else{let l;i.headers instanceof on?l=i.headers:l=new on(i.headers);let c;i.params&&(i.params instanceof On?c=i.params:c=new On({fromObject:i.params})),o=new Ui(n,r,i.body!==void 0?i.body:null,{headers:l,context:i.context,params:c,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache})}let s=A(o).pipe($t(l=>this.handler.handle(l)));if(n instanceof Ui||i.observe==="events")return s;let a=s.pipe(Be(l=>l instanceof zi));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return a.pipe(R(l=>{if(l.body!==null&&!(l.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return l.body}));case"blob":return a.pipe(R(l=>{if(l.body!==null&&!(l.body instanceof Blob))throw new Error("Response is not a Blob.");return l.body}));case"text":return a.pipe(R(l=>{if(l.body!==null&&typeof l.body!="string")throw new Error("Response is not a string.");return l.body}));case"json":default:return a.pipe(R(l=>l.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${i.observe}}`)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new On().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,i={}){return this.request("PATCH",n,wd(i,r))}post(n,r,i={}){return this.request("POST",n,wd(i,r))}put(n,r,i={}){return this.request("PUT",n,wd(i,r))}static{this.\u0275fac=function(r){return new(r||e)(M($i))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),aE=/^\)\]\}',?\n/,lE="X-Request-URL";function F0(e){if(e.url)return e.url;let t=lE.toLocaleLowerCase();return e.headers.get(t)}var cE=(()=>{class e{constructor(){this.fetchImpl=C(Md,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n)),this.ngZone=C(K)}handle(n){return new W(r=>{let i=new AbortController;return this.doRequest(n,i.signal,r).then(Id,o=>r.error(new Nn({error:o}))),()=>i.abort()})}doRequest(n,r,i){return No(this,null,function*(){let o=this.createRequestInit(n),s;try{let f=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,w({signal:r},o)));uE(f),i.next({type:Pn.Sent}),s=yield f}catch(f){i.error(new Nn({error:f,status:f.status??0,statusText:f.statusText,url:n.urlWithParams,headers:f.headers}));return}let a=new on(s.headers),l=s.statusText,c=F0(s)??n.urlWithParams,u=s.status,d=null;if(n.reportProgress&&i.next(new Fa({headers:a,status:u,statusText:l,url:c})),s.body){let f=s.headers.get("content-length"),m=[],v=s.body.getReader(),_=0,S,V,N=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>No(this,null,function*(){for(;;){let{done:he,value:te}=yield v.read();if(he)break;if(m.push(te),_+=te.length,n.reportProgress){V=n.responseType==="text"?(V??"")+(S??=new TextDecoder).decode(te,{stream:!0}):void 0;let le=()=>i.next({type:Pn.DownloadProgress,total:f?+f:void 0,loaded:_,partialText:V});N?N.run(le):le()}}}));let G=this.concatChunks(m,_);try{let he=s.headers.get("Content-Type")??"";d=this.parseBody(n,G,he)}catch(he){i.error(new Nn({error:he,headers:new on(s.headers),status:s.status,statusText:s.statusText,url:F0(s)??n.urlWithParams}));return}}u===0&&(u=d?B0:0),u>=200&&u<300?(i.next(new zi({body:d,headers:a,status:u,statusText:l,url:c})),i.complete()):i.error(new Nn({error:d,headers:a,status:u,statusText:l,url:c}))})}parseBody(n,r,i){switch(n.responseType){case"json":let o=new TextDecoder().decode(r).replace(aE,"");return o===""?null:JSON.parse(o);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:i});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},i=n.withCredentials?"include":void 0;if(n.headers.forEach((o,s)=>r[o]=s.join(",")),n.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!n.headers.has("Content-Type")){let o=n.detectContentTypeHeader();o!==null&&(r["Content-Type"]=o)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:i}}concatChunks(n,r){let i=new Uint8Array(r),o=0;for(let s of n)i.set(s,o),o+=s.length;return i}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Md=class{};function Id(){}function uE(e){e.then(Id,Id)}function U0(e,t){return t(e)}function dE(e,t){return(n,r)=>t.intercept(n,{handle:i=>e(i,r)})}function fE(e,t,n){return(r,i)=>ot(n,()=>t(r,o=>e(o,i)))}var hE=new E(""),Sd=new E(""),pE=new E(""),$0=new E("",{providedIn:"root",factory:()=>!0});function gE(){let e=null;return(t,n)=>{e===null&&(e=(C(hE,{optional:!0})??[]).reduceRight(dE,U0));let r=C(Qt);if(C($0)){let o=r.add();return e(t,n).pipe(gn(()=>r.remove(o)))}else return e(t,n)}}var L0=(()=>{class e extends $i{constructor(n,r){super(),this.backend=n,this.injector=r,this.chain=null,this.pendingTasks=C(Qt),this.contributeToStability=C($0)}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Sd),...this.injector.get(pE,[])]));this.chain=r.reduceRight((i,o)=>fE(i,o,this.injector),U0)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,i=>this.backend.handle(i)).pipe(gn(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||e)(M(Ra),M(Le))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();var mE=/^\)\]\}',?\n/;function yE(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var V0=(()=>{class e{constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new D(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?ce(r.\u0275loadImpl()):A(null)).pipe(Ue(()=>new W(o=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((v,_)=>s.setRequestHeader(v,_.join(","))),n.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){let v=n.detectContentTypeHeader();v!==null&&s.setRequestHeader("Content-Type",v)}if(n.responseType){let v=n.responseType.toLowerCase();s.responseType=v!=="json"?v:"text"}let a=n.serializeBody(),l=null,c=()=>{if(l!==null)return l;let v=s.statusText||"OK",_=new on(s.getAllResponseHeaders()),S=yE(s)||n.url;return l=new Fa({headers:_,status:s.status,statusText:v,url:S}),l},u=()=>{let{headers:v,status:_,statusText:S,url:V}=c(),N=null;_!==sE&&(N=typeof s.response>"u"?s.responseText:s.response),_===0&&(_=N?B0:0);let G=_>=200&&_<300;if(n.responseType==="json"&&typeof N=="string"){let he=N;N=N.replace(mE,"");try{N=N!==""?JSON.parse(N):null}catch(te){N=he,G&&(G=!1,N={error:te,text:N})}}G?(o.next(new zi({body:N,headers:v,status:_,statusText:S,url:V||void 0})),o.complete()):o.error(new Nn({error:N,headers:v,status:_,statusText:S,url:V||void 0}))},d=v=>{let{url:_}=c(),S=new Nn({error:v,status:s.status||0,statusText:s.statusText||"Unknown Error",url:_||void 0});o.error(S)},g=!1,f=v=>{g||(o.next(c()),g=!0);let _={type:Pn.DownloadProgress,loaded:v.loaded};v.lengthComputable&&(_.total=v.total),n.responseType==="text"&&s.responseText&&(_.partialText=s.responseText),o.next(_)},m=v=>{let _={type:Pn.UploadProgress,loaded:v.loaded};v.lengthComputable&&(_.total=v.total),o.next(_)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",m)),s.send(a),o.next({type:Pn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",m)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(M(Yr))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),H0=new E(""),vE="XSRF-TOKEN",CE=new E("",{providedIn:"root",factory:()=>vE}),DE="X-XSRF-TOKEN",_E=new E("",{providedIn:"root",factory:()=>DE}),La=class{},wE=(()=>{class e{constructor(n,r,i){this.doc=n,this.platform=r,this.cookieName=i,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Na(n,this.cookieName),this.lastCookieString=n),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(M(Me),M(Ct),M(CE))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function bE(e,t){let n=e.url.toLowerCase();if(!C(H0)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=C(La).getToken(),i=C(_E);return r!=null&&!e.headers.has(i)&&(e=e.clone({headers:e.headers.set(i,r)})),t(e)}var z0=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(z0||{});function EE(e,t){return{\u0275kind:e,\u0275providers:t}}function ME(...e){let t=[sn,V0,L0,{provide:$i,useExisting:L0},{provide:Ra,useFactory:()=>C(cE,{optional:!0})??C(V0)},{provide:Sd,useValue:bE,multi:!0},{provide:H0,useValue:!0},{provide:La,useClass:wE}];for(let n of e)t.push(...n.\u0275providers);return Ks(t)}var j0=new E("");function IE(){return EE(z0.LegacyInterceptors,[{provide:j0,useFactory:gE},{provide:Sd,useExisting:j0,multi:!0}])}var Va=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({providers:[ME(IE())]})}}return e})();var Ad=class extends xa{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Nd=class e extends Ad{static makeCurrent(){w0(new e)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=SE();return n==null?null:xE(n)}resetBaseElement(){Gi=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Na(document.cookie,t)}},Gi=null;function SE(){return Gi=Gi||document.querySelector("base"),Gi?Gi.getAttribute("href"):null}function xE(e){return new URL(e,document.baseURI).pathname}var Od=class{addToWindow(t){ke.getAngularTestability=(r,i=!0)=>{let o=t.findTestabilityInTree(r,i);if(o==null)throw new D(5103,!1);return o},ke.getAllAngularTestabilities=()=>t.getAllTestabilities(),ke.getAllAngularRootElements=()=>t.getAllRootElements();let n=r=>{let i=ke.getAllAngularTestabilities(),o=i.length,s=function(){o--,o==0&&r()};i.forEach(a=>{a.whenStable(s)})};ke.frameworkStabilizers||(ke.frameworkStabilizers=[]),ke.frameworkStabilizers.push(n)}findTestabilityInTree(t,n,r){if(n==null)return null;let i=t.getTestability(n);return i??(r?lr().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},TE=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Pd=new E(""),W0=(()=>{class e{constructor(n,r){this._zone=r,this._eventNameToPlugin=new Map,n.forEach(i=>{i.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,i){return this._findPluginFor(r).addEventListener(n,r,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(o=>o.supports(n)),!r)throw new D(5101,!1);return this._eventNameToPlugin.set(n,r),r}static{this.\u0275fac=function(r){return new(r||e)(M(Pd),M(K))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Ba=class{constructor(t){this._doc=t}},xd="ng-app-id",Z0=(()=>{class e{constructor(n,r,i,o={}){this.doc=n,this.appId=r,this.nonce=i,this.platformId=o,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Bi(o),this.resetHostNodes()}addStyles(n){for(let r of n)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(n){for(let r of n)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let n=this.styleNodesInDOM;n&&(n.forEach(r=>r.remove()),n.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(let r of this.getAllStyles())this.addStyleToHost(n,r)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(let r of this.hostNodes)this.addStyleToHost(r,n)}onStyleRemoved(n){let r=this.styleRef;r.get(n)?.elements?.forEach(i=>i.remove()),r.delete(n)}collectServerRenderedStyles(){let n=this.doc.head?.querySelectorAll(`style[${xd}="${this.appId}"]`);if(n?.length){let r=new Map;return n.forEach(i=>{i.textContent!=null&&r.set(i.textContent,i)}),r}return null}changeUsageCount(n,r){let i=this.styleRef;if(i.has(n)){let o=i.get(n);return o.usage+=r,o.usage}return i.set(n,{usage:r,elements:[]}),r}getStyleElement(n,r){let i=this.styleNodesInDOM,o=i?.get(r);if(o?.parentNode===n)return i.delete(r),o.removeAttribute(xd),o;{let s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(xd,this.appId),n.appendChild(s),s}}addStyleToHost(n,r){let i=this.getStyleElement(n,r),o=this.styleRef,s=o.get(r)?.elements;s?s.push(i):o.set(r,{elements:[i],usage:1})}resetHostNodes(){let n=this.hostNodes;n.clear(),n.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(M(Me),M(oa),M(ju,8),M(Ct))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),Td={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Rd=/%COMP%/g,Q0="%COMP%",AE=`_nghost-${Q0}`,NE=`_ngcontent-${Q0}`,OE=!0,PE=new E("",{providedIn:"root",factory:()=>OE});function kE(e){return NE.replace(Rd,e)}function RE(e){return AE.replace(Rd,e)}function Y0(e,t){return t.map(n=>n.replace(Rd,e))}var Ua=(()=>{class e{constructor(n,r,i,o,s,a,l,c=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.rendererByCompId=new Map,this.platformIsServer=Bi(a),this.defaultRenderer=new qi(n,s,l,this.platformIsServer)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===At.ShadowDom&&(r=q(w({},r),{encapsulation:At.Emulated}));let i=this.getOrCreateRenderer(n,r);return i instanceof $a?i.applyToHost(n):i instanceof Wi&&i.applyStyles(),i}getOrCreateRenderer(n,r){let i=this.rendererByCompId,o=i.get(r.id);if(!o){let s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case At.Emulated:o=new $a(l,c,r,this.appId,u,s,a,d);break;case At.ShadowDom:return new kd(l,c,n,r,s,a,this.nonce,d);default:o=new Wi(l,c,r,u,s,a,d);break}i.set(r.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(M(W0),M(Z0),M(oa),M(PE),M(Me),M(Ct),M(K),M(ju))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),qi=class{constructor(t,n,r,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=i,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(Td[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(G0(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(G0(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new D(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,i){if(i){n=i+":"+n;let o=Td[i];o?t.setAttributeNS(o,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let i=Td[r];i?t.removeAttributeNS(i,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,i){i&(qt.DashCase|qt.Important)?t.style.setProperty(n,r,i&qt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&qt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r){if(typeof t=="string"&&(t=lr().getGlobalEventTarget(this.doc,t),!t))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function G0(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var kd=class extends qi{constructor(t,n,r,i,o,s,a,l){super(t,o,s,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let c=Y0(i.id,i.styles);for(let u of c){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=u,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Wi=class extends qi{constructor(t,n,r,i,o,s,a,l){super(t,o,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=i,this.styles=l?Y0(l,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},$a=class extends Wi{constructor(t,n,r,i,o,s,a,l){let c=i+"-"+r.id;super(t,n,r,o,s,a,l,c),this.contentAttr=kE(c),this.hostAttr=RE(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}},FE=(()=>{class e extends Ba{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,i){return n.addEventListener(r,i,!1),()=>this.removeEventListener(n,r,i)}removeEventListener(n,r,i){return n.removeEventListener(r,i)}static{this.\u0275fac=function(r){return new(r||e)(M(Me))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),q0=["alt","control","meta","shift"],LE={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},VE={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},jE=(()=>{class e extends Ba{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,i){let o=e.parseEventName(r),s=e.eventCallback(o.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>lr().onAndCancel(n,o.domEventName,s))}static parseEventName(n){let r=n.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let o=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),q0.forEach(c=>{let u=r.indexOf(c);u>-1&&(r.splice(u,1),s+=c+".")}),s+=o,r.length!=0||o.length===0)return null;let l={};return l.domEventName=i,l.fullKey=s,l}static matchEventFullKeyCode(n,r){let i=LE[n.key]||n.key,o="";return r.indexOf("code.")>-1&&(i=n.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),q0.forEach(s=>{if(s!==i){let a=VE[s];a(n)&&(o+=s+".")}}),o+=i,o===r)}static eventCallback(n,r,i){return o=>{e.matchEventFullKeyCode(o,n)&&i.runGuarded(()=>r(o))}}static _normalizeKey(n){return n==="esc"?"escape":n}static{this.\u0275fac=function(r){return new(r||e)(M(Me))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function BE(){Nd.makeCurrent()}function UE(){return new Gt}function $E(){return tm(document),document}var HE=[{provide:Ct,useValue:_d},{provide:Lu,useValue:BE,multi:!0},{provide:Me,useFactory:$E,deps:[]}],K0=sd(f0,"browser",HE),zE=new E(""),GE=[{provide:Vi,useClass:Od,deps:[]},{provide:id,useClass:pa,deps:[K,ga,Vi]},{provide:pa,useClass:pa,deps:[K,ga,Vi]}],qE=[{provide:Xs,useValue:"root"},{provide:Gt,useFactory:UE,deps:[]},{provide:Pd,useClass:FE,multi:!0,deps:[Me,K,Ct]},{provide:Pd,useClass:jE,multi:!0,deps:[Me]},Ua,Z0,W0,{provide:wn,useExisting:Ua},{provide:Yr,useClass:TE,deps:[]},[]],Ha=(()=>{class e{constructor(n){}static withServerTransition(n){return{ngModule:e,providers:[{provide:oa,useValue:n.appId}]}}static{this.\u0275fac=function(r){return new(r||e)(M(zE,12))}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({providers:[...qE,...GE],imports:[Pa,h0]})}}return e})();var X0=(()=>{class e{constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static{this.\u0275fac=function(r){return new(r||e)(M(Me))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var $=function(e){return e[e.State=0]="State",e[e.Transition=1]="Transition",e[e.Sequence=2]="Sequence",e[e.Group=3]="Group",e[e.Animate=4]="Animate",e[e.Keyframes=5]="Keyframes",e[e.Style=6]="Style",e[e.Trigger=7]="Trigger",e[e.Reference=8]="Reference",e[e.AnimateChild=9]="AnimateChild",e[e.AnimateRef=10]="AnimateRef",e[e.Query=11]="Query",e[e.Stagger=12]="Stagger",e}($||{}),Lt="*";function ey(e,t=null){return{type:$.Sequence,steps:e,options:t}}function Fd(e){return{type:$.Style,styles:e,offset:null}}var kn=class{constructor(t=0,n=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=t+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},Zi=class{constructor(t){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=t;let n=0,r=0,i=0,o=this.players.length;o==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==o&&this._onFinish()}),s.onDestroy(()=>{++r==o&&this._onDestroy()}),s.onStart(()=>{++i==o&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let n=t*this.totalTime;this.players.forEach(r=>{let i=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(i)})}getPosition(){let t=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},za="!";function ty(e){return new D(3e3,!1)}function WE(){return new D(3100,!1)}function ZE(){return new D(3101,!1)}function QE(e){return new D(3001,!1)}function YE(e){return new D(3003,!1)}function KE(e){return new D(3004,!1)}function XE(e,t){return new D(3005,!1)}function JE(){return new D(3006,!1)}function eM(){return new D(3007,!1)}function tM(e,t){return new D(3008,!1)}function nM(e){return new D(3002,!1)}function rM(e,t,n,r,i){return new D(3010,!1)}function iM(){return new D(3011,!1)}function oM(){return new D(3012,!1)}function sM(){return new D(3200,!1)}function aM(){return new D(3202,!1)}function lM(){return new D(3013,!1)}function cM(e){return new D(3014,!1)}function uM(e){return new D(3015,!1)}function dM(e){return new D(3016,!1)}function fM(e,t){return new D(3404,!1)}function hM(e){return new D(3502,!1)}function pM(e){return new D(3503,!1)}function gM(){return new D(3300,!1)}function mM(e){return new D(3504,!1)}function yM(e){return new D(3301,!1)}function vM(e,t){return new D(3302,!1)}function CM(e){return new D(3303,!1)}function DM(e,t){return new D(3400,!1)}function _M(e){return new D(3401,!1)}function wM(e){return new D(3402,!1)}function bM(e,t){return new D(3505,!1)}function Rn(e){switch(e.length){case 0:return new kn;case 1:return e[0];default:return new Zi(e)}}function gy(e,t,n=new Map,r=new Map){let i=[],o=[],s=-1,a=null;if(t.forEach(l=>{let c=l.get("offset"),u=c==s,d=u&&a||new Map;l.forEach((g,f)=>{let m=f,v=g;if(f!=="offset")switch(m=e.normalizePropertyName(m,i),v){case za:v=n.get(f);break;case Lt:v=r.get(f);break;default:v=e.normalizeStyleValue(f,m,v,i);break}d.set(m,v)}),u||o.push(d),a=d,s=c}),i.length)throw hM(i);return o}function sf(e,t,n,r){switch(t){case"start":e.onStart(()=>r(n&&Ld(n,"start",e)));break;case"done":e.onDone(()=>r(n&&Ld(n,"done",e)));break;case"destroy":e.onDestroy(()=>r(n&&Ld(n,"destroy",e)));break}}function Ld(e,t,n){let r=n.totalTime,i=!!n.disabled,o=af(e.element,e.triggerName,e.fromState,e.toState,t||e.phaseName,r??e.totalTime,i),s=e._data;return s!=null&&(o._data=s),o}function af(e,t,n,r,i="",o=0,s){return{element:e,triggerName:t,fromState:n,toState:r,phaseName:i,totalTime:o,disabled:!!s}}function Je(e,t,n){let r=e.get(t);return r||e.set(t,r=n),r}function ny(e){let t=e.indexOf(":"),n=e.substring(1,t),r=e.slice(t+1);return[n,r]}var EM=typeof document>"u"?null:document.documentElement;function lf(e){let t=e.parentNode||e.host||null;return t===EM?null:t}function MM(e){return e.substring(1,6)=="ebkit"}var ur=null,ry=!1;function IM(e){ur||(ur=SM()||{},ry=ur.style?"WebkitAppearance"in ur.style:!1);let t=!0;return ur.style&&!MM(e)&&(t=e in ur.style,!t&&ry&&(t="Webkit"+e.charAt(0).toUpperCase()+e.slice(1)in ur.style)),t}function SM(){return typeof document<"u"?document.body:null}function my(e,t){for(;t;){if(t===e)return!0;t=lf(t)}return!1}function yy(e,t,n){if(n)return Array.from(e.querySelectorAll(t));let r=e.querySelector(t);return r?[r]:[]}var cf=(()=>{class e{validateStyleProperty(n){return IM(n)}containsElement(n,r){return my(n,r)}getParentElement(n){return lf(n)}query(n,r,i){return yy(n,r,i)}computeStyle(n,r,i){return i||""}animate(n,r,i,o,s,a=[],l){return new kn(i,o)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})(),hr=class{static{this.NOOP=new cf}},pr=class{};var xM=1e3,vy="{{",TM="}}",Cy="ng-enter",Hd="ng-leave",Ga="ng-trigger",Ya=".ng-trigger",iy="ng-animating",zd=".ng-animating";function an(e){if(typeof e=="number")return e;let t=e.match(/^(-?[\.\d]+)(m?s)/);return!t||t.length<2?0:Gd(parseFloat(t[1]),t[2])}function Gd(e,t){switch(t){case"s":return e*xM;default:return e}}function Ka(e,t,n){return e.hasOwnProperty("duration")?e:AM(e,t,n)}function AM(e,t,n){let r=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,o=0,s="";if(typeof e=="string"){let a=e.match(r);if(a===null)return t.push(ty(e)),{duration:0,delay:0,easing:""};i=Gd(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(o=Gd(parseFloat(l),a[4]));let c=a[5];c&&(s=c)}else i=e;if(!n){let a=!1,l=t.length;i<0&&(t.push(WE()),a=!0),o<0&&(t.push(ZE()),a=!0),a&&t.splice(l,0,ty(e))}return{duration:i,delay:o,easing:s}}function NM(e){return e.length?e[0]instanceof Map?e:e.map(t=>new Map(Object.entries(t))):[]}function Vt(e,t,n){t.forEach((r,i)=>{let o=uf(i);n&&!n.has(i)&&n.set(i,e.style[o]),e.style[o]=r})}function fr(e,t){t.forEach((n,r)=>{let i=uf(r);e.style[i]=""})}function Qi(e){return Array.isArray(e)?e.length==1?e[0]:ey(e):e}function OM(e,t,n){let r=t.params||{},i=Dy(e);i.length&&i.forEach(o=>{r.hasOwnProperty(o)||n.push(QE(o))})}var qd=new RegExp(`${vy}\\s*(.+?)\\s*${TM}`,"g");function Dy(e){let t=[];if(typeof e=="string"){let n;for(;n=qd.exec(e);)t.push(n[1]);qd.lastIndex=0}return t}function Ki(e,t,n){let r=`${e}`,i=r.replace(qd,(o,s)=>{let a=t[s];return a==null&&(n.push(YE(s)),a=""),a.toString()});return i==r?e:i}var PM=/-+([a-z0-9])/g;function uf(e){return e.replace(PM,(...t)=>t[1].toUpperCase())}function kM(e,t){return e===0||t===0}function RM(e,t,n){if(n.size&&t.length){let r=t[0],i=[];if(n.forEach((o,s)=>{r.has(s)||i.push(s),r.set(s,o)}),i.length)for(let o=1;o<t.length;o++){let s=t[o];i.forEach(a=>s.set(a,df(e,a)))}}return t}function Xe(e,t,n){switch(t.type){case $.Trigger:return e.visitTrigger(t,n);case $.State:return e.visitState(t,n);case $.Transition:return e.visitTransition(t,n);case $.Sequence:return e.visitSequence(t,n);case $.Group:return e.visitGroup(t,n);case $.Animate:return e.visitAnimate(t,n);case $.Keyframes:return e.visitKeyframes(t,n);case $.Style:return e.visitStyle(t,n);case $.Reference:return e.visitReference(t,n);case $.AnimateChild:return e.visitAnimateChild(t,n);case $.AnimateRef:return e.visitAnimateRef(t,n);case $.Query:return e.visitQuery(t,n);case $.Stagger:return e.visitStagger(t,n);default:throw KE(t.type)}}function df(e,t){return window.getComputedStyle(e)[t]}var FM=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),Xa=class extends pr{normalizePropertyName(t,n){return uf(t)}normalizeStyleValue(t,n,r,i){let o="",s=r.toString().trim();if(FM.has(n)&&r!==0&&r!=="0")if(typeof r=="number")o="px";else{let a=r.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&i.push(XE(t,r))}return s+o}};var Ja="*";function LM(e,t){let n=[];return typeof e=="string"?e.split(/\s*,\s*/).forEach(r=>VM(r,n,t)):n.push(e),n}function VM(e,t,n){if(e[0]==":"){let l=jM(e,n);if(typeof l=="function"){t.push(l);return}e=l}let r=e.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(r==null||r.length<4)return n.push(uM(e)),t;let i=r[1],o=r[2],s=r[3];t.push(oy(i,s));let a=i==Ja&&s==Ja;o[0]=="<"&&!a&&t.push(oy(s,i))}function jM(e,t){switch(e){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(n,r)=>parseFloat(r)>parseFloat(n);case":decrement":return(n,r)=>parseFloat(r)<parseFloat(n);default:return t.push(dM(e)),"* => *"}}var qa=new Set(["true","1"]),Wa=new Set(["false","0"]);function oy(e,t){let n=qa.has(e)||Wa.has(e),r=qa.has(t)||Wa.has(t);return(i,o)=>{let s=e==Ja||e==i,a=t==Ja||t==o;return!s&&n&&typeof i=="boolean"&&(s=i?qa.has(e):Wa.has(e)),!a&&r&&typeof o=="boolean"&&(a=o?qa.has(t):Wa.has(t)),s&&a}}var _y=":self",BM=new RegExp(`s*${_y}s*,?`,"g");function wy(e,t,n,r){return new Wd(e).build(t,n,r)}var sy="",Wd=class{constructor(t){this._driver=t}build(t,n,r){let i=new Zd(n);return this._resetContextStyleTimingState(i),Xe(this,Qi(t),i)}_resetContextStyleTimingState(t){t.currentQuerySelector=sy,t.collectedStyles=new Map,t.collectedStyles.set(sy,new Map),t.currentTime=0}visitTrigger(t,n){let r=n.queryCount=0,i=n.depCount=0,o=[],s=[];return t.name.charAt(0)=="@"&&n.errors.push(JE()),t.definitions.forEach(a=>{if(this._resetContextStyleTimingState(n),a.type==$.State){let l=a,c=l.name;c.toString().split(/\s*,\s*/).forEach(u=>{l.name=u,o.push(this.visitState(l,n))}),l.name=c}else if(a.type==$.Transition){let l=this.visitTransition(a,n);r+=l.queryCount,i+=l.depCount,s.push(l)}else n.errors.push(eM())}),{type:$.Trigger,name:t.name,states:o,transitions:s,queryCount:r,depCount:i,options:null}}visitState(t,n){let r=this.visitStyle(t.styles,n),i=t.options&&t.options.params||null;if(r.containsDynamicStyles){let o=new Set,s=i||{};r.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{Dy(l).forEach(c=>{s.hasOwnProperty(c)||o.add(c)})})}),o.size&&n.errors.push(tM(t.name,[...o.values()]))}return{type:$.State,name:t.name,style:r,options:i?{params:i}:null}}visitTransition(t,n){n.queryCount=0,n.depCount=0;let r=Xe(this,Qi(t.animation),n),i=LM(t.expr,n.errors);return{type:$.Transition,matchers:i,animation:r,queryCount:n.queryCount,depCount:n.depCount,options:dr(t.options)}}visitSequence(t,n){return{type:$.Sequence,steps:t.steps.map(r=>Xe(this,r,n)),options:dr(t.options)}}visitGroup(t,n){let r=n.currentTime,i=0,o=t.steps.map(s=>{n.currentTime=r;let a=Xe(this,s,n);return i=Math.max(i,n.currentTime),a});return n.currentTime=i,{type:$.Group,steps:o,options:dr(t.options)}}visitAnimate(t,n){let r=zM(t.timings,n.errors);n.currentAnimateTimings=r;let i,o=t.styles?t.styles:Fd({});if(o.type==$.Keyframes)i=this.visitKeyframes(o,n);else{let s=t.styles,a=!1;if(!s){a=!0;let c={};r.easing&&(c.easing=r.easing),s=Fd(c)}n.currentTime+=r.duration+r.delay;let l=this.visitStyle(s,n);l.isEmptyStep=a,i=l}return n.currentAnimateTimings=null,{type:$.Animate,timings:r,style:i,options:null}}visitStyle(t,n){let r=this._makeStyleAst(t,n);return this._validateStyleAst(r,n),r}_makeStyleAst(t,n){let r=[],i=Array.isArray(t.styles)?t.styles:[t.styles];for(let a of i)typeof a=="string"?a===Lt?r.push(a):n.errors.push(nM(a)):r.push(new Map(Object.entries(a)));let o=!1,s=null;return r.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(s=a.get("easing"),a.delete("easing")),!o)){for(let l of a.values())if(l.toString().indexOf(vy)>=0){o=!0;break}}}),{type:$.Style,styles:r,easing:s,offset:t.offset,containsDynamicStyles:o,options:null}}_validateStyleAst(t,n){let r=n.currentAnimateTimings,i=n.currentTime,o=n.currentTime;r&&o>0&&(o-=r.duration+r.delay),t.styles.forEach(s=>{typeof s!="string"&&s.forEach((a,l)=>{let c=n.collectedStyles.get(n.currentQuerySelector),u=c.get(l),d=!0;u&&(o!=i&&o>=u.startTime&&i<=u.endTime&&(n.errors.push(rM(l,u.startTime,u.endTime,o,i)),d=!1),o=u.startTime),d&&c.set(l,{startTime:o,endTime:i}),n.options&&OM(a,n.options,n.errors)})})}visitKeyframes(t,n){let r={type:$.Keyframes,styles:[],options:null};if(!n.currentAnimateTimings)return n.errors.push(iM()),r;let i=1,o=0,s=[],a=!1,l=!1,c=0,u=t.steps.map(S=>{let V=this._makeStyleAst(S,n),N=V.offset!=null?V.offset:HM(V.styles),G=0;return N!=null&&(o++,G=V.offset=N),l=l||G<0||G>1,a=a||G<c,c=G,s.push(G),V});l&&n.errors.push(oM()),a&&n.errors.push(sM());let d=t.steps.length,g=0;o>0&&o<d?n.errors.push(aM()):o==0&&(g=i/(d-1));let f=d-1,m=n.currentTime,v=n.currentAnimateTimings,_=v.duration;return u.forEach((S,V)=>{let N=g>0?V==f?1:g*V:s[V],G=N*_;n.currentTime=m+v.delay+G,v.duration=G,this._validateStyleAst(S,n),S.offset=N,r.styles.push(S)}),r}visitReference(t,n){return{type:$.Reference,animation:Xe(this,Qi(t.animation),n),options:dr(t.options)}}visitAnimateChild(t,n){return n.depCount++,{type:$.AnimateChild,options:dr(t.options)}}visitAnimateRef(t,n){return{type:$.AnimateRef,animation:this.visitReference(t.animation,n),options:dr(t.options)}}visitQuery(t,n){let r=n.currentQuerySelector,i=t.options||{};n.queryCount++,n.currentQuery=t;let[o,s]=UM(t.selector);n.currentQuerySelector=r.length?r+" "+o:o,Je(n.collectedStyles,n.currentQuerySelector,new Map);let a=Xe(this,Qi(t.animation),n);return n.currentQuery=null,n.currentQuerySelector=r,{type:$.Query,selector:o,limit:i.limit||0,optional:!!i.optional,includeSelf:s,animation:a,originalSelector:t.selector,options:dr(t.options)}}visitStagger(t,n){n.currentQuery||n.errors.push(lM());let r=t.timings==="full"?{duration:0,delay:0,easing:"full"}:Ka(t.timings,n.errors,!0);return{type:$.Stagger,animation:Xe(this,Qi(t.animation),n),timings:r,options:null}}};function UM(e){let t=!!e.split(/\s*,\s*/).find(n=>n==_y);return t&&(e=e.replace(BM,"")),e=e.replace(/@\*/g,Ya).replace(/@\w+/g,n=>Ya+"-"+n.slice(1)).replace(/:animating/g,zd),[e,t]}function $M(e){return e?w({},e):null}var Zd=class{constructor(t){this.errors=t,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}};function HM(e){if(typeof e=="string")return null;let t=null;if(Array.isArray(e))e.forEach(n=>{if(n instanceof Map&&n.has("offset")){let r=n;t=parseFloat(r.get("offset")),r.delete("offset")}});else if(e instanceof Map&&e.has("offset")){let n=e;t=parseFloat(n.get("offset")),n.delete("offset")}return t}function zM(e,t){if(e.hasOwnProperty("duration"))return e;if(typeof e=="number"){let o=Ka(e,t).duration;return Vd(o,0,"")}let n=e;if(n.split(/\s+/).some(o=>o.charAt(0)=="{"&&o.charAt(1)=="{")){let o=Vd(0,0,"");return o.dynamic=!0,o.strValue=n,o}let i=Ka(n,t);return Vd(i.duration,i.delay,i.easing)}function dr(e){return e?(e=w({},e),e.params&&(e.params=$M(e.params))):e={},e}function Vd(e,t,n){return{duration:e,delay:t,easing:n}}function ff(e,t,n,r,i,o,s=null,a=!1){return{type:1,element:e,keyframes:t,preStyleProps:n,postStyleProps:r,duration:i,delay:o,totalTime:i+o,easing:s,subTimeline:a}}var Xi=class{constructor(){this._map=new Map}get(t){return this._map.get(t)||[]}append(t,n){let r=this._map.get(t);r||this._map.set(t,r=[]),r.push(...n)}has(t){return this._map.has(t)}clear(){this._map.clear()}},GM=1,qM=":enter",WM=new RegExp(qM,"g"),ZM=":leave",QM=new RegExp(ZM,"g");function by(e,t,n,r,i,o=new Map,s=new Map,a,l,c=[]){return new Qd().buildKeyframes(e,t,n,r,i,o,s,a,l,c)}var Qd=class{buildKeyframes(t,n,r,i,o,s,a,l,c,u=[]){c=c||new Xi;let d=new Yd(t,n,c,i,o,u,[]);d.options=l;let g=l.delay?an(l.delay):0;d.currentTimeline.delayNextStep(g),d.currentTimeline.setStyles([s],null,d.errors,l),Xe(this,r,d);let f=d.timelines.filter(m=>m.containsAnimation());if(f.length&&a.size){let m;for(let v=f.length-1;v>=0;v--){let _=f[v];if(_.element===n){m=_;break}}m&&!m.allowOnlyTimelineStyles()&&m.setStyles([a],null,d.errors,l)}return f.length?f.map(m=>m.buildKeyframes()):[ff(n,[],[],[],0,g,"",!1)]}visitTrigger(t,n){}visitState(t,n){}visitTransition(t,n){}visitAnimateChild(t,n){let r=n.subInstructions.get(n.element);if(r){let i=n.createSubContext(t.options),o=n.currentTimeline.currentTime,s=this._visitSubInstructions(r,i,i.options);o!=s&&n.transformIntoNewTimeline(s)}n.previousNode=t}visitAnimateRef(t,n){let r=n.createSubContext(t.options);r.transformIntoNewTimeline(),this._applyAnimationRefDelays([t.options,t.animation.options],n,r),this.visitReference(t.animation,r),n.transformIntoNewTimeline(r.currentTimeline.currentTime),n.previousNode=t}_applyAnimationRefDelays(t,n,r){for(let i of t){let o=i?.delay;if(o){let s=typeof o=="number"?o:an(Ki(o,i?.params??{},n.errors));r.delayNextStep(s)}}}_visitSubInstructions(t,n,r){let o=n.currentTimeline.currentTime,s=r.duration!=null?an(r.duration):null,a=r.delay!=null?an(r.delay):null;return s!==0&&t.forEach(l=>{let c=n.appendInstructionToTimeline(l,s,a);o=Math.max(o,c.duration+c.delay)}),o}visitReference(t,n){n.updateOptions(t.options,!0),Xe(this,t.animation,n),n.previousNode=t}visitSequence(t,n){let r=n.subContextCount,i=n,o=t.options;if(o&&(o.params||o.delay)&&(i=n.createSubContext(o),i.transformIntoNewTimeline(),o.delay!=null)){i.previousNode.type==$.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=el);let s=an(o.delay);i.delayNextStep(s)}t.steps.length&&(t.steps.forEach(s=>Xe(this,s,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>r&&i.transformIntoNewTimeline()),n.previousNode=t}visitGroup(t,n){let r=[],i=n.currentTimeline.currentTime,o=t.options&&t.options.delay?an(t.options.delay):0;t.steps.forEach(s=>{let a=n.createSubContext(t.options);o&&a.delayNextStep(o),Xe(this,s,a),i=Math.max(i,a.currentTimeline.currentTime),r.push(a.currentTimeline)}),r.forEach(s=>n.currentTimeline.mergeTimelineCollectedStyles(s)),n.transformIntoNewTimeline(i),n.previousNode=t}_visitTiming(t,n){if(t.dynamic){let r=t.strValue,i=n.params?Ki(r,n.params,n.errors):r;return Ka(i,n.errors)}else return{duration:t.duration,delay:t.delay,easing:t.easing}}visitAnimate(t,n){let r=n.currentAnimateTimings=this._visitTiming(t.timings,n),i=n.currentTimeline;r.delay&&(n.incrementTime(r.delay),i.snapshotCurrentStyles());let o=t.style;o.type==$.Keyframes?this.visitKeyframes(o,n):(n.incrementTime(r.duration),this.visitStyle(o,n),i.applyStylesToKeyframe()),n.currentAnimateTimings=null,n.previousNode=t}visitStyle(t,n){let r=n.currentTimeline,i=n.currentAnimateTimings;!i&&r.hasCurrentStyleProperties()&&r.forwardFrame();let o=i&&i.easing||t.easing;t.isEmptyStep?r.applyEmptyStep(o):r.setStyles(t.styles,o,n.errors,n.options),n.previousNode=t}visitKeyframes(t,n){let r=n.currentAnimateTimings,i=n.currentTimeline.duration,o=r.duration,a=n.createSubContext().currentTimeline;a.easing=r.easing,t.styles.forEach(l=>{let c=l.offset||0;a.forwardTime(c*o),a.setStyles(l.styles,l.easing,n.errors,n.options),a.applyStylesToKeyframe()}),n.currentTimeline.mergeTimelineCollectedStyles(a),n.transformIntoNewTimeline(i+o),n.previousNode=t}visitQuery(t,n){let r=n.currentTimeline.currentTime,i=t.options||{},o=i.delay?an(i.delay):0;o&&(n.previousNode.type===$.Style||r==0&&n.currentTimeline.hasCurrentStyleProperties())&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=el);let s=r,a=n.invokeQuery(t.selector,t.originalSelector,t.limit,t.includeSelf,!!i.optional,n.errors);n.currentQueryTotal=a.length;let l=null;a.forEach((c,u)=>{n.currentQueryIndex=u;let d=n.createSubContext(t.options,c);o&&d.delayNextStep(o),c===n.element&&(l=d.currentTimeline),Xe(this,t.animation,d),d.currentTimeline.applyStylesToKeyframe();let g=d.currentTimeline.currentTime;s=Math.max(s,g)}),n.currentQueryIndex=0,n.currentQueryTotal=0,n.transformIntoNewTimeline(s),l&&(n.currentTimeline.mergeTimelineCollectedStyles(l),n.currentTimeline.snapshotCurrentStyles()),n.previousNode=t}visitStagger(t,n){let r=n.parentContext,i=n.currentTimeline,o=t.timings,s=Math.abs(o.duration),a=s*(n.currentQueryTotal-1),l=s*n.currentQueryIndex;switch(o.duration<0?"reverse":o.easing){case"reverse":l=a-l;break;case"full":l=r.currentStaggerTime;break}let u=n.currentTimeline;l&&u.delayNextStep(l);let d=u.currentTime;Xe(this,t.animation,n),n.previousNode=t,r.currentStaggerTime=i.currentTime-d+(i.startTime-r.currentTimeline.startTime)}},el={},Yd=class e{constructor(t,n,r,i,o,s,a,l){this._driver=t,this.element=n,this.subInstructions=r,this._enterClassName=i,this._leaveClassName=o,this.errors=s,this.timelines=a,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=el,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=l||new tl(this._driver,n,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(t,n){if(!t)return;let r=t,i=this.options;r.duration!=null&&(i.duration=an(r.duration)),r.delay!=null&&(i.delay=an(r.delay));let o=r.params;if(o){let s=i.params;s||(s=this.options.params={}),Object.keys(o).forEach(a=>{(!n||!s.hasOwnProperty(a))&&(s[a]=Ki(o[a],s,this.errors))})}}_copyOptions(){let t={};if(this.options){let n=this.options.params;if(n){let r=t.params={};Object.keys(n).forEach(i=>{r[i]=n[i]})}}return t}createSubContext(t=null,n,r){let i=n||this.element,o=new e(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,r||0));return o.previousNode=this.previousNode,o.currentAnimateTimings=this.currentAnimateTimings,o.options=this._copyOptions(),o.updateOptions(t),o.currentQueryIndex=this.currentQueryIndex,o.currentQueryTotal=this.currentQueryTotal,o.parentContext=this,this.subContextCount++,o}transformIntoNewTimeline(t){return this.previousNode=el,this.currentTimeline=this.currentTimeline.fork(this.element,t),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(t,n,r){let i={duration:n??t.duration,delay:this.currentTimeline.currentTime+(r??0)+t.delay,easing:""},o=new Kd(this._driver,t.element,t.keyframes,t.preStyleProps,t.postStyleProps,i,t.stretchStartingKeyframe);return this.timelines.push(o),i}incrementTime(t){this.currentTimeline.forwardTime(this.currentTimeline.duration+t)}delayNextStep(t){t>0&&this.currentTimeline.delayNextStep(t)}invokeQuery(t,n,r,i,o,s){let a=[];if(i&&a.push(this.element),t.length>0){t=t.replace(WM,"."+this._enterClassName),t=t.replace(QM,"."+this._leaveClassName);let l=r!=1,c=this._driver.query(this.element,t,l);r!==0&&(c=r<0?c.slice(c.length+r,c.length):c.slice(0,r)),a.push(...c)}return!o&&a.length==0&&s.push(cM(n)),a}},tl=class e{constructor(t,n,r,i){this._driver=t,this.element=n,this.startTime=r,this._elementTimelineStylesLookup=i,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(n),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(n,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(t){let n=this._keyframes.size===1&&this._pendingStyles.size;this.duration||n?(this.forwardTime(this.currentTime+t),n&&this.snapshotCurrentStyles()):this.startTime+=t}fork(t,n){return this.applyStylesToKeyframe(),new e(this._driver,t,n||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=GM,this._loadKeyframe()}forwardTime(t){this.applyStylesToKeyframe(),this.duration=t,this._loadKeyframe()}_updateStyle(t,n){this._localTimelineStyles.set(t,n),this._globalTimelineStyles.set(t,n),this._styleSummary.set(t,{time:this.currentTime,value:n})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(t){t&&this._previousKeyframe.set("easing",t);for(let[n,r]of this._globalTimelineStyles)this._backFill.set(n,r||Lt),this._currentKeyframe.set(n,Lt);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(t,n,r,i){n&&this._previousKeyframe.set("easing",n);let o=i&&i.params||{},s=YM(t,this._globalTimelineStyles);for(let[a,l]of s){let c=Ki(l,o,r);this._pendingStyles.set(a,c),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??Lt),this._updateStyle(a,c)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((t,n)=>{this._currentKeyframe.set(n,t)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((t,n)=>{this._currentKeyframe.has(n)||this._currentKeyframe.set(n,t)}))}snapshotCurrentStyles(){for(let[t,n]of this._localTimelineStyles)this._pendingStyles.set(t,n),this._updateStyle(t,n)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let t=[];for(let n in this._currentKeyframe)t.push(n);return t}mergeTimelineCollectedStyles(t){t._styleSummary.forEach((n,r)=>{let i=this._styleSummary.get(r);(!i||n.time>i.time)&&this._updateStyle(r,n.value)})}buildKeyframes(){this.applyStylesToKeyframe();let t=new Set,n=new Set,r=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((a,l)=>{let c=new Map([...this._backFill,...a]);c.forEach((u,d)=>{u===za?t.add(d):u===Lt&&n.add(d)}),r||c.set("offset",l/this.duration),i.push(c)});let o=[...t.values()],s=[...n.values()];if(r){let a=i[0],l=new Map(a);a.set("offset",0),l.set("offset",1),i=[a,l]}return ff(this.element,i,o,s,this.duration,this.startTime,this.easing,!1)}},Kd=class extends tl{constructor(t,n,r,i,o,s,a=!1){super(t,n,s.delay),this.keyframes=r,this.preStyleProps=i,this.postStyleProps=o,this._stretchStartingKeyframe=a,this.timings={duration:s.duration,delay:s.delay,easing:s.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let t=this.keyframes,{delay:n,duration:r,easing:i}=this.timings;if(this._stretchStartingKeyframe&&n){let o=[],s=r+n,a=n/s,l=new Map(t[0]);l.set("offset",0),o.push(l);let c=new Map(t[0]);c.set("offset",ay(a)),o.push(c);let u=t.length-1;for(let d=1;d<=u;d++){let g=new Map(t[d]),f=g.get("offset"),m=n+f*r;g.set("offset",ay(m/s)),o.push(g)}r=s,n=0,i="",t=o}return ff(this.element,t,this.preStyleProps,this.postStyleProps,r,n,i,!0)}};function ay(e,t=3){let n=Math.pow(10,t-1);return Math.round(e*n)/n}function YM(e,t){let n=new Map,r;return e.forEach(i=>{if(i==="*"){r??=t.keys();for(let o of r)n.set(o,Lt)}else for(let[o,s]of i)n.set(o,s)}),n}function ly(e,t,n,r,i,o,s,a,l,c,u,d,g){return{type:0,element:e,triggerName:t,isRemovalTransition:i,fromState:n,fromStyles:o,toState:r,toStyles:s,timelines:a,queriedElements:l,preStyleProps:c,postStyleProps:u,totalTime:d,errors:g}}var jd={},nl=class{constructor(t,n,r){this._triggerName=t,this.ast=n,this._stateStyles=r}match(t,n,r,i){return KM(this.ast.matchers,t,n,r,i)}buildStyles(t,n,r){let i=this._stateStyles.get("*");return t!==void 0&&(i=this._stateStyles.get(t?.toString())||i),i?i.buildStyles(n,r):new Map}build(t,n,r,i,o,s,a,l,c,u){let d=[],g=this.ast.options&&this.ast.options.params||jd,f=a&&a.params||jd,m=this.buildStyles(r,f,d),v=l&&l.params||jd,_=this.buildStyles(i,v,d),S=new Set,V=new Map,N=new Map,G=i==="void",he={params:Ey(v,g),delay:this.ast.options?.delay},te=u?[]:by(t,n,this.ast.animation,o,s,m,_,he,c,d),le=0;return te.forEach(Ie=>{le=Math.max(Ie.duration+Ie.delay,le)}),d.length?ly(n,this._triggerName,r,i,G,m,_,[],[],V,N,le,d):(te.forEach(Ie=>{let Bt=Ie.element,vr=Je(V,Bt,new Set);Ie.preStyleProps.forEach(jn=>vr.add(jn));let oh=Je(N,Bt,new Set);Ie.postStyleProps.forEach(jn=>oh.add(jn)),Bt!==n&&S.add(Bt)}),ly(n,this._triggerName,r,i,G,m,_,te,[...S.values()],V,N,le))}};function KM(e,t,n,r,i){return e.some(o=>o(t,n,r,i))}function Ey(e,t){let n=w({},t);return Object.entries(e).forEach(([r,i])=>{i!=null&&(n[r]=i)}),n}var Xd=class{constructor(t,n,r){this.styles=t,this.defaultParams=n,this.normalizer=r}buildStyles(t,n){let r=new Map,i=Ey(t,this.defaultParams);return this.styles.styles.forEach(o=>{typeof o!="string"&&o.forEach((s,a)=>{s&&(s=Ki(s,i,n));let l=this.normalizer.normalizePropertyName(a,n);s=this.normalizer.normalizeStyleValue(a,l,s,n),r.set(a,s)})}),r}};function XM(e,t,n){return new Jd(e,t,n)}var Jd=class{constructor(t,n,r){this.name=t,this.ast=n,this._normalizer=r,this.transitionFactories=[],this.states=new Map,n.states.forEach(i=>{let o=i.options&&i.options.params||{};this.states.set(i.name,new Xd(i.style,o,r))}),cy(this.states,"true","1"),cy(this.states,"false","0"),n.transitions.forEach(i=>{this.transitionFactories.push(new nl(t,i,this.states))}),this.fallbackTransition=JM(t,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(t,n,r,i){return this.transitionFactories.find(s=>s.match(t,n,r,i))||null}matchStyles(t,n,r){return this.fallbackTransition.buildStyles(t,n,r)}};function JM(e,t,n){let r=[(s,a)=>!0],i={type:$.Sequence,steps:[],options:null},o={type:$.Transition,animation:i,matchers:r,options:null,queryCount:0,depCount:0};return new nl(e,o,t)}function cy(e,t,n){e.has(t)?e.has(n)||e.set(n,e.get(t)):e.has(n)&&e.set(t,e.get(n))}var e3=new Xi,ef=class{constructor(t,n,r){this.bodyNode=t,this._driver=n,this._normalizer=r,this._animations=new Map,this._playersById=new Map,this.players=[]}register(t,n){let r=[],i=[],o=wy(this._driver,n,r,i);if(r.length)throw pM(r);i.length&&void 0,this._animations.set(t,o)}_buildPlayer(t,n,r){let i=t.element,o=gy(this._normalizer,t.keyframes,n,r);return this._driver.animate(i,o,t.duration,t.delay,t.easing,[],!0)}create(t,n,r={}){let i=[],o=this._animations.get(t),s,a=new Map;if(o?(s=by(this._driver,n,o,Cy,Hd,new Map,new Map,r,e3,i),s.forEach(u=>{let d=Je(a,u.element,new Map);u.postStyleProps.forEach(g=>d.set(g,null))})):(i.push(gM()),s=[]),i.length)throw mM(i);a.forEach((u,d)=>{u.forEach((g,f)=>{u.set(f,this._driver.computeStyle(d,f,Lt))})});let l=s.map(u=>{let d=a.get(u.element);return this._buildPlayer(u,new Map,d)}),c=Rn(l);return this._playersById.set(t,c),c.onDestroy(()=>this.destroy(t)),this.players.push(c),c}destroy(t){let n=this._getPlayer(t);n.destroy(),this._playersById.delete(t);let r=this.players.indexOf(n);r>=0&&this.players.splice(r,1)}_getPlayer(t){let n=this._playersById.get(t);if(!n)throw yM(t);return n}listen(t,n,r,i){let o=af(n,"","","");return sf(this._getPlayer(t),r,o,i),()=>{}}command(t,n,r,i){if(r=="register"){this.register(t,i[0]);return}if(r=="create"){let s=i[0]||{};this.create(t,n,s);return}let o=this._getPlayer(t);switch(r){case"play":o.play();break;case"pause":o.pause();break;case"reset":o.reset();break;case"restart":o.restart();break;case"finish":o.finish();break;case"init":o.init();break;case"setPosition":o.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(t);break}}},uy="ng-animate-queued",t3=".ng-animate-queued",Bd="ng-animate-disabled",n3=".ng-animate-disabled",r3="ng-star-inserted",i3=".ng-star-inserted",o3=[],My={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},s3={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},wt="__ng_removed",Ji=class{get params(){return this.options.params}constructor(t,n=""){this.namespaceId=n;let r=t&&t.hasOwnProperty("value"),i=r?t.value:t;if(this.value=l3(i),r){let o=t,{value:s}=o,a=jl(o,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(t){let n=t.params;if(n){let r=this.options.params;Object.keys(n).forEach(i=>{r[i]==null&&(r[i]=n[i])})}}},Yi="void",Ud=new Ji(Yi),tf=class{constructor(t,n,r){this.id=t,this.hostElement=n,this._engine=r,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+t,dt(n,this._hostClassName)}listen(t,n,r,i){if(!this._triggers.has(n))throw vM(r,n);if(r==null||r.length==0)throw CM(n);if(!c3(r))throw DM(r,n);let o=Je(this._elementListeners,t,[]),s={name:n,phase:r,callback:i};o.push(s);let a=Je(this._engine.statesByElement,t,new Map);return a.has(n)||(dt(t,Ga),dt(t,Ga+"-"+n),a.set(n,Ud)),()=>{this._engine.afterFlush(()=>{let l=o.indexOf(s);l>=0&&o.splice(l,1),this._triggers.has(n)||a.delete(n)})}}register(t,n){return this._triggers.has(t)?!1:(this._triggers.set(t,n),!0)}_getTrigger(t){let n=this._triggers.get(t);if(!n)throw _M(t);return n}trigger(t,n,r,i=!0){let o=this._getTrigger(n),s=new eo(this.id,n,t),a=this._engine.statesByElement.get(t);a||(dt(t,Ga),dt(t,Ga+"-"+n),this._engine.statesByElement.set(t,a=new Map));let l=a.get(n),c=new Ji(r,this.id);if(!(r&&r.hasOwnProperty("value"))&&l&&c.absorbOptions(l.options),a.set(n,c),l||(l=Ud),!(c.value===Yi)&&l.value===c.value){if(!f3(l.params,c.params)){let v=[],_=o.matchStyles(l.value,l.params,v),S=o.matchStyles(c.value,c.params,v);v.length?this._engine.reportError(v):this._engine.afterFlush(()=>{fr(t,_),Vt(t,S)})}return}let g=Je(this._engine.playersByElement,t,[]);g.forEach(v=>{v.namespaceId==this.id&&v.triggerName==n&&v.queued&&v.destroy()});let f=o.matchTransition(l.value,c.value,t,c.params),m=!1;if(!f){if(!i)return;f=o.fallbackTransition,m=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:n,transition:f,fromState:l,toState:c,player:s,isFallbackTransition:m}),m||(dt(t,uy),s.onStart(()=>{ei(t,uy)})),s.onDone(()=>{let v=this.players.indexOf(s);v>=0&&this.players.splice(v,1);let _=this._engine.playersByElement.get(t);if(_){let S=_.indexOf(s);S>=0&&_.splice(S,1)}}),this.players.push(s),g.push(s),s}deregister(t){this._triggers.delete(t),this._engine.statesByElement.forEach(n=>n.delete(t)),this._elementListeners.forEach((n,r)=>{this._elementListeners.set(r,n.filter(i=>i.name!=t))})}clearElementCache(t){this._engine.statesByElement.delete(t),this._elementListeners.delete(t);let n=this._engine.playersByElement.get(t);n&&(n.forEach(r=>r.destroy()),this._engine.playersByElement.delete(t))}_signalRemovalForInnerTriggers(t,n){let r=this._engine.driver.query(t,Ya,!0);r.forEach(i=>{if(i[wt])return;let o=this._engine.fetchNamespacesByElement(i);o.size?o.forEach(s=>s.triggerLeaveAnimation(i,n,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>r.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(t,n,r,i){let o=this._engine.statesByElement.get(t),s=new Map;if(o){let a=[];if(o.forEach((l,c)=>{if(s.set(c,l.value),this._triggers.has(c)){let u=this.trigger(t,c,Yi,i);u&&a.push(u)}}),a.length)return this._engine.markElementAsRemoved(this.id,t,!0,n,s),r&&Rn(a).onDone(()=>this._engine.processLeaveNode(t)),!0}return!1}prepareLeaveAnimationListeners(t){let n=this._elementListeners.get(t),r=this._engine.statesByElement.get(t);if(n&&r){let i=new Set;n.forEach(o=>{let s=o.name;if(i.has(s))return;i.add(s);let l=this._triggers.get(s).fallbackTransition,c=r.get(s)||Ud,u=new Ji(Yi),d=new eo(this.id,s,t);this._engine.totalQueuedPlayers++,this._queue.push({element:t,triggerName:s,transition:l,fromState:c,toState:u,player:d,isFallbackTransition:!0})})}}removeNode(t,n){let r=this._engine;if(t.childElementCount&&this._signalRemovalForInnerTriggers(t,n),this.triggerLeaveAnimation(t,n,!0))return;let i=!1;if(r.totalAnimations){let o=r.players.length?r.playersByQueriedElement.get(t):[];if(o&&o.length)i=!0;else{let s=t;for(;s=s.parentNode;)if(r.statesByElement.get(s)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(t),i)r.markElementAsRemoved(this.id,t,!1,n);else{let o=t[wt];(!o||o===My)&&(r.afterFlush(()=>this.clearElementCache(t)),r.destroyInnerAnimations(t),r._onRemovalComplete(t,n))}}insertNode(t,n){dt(t,this._hostClassName)}drainQueuedTransitions(t){let n=[];return this._queue.forEach(r=>{let i=r.player;if(i.destroyed)return;let o=r.element,s=this._elementListeners.get(o);s&&s.forEach(a=>{if(a.name==r.triggerName){let l=af(o,r.triggerName,r.fromState.value,r.toState.value);l._data=t,sf(r.player,a.phase,l,a.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):n.push(r)}),this._queue=[],n.sort((r,i)=>{let o=r.transition.ast.depCount,s=i.transition.ast.depCount;return o==0||s==0?o-s:this._engine.driver.containsElement(r.element,i.element)?1:-1})}destroy(t){this.players.forEach(n=>n.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,t)}},nf=class{_onRemovalComplete(t,n){this.onRemovalComplete(t,n)}constructor(t,n,r){this.bodyNode=t,this.driver=n,this._normalizer=r,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(i,o)=>{}}get queuedPlayers(){let t=[];return this._namespaceList.forEach(n=>{n.players.forEach(r=>{r.queued&&t.push(r)})}),t}createNamespace(t,n){let r=new tf(t,n,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,n)?this._balanceNamespaceList(r,n):(this.newHostElements.set(n,r),this.collectEnterElement(n)),this._namespaceLookup[t]=r}_balanceNamespaceList(t,n){let r=this._namespaceList,i=this.namespacesByHostElement;if(r.length-1>=0){let s=!1,a=this.driver.getParentElement(n);for(;a;){let l=i.get(a);if(l){let c=r.indexOf(l);r.splice(c+1,0,t),s=!0;break}a=this.driver.getParentElement(a)}s||r.unshift(t)}else r.push(t);return i.set(n,t),t}register(t,n){let r=this._namespaceLookup[t];return r||(r=this.createNamespace(t,n)),r}registerTrigger(t,n,r){let i=this._namespaceLookup[t];i&&i.register(n,r)&&this.totalAnimations++}destroy(t,n){t&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let r=this._fetchNamespace(t);this.namespacesByHostElement.delete(r.hostElement);let i=this._namespaceList.indexOf(r);i>=0&&this._namespaceList.splice(i,1),r.destroy(n),delete this._namespaceLookup[t]}))}_fetchNamespace(t){return this._namespaceLookup[t]}fetchNamespacesByElement(t){let n=new Set,r=this.statesByElement.get(t);if(r){for(let i of r.values())if(i.namespaceId){let o=this._fetchNamespace(i.namespaceId);o&&n.add(o)}}return n}trigger(t,n,r,i){if(Za(n)){let o=this._fetchNamespace(t);if(o)return o.trigger(n,r,i),!0}return!1}insertNode(t,n,r,i){if(!Za(n))return;let o=n[wt];if(o&&o.setForRemoval){o.setForRemoval=!1,o.setForMove=!0;let s=this.collectedLeaveElements.indexOf(n);s>=0&&this.collectedLeaveElements.splice(s,1)}if(t){let s=this._fetchNamespace(t);s&&s.insertNode(n,r)}i&&this.collectEnterElement(n)}collectEnterElement(t){this.collectedEnterElements.push(t)}markElementAsDisabled(t,n){n?this.disabledNodes.has(t)||(this.disabledNodes.add(t),dt(t,Bd)):this.disabledNodes.has(t)&&(this.disabledNodes.delete(t),ei(t,Bd))}removeNode(t,n,r){if(Za(n)){let i=t?this._fetchNamespace(t):null;i?i.removeNode(n,r):this.markElementAsRemoved(t,n,!1,r);let o=this.namespacesByHostElement.get(n);o&&o.id!==t&&o.removeNode(n,r)}else this._onRemovalComplete(n,r)}markElementAsRemoved(t,n,r,i,o){this.collectedLeaveElements.push(n),n[wt]={namespaceId:t,setForRemoval:i,hasAnimation:r,removedBeforeQueried:!1,previousTriggersValues:o}}listen(t,n,r,i,o){return Za(n)?this._fetchNamespace(t).listen(n,r,i,o):()=>{}}_buildInstruction(t,n,r,i,o){return t.transition.build(this.driver,t.element,t.fromState.value,t.toState.value,r,i,t.fromState.options,t.toState.options,n,o)}destroyInnerAnimations(t){let n=this.driver.query(t,Ya,!0);n.forEach(r=>this.destroyActiveAnimationsForElement(r)),this.playersByQueriedElement.size!=0&&(n=this.driver.query(t,zd,!0),n.forEach(r=>this.finishActiveQueriedAnimationOnElement(r)))}destroyActiveAnimationsForElement(t){let n=this.playersByElement.get(t);n&&n.forEach(r=>{r.queued?r.markedForDestroy=!0:r.destroy()})}finishActiveQueriedAnimationOnElement(t){let n=this.playersByQueriedElement.get(t);n&&n.forEach(r=>r.finish())}whenRenderingDone(){return new Promise(t=>{if(this.players.length)return Rn(this.players).onDone(()=>t());t()})}processLeaveNode(t){let n=t[wt];if(n&&n.setForRemoval){if(t[wt]=My,n.namespaceId){this.destroyInnerAnimations(t);let r=this._fetchNamespace(n.namespaceId);r&&r.clearElementCache(t)}this._onRemovalComplete(t,n.setForRemoval)}t.classList?.contains(Bd)&&this.markElementAsDisabled(t,!1),this.driver.query(t,n3,!0).forEach(r=>{this.markElementAsDisabled(r,!1)})}flush(t=-1){let n=[];if(this.newHostElements.size&&(this.newHostElements.forEach((r,i)=>this._balanceNamespaceList(r,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let r=0;r<this.collectedEnterElements.length;r++){let i=this.collectedEnterElements[r];dt(i,r3)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let r=[];try{n=this._flushAnimations(r,t)}finally{for(let i=0;i<r.length;i++)r[i]()}}else for(let r=0;r<this.collectedLeaveElements.length;r++){let i=this.collectedLeaveElements[r];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(r=>r()),this._flushFns=[],this._whenQuietFns.length){let r=this._whenQuietFns;this._whenQuietFns=[],n.length?Rn(n).onDone(()=>{r.forEach(i=>i())}):r.forEach(i=>i())}}reportError(t){throw wM(t)}_flushAnimations(t,n){let r=new Xi,i=[],o=new Map,s=[],a=new Map,l=new Map,c=new Map,u=new Set;this.disabledNodes.forEach(I=>{u.add(I);let x=this.driver.query(I,t3,!0);for(let T=0;T<x.length;T++)u.add(x[T])});let d=this.bodyNode,g=Array.from(this.statesByElement.keys()),f=hy(g,this.collectedEnterElements),m=new Map,v=0;f.forEach((I,x)=>{let T=Cy+v++;m.set(x,T),I.forEach(Q=>dt(Q,T))});let _=[],S=new Set,V=new Set;for(let I=0;I<this.collectedLeaveElements.length;I++){let x=this.collectedLeaveElements[I],T=x[wt];T&&T.setForRemoval&&(_.push(x),S.add(x),T.hasAnimation?this.driver.query(x,i3,!0).forEach(Q=>S.add(Q)):V.add(x))}let N=new Map,G=hy(g,Array.from(S));G.forEach((I,x)=>{let T=Hd+v++;N.set(x,T),I.forEach(Q=>dt(Q,T))}),t.push(()=>{f.forEach((I,x)=>{let T=m.get(x);I.forEach(Q=>ei(Q,T))}),G.forEach((I,x)=>{let T=N.get(x);I.forEach(Q=>ei(Q,T))}),_.forEach(I=>{this.processLeaveNode(I)})});let he=[],te=[];for(let I=this._namespaceList.length-1;I>=0;I--)this._namespaceList[I].drainQueuedTransitions(n).forEach(T=>{let Q=T.player,Se=T.element;if(he.push(Q),this.collectedEnterElements.length){let Oe=Se[wt];if(Oe&&Oe.setForMove){if(Oe.previousTriggersValues&&Oe.previousTriggersValues.has(T.triggerName)){let Bn=Oe.previousTriggersValues.get(T.triggerName),nt=this.statesByElement.get(T.element);if(nt&&nt.has(T.triggerName)){let To=nt.get(T.triggerName);To.value=Bn,nt.set(T.triggerName,To)}}Q.destroy();return}}let It=!d||!this.driver.containsElement(d,Se),qe=N.get(Se),un=m.get(Se),fe=this._buildInstruction(T,r,un,qe,It);if(fe.errors&&fe.errors.length){te.push(fe);return}if(It){Q.onStart(()=>fr(Se,fe.fromStyles)),Q.onDestroy(()=>Vt(Se,fe.toStyles)),i.push(Q);return}if(T.isFallbackTransition){Q.onStart(()=>fr(Se,fe.fromStyles)),Q.onDestroy(()=>Vt(Se,fe.toStyles)),i.push(Q);return}let lh=[];fe.timelines.forEach(Oe=>{Oe.stretchStartingKeyframe=!0,this.disabledNodes.has(Oe.element)||lh.push(Oe)}),fe.timelines=lh,r.append(Se,fe.timelines);let J1={instruction:fe,player:Q,element:Se};s.push(J1),fe.queriedElements.forEach(Oe=>Je(a,Oe,[]).push(Q)),fe.preStyleProps.forEach((Oe,Bn)=>{if(Oe.size){let nt=l.get(Bn);nt||l.set(Bn,nt=new Set),Oe.forEach((To,Vl)=>nt.add(Vl))}}),fe.postStyleProps.forEach((Oe,Bn)=>{let nt=c.get(Bn);nt||c.set(Bn,nt=new Set),Oe.forEach((To,Vl)=>nt.add(Vl))})});if(te.length){let I=[];te.forEach(x=>{I.push(bM(x.triggerName,x.errors))}),he.forEach(x=>x.destroy()),this.reportError(I)}let le=new Map,Ie=new Map;s.forEach(I=>{let x=I.element;r.has(x)&&(Ie.set(x,x),this._beforeAnimationBuild(I.player.namespaceId,I.instruction,le))}),i.forEach(I=>{let x=I.element;this._getPreviousPlayers(x,!1,I.namespaceId,I.triggerName,null).forEach(Q=>{Je(le,x,[]).push(Q),Q.destroy()})});let Bt=_.filter(I=>py(I,l,c)),vr=new Map;fy(vr,this.driver,V,c,Lt).forEach(I=>{py(I,l,c)&&Bt.push(I)});let jn=new Map;f.forEach((I,x)=>{fy(jn,this.driver,new Set(I),l,za)}),Bt.forEach(I=>{let x=vr.get(I),T=jn.get(I);vr.set(I,new Map([...x?.entries()??[],...T?.entries()??[]]))});let Ll=[],sh=[],ah={};s.forEach(I=>{let{element:x,player:T,instruction:Q}=I;if(r.has(x)){if(u.has(x)){T.onDestroy(()=>Vt(x,Q.toStyles)),T.disabled=!0,T.overrideTotalTime(Q.totalTime),i.push(T);return}let Se=ah;if(Ie.size>1){let qe=x,un=[];for(;qe=qe.parentNode;){let fe=Ie.get(qe);if(fe){Se=fe;break}un.push(qe)}un.forEach(fe=>Ie.set(fe,Se))}let It=this._buildAnimation(T.namespaceId,Q,le,o,jn,vr);if(T.setRealPlayer(It),Se===ah)Ll.push(T);else{let qe=this.playersByElement.get(Se);qe&&qe.length&&(T.parentPlayer=Rn(qe)),i.push(T)}}else fr(x,Q.fromStyles),T.onDestroy(()=>Vt(x,Q.toStyles)),sh.push(T),u.has(x)&&i.push(T)}),sh.forEach(I=>{let x=o.get(I.element);if(x&&x.length){let T=Rn(x);I.setRealPlayer(T)}}),i.forEach(I=>{I.parentPlayer?I.syncPlayerEvents(I.parentPlayer):I.destroy()});for(let I=0;I<_.length;I++){let x=_[I],T=x[wt];if(ei(x,Hd),T&&T.hasAnimation)continue;let Q=[];if(a.size){let It=a.get(x);It&&It.length&&Q.push(...It);let qe=this.driver.query(x,zd,!0);for(let un=0;un<qe.length;un++){let fe=a.get(qe[un]);fe&&fe.length&&Q.push(...fe)}}let Se=Q.filter(It=>!It.destroyed);Se.length?u3(this,x,Se):this.processLeaveNode(x)}return _.length=0,Ll.forEach(I=>{this.players.push(I),I.onDone(()=>{I.destroy();let x=this.players.indexOf(I);this.players.splice(x,1)}),I.play()}),Ll}afterFlush(t){this._flushFns.push(t)}afterFlushAnimationsDone(t){this._whenQuietFns.push(t)}_getPreviousPlayers(t,n,r,i,o){let s=[];if(n){let a=this.playersByQueriedElement.get(t);a&&(s=a)}else{let a=this.playersByElement.get(t);if(a){let l=!o||o==Yi;a.forEach(c=>{c.queued||!l&&c.triggerName!=i||s.push(c)})}}return(r||i)&&(s=s.filter(a=>!(r&&r!=a.namespaceId||i&&i!=a.triggerName))),s}_beforeAnimationBuild(t,n,r){let i=n.triggerName,o=n.element,s=n.isRemovalTransition?void 0:t,a=n.isRemovalTransition?void 0:i;for(let l of n.timelines){let c=l.element,u=c!==o,d=Je(r,c,[]);this._getPreviousPlayers(c,u,s,a,n.toState).forEach(f=>{let m=f.getRealPlayer();m.beforeDestroy&&m.beforeDestroy(),f.destroy(),d.push(f)})}fr(o,n.fromStyles)}_buildAnimation(t,n,r,i,o,s){let a=n.triggerName,l=n.element,c=[],u=new Set,d=new Set,g=n.timelines.map(m=>{let v=m.element;u.add(v);let _=v[wt];if(_&&_.removedBeforeQueried)return new kn(m.duration,m.delay);let S=v!==l,V=d3((r.get(v)||o3).map(le=>le.getRealPlayer())).filter(le=>{let Ie=le;return Ie.element?Ie.element===v:!1}),N=o.get(v),G=s.get(v),he=gy(this._normalizer,m.keyframes,N,G),te=this._buildPlayer(m,he,V);if(m.subTimeline&&i&&d.add(v),S){let le=new eo(t,a,v);le.setRealPlayer(te),c.push(le)}return te});c.forEach(m=>{Je(this.playersByQueriedElement,m.element,[]).push(m),m.onDone(()=>a3(this.playersByQueriedElement,m.element,m))}),u.forEach(m=>dt(m,iy));let f=Rn(g);return f.onDestroy(()=>{u.forEach(m=>ei(m,iy)),Vt(l,n.toStyles)}),d.forEach(m=>{Je(i,m,[]).push(f)}),f}_buildPlayer(t,n,r){return n.length>0?this.driver.animate(t.element,n,t.duration,t.delay,t.easing,r):new kn(t.duration,t.delay)}},eo=class{constructor(t,n,r){this.namespaceId=t,this.triggerName=n,this.element=r,this._player=new kn,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(t){this._containsRealPlayer||(this._player=t,this._queuedCallbacks.forEach((n,r)=>{n.forEach(i=>sf(t,r,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(t.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(t){this.totalTime=t}syncPlayerEvents(t){let n=this._player;n.triggerCallback&&t.onStart(()=>n.triggerCallback("start")),t.onDone(()=>this.finish()),t.onDestroy(()=>this.destroy())}_queueEvent(t,n){Je(this._queuedCallbacks,t,[]).push(n)}onDone(t){this.queued&&this._queueEvent("done",t),this._player.onDone(t)}onStart(t){this.queued&&this._queueEvent("start",t),this._player.onStart(t)}onDestroy(t){this.queued&&this._queueEvent("destroy",t),this._player.onDestroy(t)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(t){this.queued||this._player.setPosition(t)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(t){let n=this._player;n.triggerCallback&&n.triggerCallback(t)}};function a3(e,t,n){let r=e.get(t);if(r){if(r.length){let i=r.indexOf(n);r.splice(i,1)}r.length==0&&e.delete(t)}return r}function l3(e){return e??null}function Za(e){return e&&e.nodeType===1}function c3(e){return e=="start"||e=="done"}function dy(e,t){let n=e.style.display;return e.style.display=t??"none",n}function fy(e,t,n,r,i){let o=[];n.forEach(l=>o.push(dy(l)));let s=[];r.forEach((l,c)=>{let u=new Map;l.forEach(d=>{let g=t.computeStyle(c,d,i);u.set(d,g),(!g||g.length==0)&&(c[wt]=s3,s.push(c))}),e.set(c,u)});let a=0;return n.forEach(l=>dy(l,o[a++])),s}function hy(e,t){let n=new Map;if(e.forEach(a=>n.set(a,[])),t.length==0)return n;let r=1,i=new Set(t),o=new Map;function s(a){if(!a)return r;let l=o.get(a);if(l)return l;let c=a.parentNode;return n.has(c)?l=c:i.has(c)?l=r:l=s(c),o.set(a,l),l}return t.forEach(a=>{let l=s(a);l!==r&&n.get(l).push(a)}),n}function dt(e,t){e.classList?.add(t)}function ei(e,t){e.classList?.remove(t)}function u3(e,t,n){Rn(n).onDone(()=>e.processLeaveNode(t))}function d3(e){let t=[];return Iy(e,t),t}function Iy(e,t){for(let n=0;n<e.length;n++){let r=e[n];r instanceof Zi?Iy(r.players,t):t.push(r)}}function f3(e,t){let n=Object.keys(e),r=Object.keys(t);if(n.length!=r.length)return!1;for(let i=0;i<n.length;i++){let o=n[i];if(!t.hasOwnProperty(o)||e[o]!==t[o])return!1}return!0}function py(e,t,n){let r=n.get(e);if(!r)return!1;let i=t.get(e);return i?r.forEach(o=>i.add(o)):t.set(e,r),n.delete(e),!0}var ti=class{constructor(t,n,r){this._driver=n,this._normalizer=r,this._triggerCache={},this.onRemovalComplete=(i,o)=>{},this._transitionEngine=new nf(t.body,n,r),this._timelineEngine=new ef(t.body,n,r),this._transitionEngine.onRemovalComplete=(i,o)=>this.onRemovalComplete(i,o)}registerTrigger(t,n,r,i,o){let s=t+"-"+i,a=this._triggerCache[s];if(!a){let l=[],c=[],u=wy(this._driver,o,l,c);if(l.length)throw fM(i,l);c.length&&void 0,a=XM(i,u,this._normalizer),this._triggerCache[s]=a}this._transitionEngine.registerTrigger(n,i,a)}register(t,n){this._transitionEngine.register(t,n)}destroy(t,n){this._transitionEngine.destroy(t,n)}onInsert(t,n,r,i){this._transitionEngine.insertNode(t,n,r,i)}onRemove(t,n,r){this._transitionEngine.removeNode(t,n,r)}disableAnimations(t,n){this._transitionEngine.markElementAsDisabled(t,n)}process(t,n,r,i){if(r.charAt(0)=="@"){let[o,s]=ny(r),a=i;this._timelineEngine.command(o,n,s,a)}else this._transitionEngine.trigger(t,n,r,i)}listen(t,n,r,i,o){if(r.charAt(0)=="@"){let[s,a]=ny(r);return this._timelineEngine.listen(s,n,a,o)}return this._transitionEngine.listen(t,n,r,i,o)}flush(t=-1){this._transitionEngine.flush(t)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(t){this._transitionEngine.afterFlushAnimationsDone(t)}};function h3(e,t){let n=null,r=null;return Array.isArray(t)&&t.length?(n=$d(t[0]),t.length>1&&(r=$d(t[t.length-1]))):t instanceof Map&&(n=$d(t)),n||r?new rf(e,n,r):null}var rf=class e{static{this.initialStylesByElement=new WeakMap}constructor(t,n,r){this._element=t,this._startStyles=n,this._endStyles=r,this._state=0;let i=e.initialStylesByElement.get(t);i||e.initialStylesByElement.set(t,i=new Map),this._initialStyles=i}start(){this._state<1&&(this._startStyles&&Vt(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(Vt(this._element,this._initialStyles),this._endStyles&&(Vt(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(e.initialStylesByElement.delete(this._element),this._startStyles&&(fr(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(fr(this._element,this._endStyles),this._endStyles=null),Vt(this._element,this._initialStyles),this._state=3)}};function $d(e){let t=null;return e.forEach((n,r)=>{p3(r)&&(t=t||new Map,t.set(r,n))}),t}function p3(e){return e==="display"||e==="position"}var rl=class{constructor(t,n,r,i){this.element=t,this.keyframes=n,this.options=r,this._specialStyles=i,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=r.duration,this._delay=r.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let t=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,t,this.options),this._finalKeyframe=t.length?t[t.length-1]:new Map;let n=()=>this._onFinish();this.domPlayer.addEventListener("finish",n),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",n)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(t){let n=[];return t.forEach(r=>{n.push(Object.fromEntries(r))}),n}_triggerWebAnimation(t,n,r){return t.animate(this._convertKeyframesToObject(n),r)}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(t=>t()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}setPosition(t){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=t*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let t=new Map;this.hasStarted()&&this._finalKeyframe.forEach((r,i)=>{i!=="offset"&&t.set(i,this._finished?r:df(this.element,i))}),this.currentSnapshot=t}triggerCallback(t){let n=t==="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},il=class{validateStyleProperty(t){return!0}validateAnimatableStyleProperty(t){return!0}containsElement(t,n){return my(t,n)}getParentElement(t){return lf(t)}query(t,n,r){return yy(t,n,r)}computeStyle(t,n,r){return df(t,n)}animate(t,n,r,i,o,s=[]){let a=i==0?"both":"forwards",l={duration:r,delay:i,fill:a};o&&(l.easing=o);let c=new Map,u=s.filter(f=>f instanceof rl);kM(r,i)&&u.forEach(f=>{f.currentSnapshot.forEach((m,v)=>c.set(v,m))});let d=NM(n).map(f=>new Map(f));d=RM(t,d,c);let g=h3(t,d);return new rl(t,d,l,g)}};var Qa="@",Sy="@.disabled",ol=class{constructor(t,n,r,i){this.namespaceId=t,this.delegate=n,this.engine=r,this._onDestroy=i,this.\u0275type=0}get data(){return this.delegate.data}destroyNode(t){this.delegate.destroyNode?.(t)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(t,n){return this.delegate.createElement(t,n)}createComment(t){return this.delegate.createComment(t)}createText(t){return this.delegate.createText(t)}appendChild(t,n){this.delegate.appendChild(t,n),this.engine.onInsert(this.namespaceId,n,t,!1)}insertBefore(t,n,r,i=!0){this.delegate.insertBefore(t,n,r),this.engine.onInsert(this.namespaceId,n,t,i)}removeChild(t,n,r){this.parentNode(n)&&this.engine.onRemove(this.namespaceId,n,this.delegate)}selectRootElement(t,n){return this.delegate.selectRootElement(t,n)}parentNode(t){return this.delegate.parentNode(t)}nextSibling(t){return this.delegate.nextSibling(t)}setAttribute(t,n,r,i){this.delegate.setAttribute(t,n,r,i)}removeAttribute(t,n,r){this.delegate.removeAttribute(t,n,r)}addClass(t,n){this.delegate.addClass(t,n)}removeClass(t,n){this.delegate.removeClass(t,n)}setStyle(t,n,r,i){this.delegate.setStyle(t,n,r,i)}removeStyle(t,n,r){this.delegate.removeStyle(t,n,r)}setProperty(t,n,r){n.charAt(0)==Qa&&n==Sy?this.disableAnimations(t,!!r):this.delegate.setProperty(t,n,r)}setValue(t,n){this.delegate.setValue(t,n)}listen(t,n,r){return this.delegate.listen(t,n,r)}disableAnimations(t,n){this.engine.disableAnimations(t,n)}},of=class extends ol{constructor(t,n,r,i,o){super(n,r,i,o),this.factory=t,this.namespaceId=n}setProperty(t,n,r){n.charAt(0)==Qa?n.charAt(1)=="."&&n==Sy?(r=r===void 0?!0:!!r,this.disableAnimations(t,r)):this.engine.process(this.namespaceId,t,n.slice(1),r):this.delegate.setProperty(t,n,r)}listen(t,n,r){if(n.charAt(0)==Qa){let i=g3(t),o=n.slice(1),s="";return o.charAt(0)!=Qa&&([o,s]=m3(o)),this.engine.listen(this.namespaceId,i,o,s,a=>{let l=a._data||-1;this.factory.scheduleListenerCallback(l,r,a)})}return this.delegate.listen(t,n,r)}};function g3(e){switch(e){case"body":return document.body;case"document":return document;case"window":return window;default:return e}}function m3(e){let t=e.indexOf("."),n=e.substring(0,t),r=e.slice(t+1);return[n,r]}var sl=class{constructor(t,n,r){this.delegate=t,this.engine=n,this._zone=r,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,n.onRemovalComplete=(i,o)=>{o?.removeChild(null,i)}}createRenderer(t,n){let r="",i=this.delegate.createRenderer(t,n);if(!t||!n?.data?.animation){let c=this._rendererCache,u=c.get(i);if(!u){let d=()=>c.delete(i);u=new ol(r,i,this.engine,d),c.set(i,u)}return u}let o=n.id,s=n.id+"-"+this._currentId;this._currentId++,this.engine.register(s,t);let a=c=>{Array.isArray(c)?c.forEach(a):this.engine.registerTrigger(o,s,t,c.name,c)};return n.data.animation.forEach(a),new of(this,s,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(t,n,r){if(t>=0&&t<this._microtaskId){this._zone.run(()=>n(r));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(o=>{let[s,a]=o;s(a)}),this._animationCallbacksBuffer=[]})}),i.push([n,r])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}};var v3=(()=>{class e extends ti{constructor(n,r,i){super(n,r,i)}ngOnDestroy(){this.flush()}static{this.\u0275fac=function(r){return new(r||e)(M(Me),M(hr),M(pr))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function C3(){return new Xa}function D3(e,t,n){return new sl(e,t,n)}var Ty=[{provide:pr,useFactory:C3},{provide:ti,useClass:v3},{provide:wn,useFactory:D3,deps:[Ua,ti,K]}],xy=[{provide:hr,useFactory:()=>new il},{provide:Vu,useValue:"BrowserAnimations"},...Ty],_3=[{provide:hr,useClass:cf},{provide:Vu,useValue:"NoopAnimations"},...Ty],Ay=(()=>{class e{static withConfig(n){return{ngModule:e,providers:n.disableAnimations?_3:xy}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({providers:xy,imports:[Ha]})}}return e})();var w3=new E(""),b3=new E("");function Ry(e){return e!=null}function Fy(e){return or(e)?ce(e):e}function Ly(e){let t={};return e.forEach(n=>{t=n!=null?w(w({},t),n):t}),Object.keys(t).length===0?null:t}function Vy(e,t){return t.map(n=>n(e))}function E3(e){return!e.validate}function jy(e){return e.map(t=>E3(t)?t:n=>t.validate(n))}function M3(e){if(!e)return null;let t=e.filter(Ry);return t.length==0?null:function(n){return Ly(Vy(n,t))}}function yf(e){return e!=null?M3(jy(e)):null}function I3(e){if(!e)return null;let t=e.filter(Ry);return t.length==0?null:function(n){let r=Vy(n,t).map(Fy);return rc(r).pipe(R(Ly))}}function vf(e){return e!=null?I3(jy(e)):null}function Ny(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function S3(e){return e._rawValidators}function x3(e){return e._rawAsyncValidators}function hf(e){return e?Array.isArray(e)?e:[e]:[]}function ll(e,t){return Array.isArray(e)?e.includes(t):e===t}function Oy(e,t){let n=hf(t);return hf(e).forEach(i=>{ll(n,i)||n.push(i)}),n}function Py(e,t){return hf(t).filter(n=>!ll(e,n))}var pf=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=yf(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=vf(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},so=class extends pf{get formDirective(){return null}get path(){return null}};var gf=class{constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},T3={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},$7=q(w({},T3),{"[class.ng-submitted]":"isSubmitted"});var By=(()=>{class e extends gf{constructor(n){super(n)}static{this.\u0275fac=function(r){return new(r||e)(U(so,10))}}static{this.\u0275dir=mt({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,i){r&2&&Dt("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)("ng-submitted",i.isSubmitted)},features:[ua]})}}return e})();var to="VALID",al="INVALID",ni="PENDING",no="DISABLED",ii=class{},cl=class extends ii{constructor(t,n){super(),this.value=t,this.source=n}},io=class extends ii{constructor(t,n){super(),this.pristine=t,this.source=n}},oo=class extends ii{constructor(t,n){super(),this.touched=t,this.source=n}},ri=class extends ii{constructor(t,n){super(),this.status=t,this.source=n}};function A3(e){return(Cf(e)?e.validators:e)||null}function N3(e){return Array.isArray(e)?yf(e):e||null}function O3(e,t){return(Cf(t)?t.asyncValidators:e)||null}function P3(e){return Array.isArray(e)?vf(e):e||null}function Cf(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function k3(e,t,n){let r=e.controls;if(!(t?Object.keys(r):r).length)throw new D(1e3,"");if(!r[n])throw new D(1001,"")}function R3(e,t,n){e._forEachChild((r,i)=>{if(n[i]===void 0)throw new D(1002,"")})}var mf=class{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=ji(()=>this.statusReactive()),this.statusReactive=ki(void 0),this._pristine=ji(()=>this.pristineReactive()),this.pristineReactive=ki(!0),this._touched=ji(()=>this.touchedReactive()),this.touchedReactive=ki(!1),this._events=new Ae,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return en(this.statusReactive)}set status(t){en(()=>this.statusReactive.set(t))}get valid(){return this.status===to}get invalid(){return this.status===al}get pending(){return this.status==ni}get disabled(){return this.status===no}get enabled(){return this.status!==no}get pristine(){return en(this.pristineReactive)}set pristine(t){en(()=>this.pristineReactive.set(t))}get dirty(){return!this.pristine}get touched(){return en(this.touchedReactive)}set touched(t){en(()=>this.touchedReactive.set(t))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Oy(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Oy(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Py(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Py(t,this._rawAsyncValidators))}hasValidator(t){return ll(this._rawValidators,t)}hasAsyncValidator(t){return ll(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(q(w({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new oo(!0,r))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new oo(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(q(w({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new io(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new io(!0,r))}markAsPending(t={}){this.status=ni;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new ri(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(q(w({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=no,this.errors=null,this._forEachChild(i=>{i.disable(q(w({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new cl(this.value,r)),this._events.next(new ri(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(q(w({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=to,this._forEachChild(r=>{r.enable(q(w({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(q(w({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===to||this.status===ni)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new cl(this.value,n)),this._events.next(new ri(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(q(w({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?no:to}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=ni,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let r=Fy(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(i=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(i,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,i)=>r&&r._find(i),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new ri(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new _e,this.statusChanges=new _e}_calculateStatus(){return this._allControlsDisabled()?no:this.errors?al:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(ni)?ni:this._anyControlsHaveStatus(al)?al:to}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),i=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),i&&this._events.next(new io(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new oo(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){Cf(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=N3(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=P3(this._rawAsyncValidators)}},ul=class extends mf{constructor(t,n,r){super(A3(n),O3(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,r={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){R3(this,!0,t),Object.keys(t).forEach(r=>{k3(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(Object.keys(t).forEach(r=>{let i=this.controls[r];i&&i.patchValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((r,i)=>{r.reset(t?t[i]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,r)=>(t[r]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,r)=>r._syncPendingControls()?!0:n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{let r=this.controls[n];r&&t(r,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[n,r]of Object.entries(this.controls))if(this.contains(n)&&t(r))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(n,r,i)=>((r.enabled||this.disabled)&&(n[i]=r.value),n))}_reduceChildren(t,n){let r=t;return this._forEachChild((i,o)=>{r=n(r,i,o)}),r}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var Df=new E("CallSetDisabledState",{providedIn:"root",factory:()=>dl}),dl="always";function F3(e,t,n=dl){Uy(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),V3(e,t),B3(e,t),j3(e,t),L3(e,t)}function ky(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function L3(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function Uy(e,t){let n=S3(e);t.validator!==null?e.setValidators(Ny(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=x3(e);t.asyncValidator!==null?e.setAsyncValidators(Ny(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let i=()=>e.updateValueAndValidity();ky(t._rawValidators,i),ky(t._rawAsyncValidators,i)}function V3(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&$y(e,t)})}function j3(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&$y(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function $y(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function B3(e,t){let n=(r,i)=>{t.valueAccessor.writeValue(r),i&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function U3(e,t){e==null,Uy(e,t)}function $3(e,t){e._syncPendingControls(),t.forEach(n=>{let r=n.control;r.updateOn==="submit"&&r._pendingChange&&(n.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}var H3={provide:so,useExisting:Ws(()=>_f)},ro=Promise.resolve(),_f=(()=>{class e extends so{get submitted(){return en(this.submittedReactive)}constructor(n,r,i){super(),this.callSetDisabledState=i,this._submitted=ji(()=>this.submittedReactive()),this.submittedReactive=ki(!1),this._directives=new Set,this.ngSubmit=new _e,this.form=new ul({},yf(n),vf(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){ro.then(()=>{let r=this._findContainer(n.path);n.control=r.registerControl(n.name,n.control),F3(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){ro.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){ro.then(()=>{let r=this._findContainer(n.path),i=new ul({});U3(i,n),r.registerControl(n.name,i),i.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){ro.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,r){ro.then(()=>{this.form.get(n.path).setValue(r)})}setValue(n){this.control.setValue(n)}onSubmit(n){return this.submittedReactive.set(!0),$3(this.form,this._directives),this.ngSubmit.emit(n),n?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}static{this.\u0275fac=function(r){return new(r||e)(U(w3,10),U(b3,10),U(Df,8))}}static{this.\u0275dir=mt({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,i){r&1&&ve("submit",function(s){return i.onSubmit(s)})("reset",function(){return i.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[Jm([H3]),ua]})}}return e})();var Hy=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=mt({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return e})();var z3=new E("");var zy=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({})}}return e})();var fl=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Df,useValue:n.callSetDisabledState??dl}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({imports:[zy]})}}return e})(),hl=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:z3,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:Df,useValue:n.callSetDisabledState??dl}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({imports:[zy]})}}return e})();var L="primary",_o=Symbol("RouteTitle"),If=class{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function ui(e){return new If(e)}function q3(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let i={};for(let o=0;o<r.length;o++){let s=r[o],a=e[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:i}}function W3(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!jt(e[n],t[n]))return!1;return!0}function jt(e,t){let n=e?Sf(e):void 0,r=t?Sf(t):void 0;if(!n||!r||n.length!=r.length)return!1;let i;for(let o=0;o<n.length;o++)if(i=n[o],!t1(e[i],t[i]))return!1;return!0}function Sf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function t1(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((i,o)=>r[o]===i)}else return e===t}function n1(e){return e.length>0?e[e.length-1]:null}function Vn(e){return nc(e)?e:or(e)?ce(Promise.resolve(e)):A(e)}var Z3={exact:i1,subset:o1},r1={exact:Q3,subset:Y3,ignored:()=>!0};function Gy(e,t,n){return Z3[n.paths](e.root,t.root,n.matrixParams)&&r1[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function Q3(e,t){return jt(e,t)}function i1(e,t,n){if(!mr(e.segments,t.segments)||!ml(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!i1(e.children[r],t.children[r],n))return!1;return!0}function Y3(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>t1(e[n],t[n]))}function o1(e,t,n){return s1(e,t,t.segments,n)}function s1(e,t,n,r){if(e.segments.length>n.length){let i=e.segments.slice(0,n.length);return!(!mr(i,n)||t.hasChildren()||!ml(i,n,r))}else if(e.segments.length===n.length){if(!mr(e.segments,n)||!ml(e.segments,n,r))return!1;for(let i in t.children)if(!e.children[i]||!o1(e.children[i],t.children[i],r))return!1;return!0}else{let i=n.slice(0,e.segments.length),o=n.slice(e.segments.length);return!mr(e.segments,i)||!ml(e.segments,i,r)||!e.children[L]?!1:s1(e.children[L],t,o,r)}}function ml(e,t,n){return t.every((r,i)=>r1[n](e[i].parameters,r.parameters))}var ln=class{constructor(t=new re([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=ui(this.queryParams),this._queryParamMap}toString(){return J3.serialize(this)}},re=class{constructor(t,n){this.segments=t,this.children=n,this.parent=null,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return yl(this)}},gr=class{constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=ui(this.parameters),this._parameterMap}toString(){return l1(this)}};function K3(e,t){return mr(e,t)&&e.every((n,r)=>jt(n.parameters,t[r].parameters))}function mr(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function X3(e,t){let n=[];return Object.entries(e.children).forEach(([r,i])=>{r===L&&(n=n.concat(t(i,r)))}),Object.entries(e.children).forEach(([r,i])=>{r!==L&&(n=n.concat(t(i,r)))}),n}var wo=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>new di,providedIn:"root"})}}return e})(),di=class{parse(t){let n=new Tf(t);return new ln(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${ao(t.root,!0)}`,r=n5(t.queryParams),i=typeof t.fragment=="string"?`#${e5(t.fragment)}`:"";return`${n}${r}${i}`}},J3=new di;function yl(e){return e.segments.map(t=>l1(t)).join("/")}function ao(e,t){if(!e.hasChildren())return yl(e);if(t){let n=e.children[L]?ao(e.children[L],!1):"",r=[];return Object.entries(e.children).forEach(([i,o])=>{i!==L&&r.push(`${i}:${ao(o,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=X3(e,(r,i)=>i===L?[ao(e.children[L],!1)]:[`${i}:${ao(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[L]!=null?`${yl(e)}/${n[0]}`:`${yl(e)}/(${n.join("//")})`}}function a1(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function pl(e){return a1(e).replace(/%3B/gi,";")}function e5(e){return encodeURI(e)}function xf(e){return a1(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function vl(e){return decodeURIComponent(e)}function qy(e){return vl(e.replace(/\+/g,"%20"))}function l1(e){return`${xf(e.path)}${t5(e.parameters)}`}function t5(e){return Object.entries(e).map(([t,n])=>`;${xf(t)}=${xf(n)}`).join("")}function n5(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(i=>`${pl(n)}=${pl(i)}`).join("&"):`${pl(n)}=${pl(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var r5=/^[^\/()?;#]+/;function wf(e){let t=e.match(r5);return t?t[0]:""}var i5=/^[^\/()?;=#]+/;function o5(e){let t=e.match(i5);return t?t[0]:""}var s5=/^[^=?&#]+/;function a5(e){let t=e.match(s5);return t?t[0]:""}var l5=/^[^&#]+/;function c5(e){let t=e.match(l5);return t?t[0]:""}var Tf=class{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new re([],{}):new re([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[L]=new re(t,n)),r}parseSegment(){let t=wf(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new D(4009,!1);return this.capture(t),new gr(vl(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=o5(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let i=wf(this.remaining);i&&(r=i,this.capture(r))}t[vl(n)]=vl(r)}parseQueryParam(t){let n=a5(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=c5(this.remaining);s&&(r=s,this.capture(r))}let i=qy(n),o=qy(r);if(t.hasOwnProperty(i)){let s=t[i];Array.isArray(s)||(s=[s],t[i]=s),s.push(o)}else t[i]=o}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=wf(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new D(4010,!1);let o;r.indexOf(":")>-1?(o=r.slice(0,r.indexOf(":")),this.capture(o),this.capture(":")):t&&(o=L);let s=this.parseChildren();n[o]=Object.keys(s).length===1?s[L]:new re([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new D(4011,!1)}};function c1(e){return e.segments.length>0?new re([],{[L]:e}):e}function u1(e){let t={};for(let[r,i]of Object.entries(e.children)){let o=u1(i);if(r===L&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))t[s]=a;else(o.segments.length>0||o.hasChildren())&&(t[r]=o)}let n=new re(e.segments,t);return u5(n)}function u5(e){if(e.numberOfChildren===1&&e.children[L]){let t=e.children[L];return new re(e.segments.concat(t.segments),t.children)}return e}function po(e){return e instanceof ln}function d5(e,t,n=null,r=null){let i=d1(e);return f1(i,t,n,r)}function d1(e){let t;function n(o){let s={};for(let l of o.children){let c=n(l);s[l.outlet]=c}let a=new re(o.url,s);return o===e&&(t=a),a}let r=n(e.root),i=c1(r);return t??i}function f1(e,t,n,r){let i=e;for(;i.parent;)i=i.parent;if(t.length===0)return bf(i,i,i,n,r);let o=f5(t);if(o.toRoot())return bf(i,i,new re([],{}),n,r);let s=h5(o,i,e),a=s.processChildren?uo(s.segmentGroup,s.index,o.commands):p1(s.segmentGroup,s.index,o.commands);return bf(i,s.segmentGroup,a,n,r)}function Cl(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function go(e){return typeof e=="object"&&e!=null&&e.outlets}function bf(e,t,n,r,i){let o={};r&&Object.entries(r).forEach(([l,c])=>{o[l]=Array.isArray(c)?c.map(u=>`${u}`):`${c}`});let s;e===t?s=n:s=h1(e,t,n);let a=c1(u1(s));return new ln(a,o,i)}function h1(e,t,n){let r={};return Object.entries(e.children).forEach(([i,o])=>{o===t?r[i]=n:r[i]=h1(o,t,n)}),new re(e.segments,r)}var Dl=class{constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Cl(r[0]))throw new D(4003,!1);let i=r.find(go);if(i&&i!==n1(r))throw new D(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function f5(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Dl(!0,0,e);let t=0,n=!1,r=e.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([l,c])=>{a[l]=typeof c=="string"?c.split("/"):c}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,l)=>{l==0&&a==="."||(l==0&&a===""?n=!0:a===".."?t++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new Dl(n,t,r)}var ai=class{constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function h5(e,t,n){if(e.isAbsolute)return new ai(t,!0,0);if(!n)return new ai(t,!1,NaN);if(n.parent===null)return new ai(n,!0,0);let r=Cl(e.commands[0])?0:1,i=n.segments.length-1+r;return p5(n,i,e.numberOfDoubleDots)}function p5(e,t,n){let r=e,i=t,o=n;for(;o>i;){if(o-=i,r=r.parent,!r)throw new D(4005,!1);i=r.segments.length}return new ai(r,!1,i-o)}function g5(e){return go(e[0])?e[0].outlets:{[L]:e}}function p1(e,t,n){if(e??=new re([],{}),e.segments.length===0&&e.hasChildren())return uo(e,t,n);let r=m5(e,t,n),i=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let o=new re(e.segments.slice(0,r.pathIndex),{});return o.children[L]=new re(e.segments.slice(r.pathIndex),e.children),uo(o,0,i)}else return r.match&&i.length===0?new re(e.segments,{}):r.match&&!e.hasChildren()?Af(e,t,n):r.match?uo(e,0,i):Af(e,t,n)}function uo(e,t,n){if(n.length===0)return new re(e.segments,{});{let r=g5(n),i={};if(Object.keys(r).some(o=>o!==L)&&e.children[L]&&e.numberOfChildren===1&&e.children[L].segments.length===0){let o=uo(e.children[L],t,n);return new re(e.segments,o.children)}return Object.entries(r).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=p1(e.children[o],t,s))}),Object.entries(e.children).forEach(([o,s])=>{r[o]===void 0&&(i[o]=s)}),new re(e.segments,i)}}function m5(e,t,n){let r=0,i=t,o={match:!1,pathIndex:0,commandIndex:0};for(;i<e.segments.length;){if(r>=n.length)return o;let s=e.segments[i],a=n[r];if(go(a))break;let l=`${a}`,c=r<n.length-1?n[r+1]:null;if(i>0&&l===void 0)break;if(l&&c&&typeof c=="object"&&c.outlets===void 0){if(!Zy(l,c,s))return o;r+=2}else{if(!Zy(l,{},s))return o;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function Af(e,t,n){let r=e.segments.slice(0,t),i=0;for(;i<n.length;){let o=n[i];if(go(o)){let l=y5(o.outlets);return new re(r,l)}if(i===0&&Cl(n[0])){let l=e.segments[t];r.push(new gr(l.path,Wy(n[0]))),i++;continue}let s=go(o)?o.outlets[L]:`${o}`,a=i<n.length-1?n[i+1]:null;s&&a&&Cl(a)?(r.push(new gr(s,Wy(a))),i+=2):(r.push(new gr(s,{})),i++)}return new re(r,{})}function y5(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=Af(new re([],{}),0,r))}),t}function Wy(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function Zy(e,t,n){return e==n.path&&jt(t,n.parameters)}var fo="imperative",Te=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(Te||{}),ft=class{constructor(t,n){this.id=t,this.url=n}},Fn=class extends ft{constructor(t,n,r="imperative",i=null){super(t,n),this.type=Te.NavigationStart,this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Mt=class extends ft{constructor(t,n,r){super(t,n),this.urlAfterRedirects=r,this.type=Te.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},tt=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(tt||{}),_l=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(_l||{}),Et=class extends ft{constructor(t,n,r,i){super(t,n),this.reason=r,this.code=i,this.type=Te.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Ln=class extends ft{constructor(t,n,r,i){super(t,n),this.reason=r,this.code=i,this.type=Te.NavigationSkipped}},yr=class extends ft{constructor(t,n,r,i){super(t,n),this.error=r,this.target=i,this.type=Te.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},wl=class extends ft{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=Te.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Nf=class extends ft{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=Te.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Of=class extends ft{constructor(t,n,r,i,o){super(t,n),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=o,this.type=Te.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Pf=class extends ft{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=Te.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},kf=class extends ft{constructor(t,n,r,i){super(t,n),this.urlAfterRedirects=r,this.state=i,this.type=Te.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Rf=class{constructor(t){this.route=t,this.type=Te.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Ff=class{constructor(t){this.route=t,this.type=Te.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Lf=class{constructor(t){this.snapshot=t,this.type=Te.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Vf=class{constructor(t){this.snapshot=t,this.type=Te.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},jf=class{constructor(t){this.snapshot=t,this.type=Te.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Bf=class{constructor(t){this.snapshot=t,this.type=Te.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},bl=class{constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r,this.type=Te.Scroll}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},mo=class{},fi=class{constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function v5(e,t){return e.providers&&!e._injector&&(e._injector=da(e.providers,t,`Route: ${e.path}`)),e._injector??t}function bt(e){return e.outlet||L}function C5(e,t){let n=e.filter(r=>bt(r)===t);return n.push(...e.filter(r=>bt(r)!==t)),n}function bo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Uf=class{get injector(){return bo(this.route?.snapshot)??this.rootInjector}set injector(t){}constructor(t){this.rootInjector=t,this.outlet=null,this.route=null,this.children=new Eo(this.rootInjector),this.attachRef=null}},Eo=(()=>{class e{constructor(n){this.rootInjector=n,this.contexts=new Map}onChildOutletCreated(n,r){let i=this.getOrCreateContext(n);i.outlet=r,this.contexts.set(n,i)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new Uf(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static{this.\u0275fac=function(r){return new(r||e)(M(Le))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),El=class{constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=$f(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=$f(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=Hf(t,this._root);return n.length<2?[]:n[n.length-2].children.map(i=>i.value).filter(i=>i!==t)}pathFromRoot(t){return Hf(t,this._root).map(n=>n.value)}};function $f(e,t){if(e===t.value)return t;for(let n of t.children){let r=$f(e,n);if(r)return r}return null}function Hf(e,t){if(e===t.value)return[t];for(let n of t.children){let r=Hf(e,n);if(r.length)return r.unshift(t),r}return[]}var et=class{constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function si(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Ml=class extends El{constructor(t,n){super(t),this.snapshot=n,Xf(this,t)}toString(){return this.snapshot.toString()}};function g1(e){let t=D5(e),n=new pe([new gr("",{})]),r=new pe({}),i=new pe({}),o=new pe({}),s=new pe(""),a=new hi(n,r,o,s,i,L,e,t.root);return a.snapshot=t.root,new Ml(new et(a,[]),t)}function D5(e){let t={},n={},r={},i="",o=new li([],t,r,i,n,L,e,null,{});return new Sl("",new et(o,[]))}var hi=class{constructor(t,n,r,i,o,s,a,l){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=l,this.title=this.dataSubject?.pipe(R(c=>c[_o]))??A(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(R(t=>ui(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(R(t=>ui(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Il(e,t,n="emptyOnly"){let r,{routeConfig:i}=e;return t!==null&&(n==="always"||i?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:w(w({},t.params),e.params),data:w(w({},t.data),e.data),resolve:w(w(w(w({},e.data),t.data),i?.data),e._resolvedData)}:r={params:w({},e.params),data:w({},e.data),resolve:w(w({},e.data),e._resolvedData??{})},i&&y1(i)&&(r.resolve[_o]=i.title),r}var li=class{get title(){return this.data?.[_o]}constructor(t,n,r,i,o,s,a,l,c){this.url=t,this.params=n,this.queryParams=r,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=l,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ui(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ui(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},Sl=class extends El{constructor(t,n){super(n),this.url=t,Xf(this,n)}toString(){return m1(this._root)}};function Xf(e,t){t.value._routerState=e,t.children.forEach(n=>Xf(e,n))}function m1(e){let t=e.children.length>0?` { ${e.children.map(m1).join(", ")} } `:"";return`${e.value}${t}`}function Ef(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,jt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),jt(t.params,n.params)||e.paramsSubject.next(n.params),W3(t.url,n.url)||e.urlSubject.next(n.url),jt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function zf(e,t){let n=jt(e.params,t.params)&&K3(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||zf(e.parent,t.parent))}function y1(e){return typeof e.title=="string"||e.title===null}var Jf=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=L,this.activateEvents=new _e,this.deactivateEvents=new _e,this.attachEvents=new _e,this.detachEvents=new _e,this.parentContexts=C(Eo),this.location=C(Mn),this.changeDetector=C(sr),this.inputBinder=C(Ol,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:i}=n.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new D(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new D(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new D(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new D(4013,!1);this._activatedRoute=n;let i=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,l=new Gf(n,a,i.injector);this.activated=i.createComponent(s,{index:i.length,injector:l,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=mt({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[Wr]})}}return e})(),Gf=class e{__ngOutletInjector(t){return new e(this.route,this.childContexts,t)}constructor(t,n,r){this.route=t,this.childContexts=n,this.parent=r}get(t,n){return t===hi?this.route:t===Eo?this.childContexts:this.parent.get(t,n)}},Ol=new E(""),Qy=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,i=Di([r.queryParams,r.params,r.data]).pipe(Ue(([o,s,a],l)=>(a=w(w(w({},o),s),a),l===0?A(a):Promise.resolve(a)))).subscribe(o=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=p0(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(n,i)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function _5(e,t,n){let r=yo(e,t._root,n?n._root:void 0);return new Ml(r,t)}function yo(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let i=w5(e,t,n);return new et(r,i)}else{if(e.shouldAttach(t.value)){let o=e.retrieve(t.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>yo(e,a)),s}}let r=b5(t.value),i=t.children.map(o=>yo(e,o));return new et(r,i)}}function w5(e,t,n){return t.children.map(r=>{for(let i of n.children)if(e.shouldReuseRoute(r.value,i.value.snapshot))return yo(e,r,i);return yo(e,r)})}function b5(e){return new hi(new pe(e.url),new pe(e.params),new pe(e.queryParams),new pe(e.fragment),new pe(e.data),e.outlet,e.component,e)}var vo=class{constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},v1="ngNavigationCancelingError";function xl(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=po(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,i=C1(!1,tt.Redirect);return i.url=n,i.navigationBehaviorOptions=r,i}function C1(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[v1]=!0,n.cancellationCode=t,n}function E5(e){return D1(e)&&po(e.url)}function D1(e){return!!e&&e[v1]}var M5=(e,t,n,r)=>R(i=>(new qf(t,i.targetRouterState,i.currentRouterState,n,r).activate(e),i)),qf=class{constructor(t,n,r,i,o){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=o}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Ef(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let i=si(n);t.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],r),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,r)})}deactivateRoutes(t,n,r){let i=t.value,o=n?n.value:null;if(i===o)if(i.component){let s=r.getContext(i.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else o&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),i=r&&t.value.component?r.children:n,o=si(t);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),i=r&&t.value.component?r.children:n,o=si(t);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let i=si(n);t.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],r),this.forwardEvent(new Bf(o.value.snapshot))}),t.children.length&&this.forwardEvent(new Vf(t.value.snapshot))}activateRoutes(t,n,r){let i=t.value,o=n?n.value:null;if(Ef(i),i===o)if(i.component){let s=r.getOrCreateContext(i.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(i.component){let s=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Ef(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},Tl=class{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},ci=class{constructor(t,n){this.component=t,this.route=n}};function I5(e,t,n){let r=e._root,i=t?t._root:null;return lo(r,i,n,[r.value])}function S5(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function gi(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!jp(e)?e:t.get(e):r}function lo(e,t,n,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=si(t);return e.children.forEach(s=>{x5(s,o[s.value.outlet],n,r.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>ho(a,n.getContext(s),i)),i}function x5(e,t,n,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let l=T5(s,o,o.routeConfig.runGuardsAndResolvers);l?i.canActivateChecks.push(new Tl(r)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?lo(e,t,a?a.children:null,r,i):lo(e,t,n,r,i),l&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new ci(a.outlet.component,s))}else s&&ho(t,a,i),i.canActivateChecks.push(new Tl(r)),o.component?lo(e,null,a?a.children:null,r,i):lo(e,null,n,r,i);return i}function T5(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!mr(e.url,t.url);case"pathParamsOrQueryParamsChange":return!mr(e.url,t.url)||!jt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!zf(e,t)||!jt(e.queryParams,t.queryParams);case"paramsChange":default:return!zf(e,t)}}function ho(e,t,n){let r=si(e),i=e.value;Object.entries(r).forEach(([o,s])=>{i.component?t?ho(s,t.children.getContext(o),n):ho(s,null,n):ho(s,t,n)}),i.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new ci(t.outlet.component,i)):n.canDeactivateChecks.push(new ci(null,i)):n.canDeactivateChecks.push(new ci(null,i))}function Mo(e){return typeof e=="function"}function A5(e){return typeof e=="boolean"}function N5(e){return e&&Mo(e.canLoad)}function O5(e){return e&&Mo(e.canActivate)}function P5(e){return e&&Mo(e.canActivateChild)}function k5(e){return e&&Mo(e.canDeactivate)}function R5(e){return e&&Mo(e.canMatch)}function _1(e){return e instanceof Ut||e?.name==="EmptyError"}var gl=Symbol("INITIAL_VALUE");function pi(){return Ue(e=>Di(e.map(t=>t.pipe(Ht(1),ac(gl)))).pipe(R(t=>{for(let n of t)if(n!==!0){if(n===gl)return gl;if(n===!1||F5(n))return n}return!0}),Be(t=>t!==gl),Ht(1)))}function F5(e){return po(e)||e instanceof vo}function L5(e,t){return De(n=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=n;return s.length===0&&o.length===0?A(q(w({},n),{guardsResult:!0})):V5(s,r,i,e).pipe(De(a=>a&&A5(a)?j5(r,o,e,t):A(a)),R(a=>q(w({},n),{guardsResult:a})))})}function V5(e,t,n,r){return ce(e).pipe(De(i=>z5(i.component,i.route,n,t,r)),St(i=>i!==!0,!0))}function j5(e,t,n,r){return ce(t).pipe($t(i=>xr(U5(i.route.parent,r),B5(i.route,r),H5(e,i.path,n),$5(e,i.route,n))),St(i=>i!==!0,!0))}function B5(e,t){return e!==null&&t&&t(new jf(e)),A(!0)}function U5(e,t){return e!==null&&t&&t(new Lf(e)),A(!0)}function $5(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return A(!0);let i=r.map(o=>os(()=>{let s=bo(t)??n,a=gi(o,s),l=O5(a)?a.canActivate(t,e):ot(s,()=>a(t,e));return Vn(l).pipe(St())}));return A(i).pipe(pi())}function H5(e,t,n){let r=t[t.length-1],o=t.slice(0,t.length-1).reverse().map(s=>S5(s)).filter(s=>s!==null).map(s=>os(()=>{let a=s.guards.map(l=>{let c=bo(s.node)??n,u=gi(l,c),d=P5(u)?u.canActivateChild(r,e):ot(c,()=>u(r,e));return Vn(d).pipe(St())});return A(a).pipe(pi())}));return A(o).pipe(pi())}function z5(e,t,n,r,i){let o=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!o||o.length===0)return A(!0);let s=o.map(a=>{let l=bo(t)??i,c=gi(a,l),u=k5(c)?c.canDeactivate(e,t,n,r):ot(l,()=>c(e,t,n,r));return Vn(u).pipe(St())});return A(s).pipe(pi())}function G5(e,t,n,r){let i=t.canLoad;if(i===void 0||i.length===0)return A(!0);let o=i.map(s=>{let a=gi(s,e),l=N5(a)?a.canLoad(t,n):ot(e,()=>a(t,n));return Vn(l)});return A(o).pipe(pi(),w1(r))}function w1(e){return Xl(xe(t=>{if(typeof t!="boolean")throw xl(e,t)}),R(t=>t===!0))}function q5(e,t,n,r){let i=t.canMatch;if(!i||i.length===0)return A(!0);let o=i.map(s=>{let a=gi(s,e),l=R5(a)?a.canMatch(t,n):ot(e,()=>a(t,n));return Vn(l)});return A(o).pipe(pi(),w1(r))}var Co=class{constructor(t){this.segmentGroup=t||null}},Do=class extends Error{constructor(t){super(),this.urlTree=t}};function oi(e){return Ir(new Co(e))}function W5(e){return Ir(new D(4e3,!1))}function Z5(e){return Ir(C1(!1,tt.GuardRejected))}var Wf=class{constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],i=n.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return A(r);if(i.numberOfChildren>1||!i.children[L])return W5(`${t.redirectTo}`);i=i.children[L]}}applyRedirectCommands(t,n,r,i,o){if(typeof n!="string"){let a=n,{queryParams:l,fragment:c,routeConfig:u,url:d,outlet:g,params:f,data:m,title:v}=i,_=ot(o,()=>a({params:f,data:m,queryParams:l,fragment:c,routeConfig:u,url:d,outlet:g,title:v}));if(_ instanceof ln)throw new Do(_);n=_}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new Do(s);return s}applyRedirectCreateUrlTree(t,n,r,i){let o=this.createSegmentGroup(t,n.root,r,i);return new ln(o,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);r[i]=n[a]}else r[i]=o}),r}createSegmentGroup(t,n,r,i){let o=this.createSegments(t,n.segments,r,i),s={};return Object.entries(n.children).forEach(([a,l])=>{s[a]=this.createSegmentGroup(t,l,r,i)}),new re(o,s)}createSegments(t,n,r,i){return n.map(o=>o.path[0]===":"?this.findPosParam(t,o,i):this.findOrReturn(o,r))}findPosParam(t,n,r){let i=r[n.path.substring(1)];if(!i)throw new D(4001,!1);return i}findOrReturn(t,n){let r=0;for(let i of n){if(i.path===t.path)return n.splice(r),i;r++}return t}},Zf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Q5(e,t,n,r,i){let o=b1(e,t,n);return o.matched?(r=v5(t,r),q5(r,t,n,i).pipe(R(s=>s===!0?o:w({},Zf)))):A(o)}function b1(e,t,n){if(t.path==="**")return Y5(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?w({},Zf):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let i=(t.matcher||q3)(n,e,t);if(!i)return w({},Zf);let o={};Object.entries(i.posParams??{}).forEach(([a,l])=>{o[a]=l.path});let s=i.consumed.length>0?w(w({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:n.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function Y5(e){return{matched:!0,parameters:e.length>0?n1(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Yy(e,t,n,r){return n.length>0&&J5(e,n,r)?{segmentGroup:new re(t,X5(r,new re(n,e.children))),slicedSegments:[]}:n.length===0&&eI(e,n,r)?{segmentGroup:new re(e.segments,K5(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new re(e.segments,e.children),slicedSegments:n}}function K5(e,t,n,r){let i={};for(let o of n)if(Pl(e,t,o)&&!r[bt(o)]){let s=new re([],{});i[bt(o)]=s}return w(w({},r),i)}function X5(e,t){let n={};n[L]=t;for(let r of e)if(r.path===""&&bt(r)!==L){let i=new re([],{});n[bt(r)]=i}return n}function J5(e,t,n){return n.some(r=>Pl(e,t,r)&&bt(r)!==L)}function eI(e,t,n){return n.some(r=>Pl(e,t,r))}function Pl(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function tI(e,t,n){return t.length===0&&!e.children[n]}var Qf=class{};function nI(e,t,n,r,i,o,s="emptyOnly"){return new Yf(e,t,n,r,i,s,o).recognize()}var rI=31,Yf=class{constructor(t,n,r,i,o,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Wf(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(t){return new D(4002,`'${t.segmentGroup}'`)}recognize(){let t=Yy(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(R(({children:n,rootSnapshot:r})=>{let i=new et(r,n),o=new Sl("",i),s=d5(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(t){let n=new li([],Object.freeze({}),Object.freeze(w({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),L,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,L,n).pipe(R(r=>({children:r,rootSnapshot:n})),hn(r=>{if(r instanceof Do)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Co?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,i,o){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,o):this.processSegment(t,n,r,r.segments,i,!0,o).pipe(R(s=>s instanceof et?[s]:[]))}processChildren(t,n,r,i){let o=[];for(let s of Object.keys(r.children))s==="primary"?o.unshift(s):o.push(s);return ce(o).pipe($t(s=>{let a=r.children[s],l=C5(n,s);return this.processSegmentGroup(t,l,a,s,i)}),sc((s,a)=>(s.push(...a),s)),pn(null),oc(),De(s=>{if(s===null)return oi(r);let a=E1(s);return iI(a),A(a)}))}processSegment(t,n,r,i,o,s,a){return ce(n).pipe($t(l=>this.processSegmentAgainstRoute(l._injector??t,n,l,r,i,o,s,a).pipe(hn(c=>{if(c instanceof Co)return A(null);throw c}))),St(l=>!!l),hn(l=>{if(_1(l))return tI(r,i,o)?A(new Qf):oi(r);throw l}))}processSegmentAgainstRoute(t,n,r,i,o,s,a,l){return bt(r)!==s&&(s===L||!Pl(i,o,r))?oi(i):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,i,r,o,s,l):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,i,n,r,o,s,l):oi(i)}expandSegmentAgainstRouteUsingRedirect(t,n,r,i,o,s,a){let{matched:l,parameters:c,consumedSegments:u,positionalParamSegments:d,remainingSegments:g}=b1(n,i,o);if(!l)return oi(n);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>rI&&(this.allowRedirects=!1));let f=new li(o,c,Object.freeze(w({},this.urlTree.queryParams)),this.urlTree.fragment,Ky(i),bt(i),i.component??i._loadedComponent??null,i,Xy(i)),m=Il(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(m.params),f.data=Object.freeze(m.data);let v=this.applyRedirects.applyRedirectCommands(u,i.redirectTo,d,f,t);return this.applyRedirects.lineralizeSegments(i,v).pipe(De(_=>this.processSegment(t,r,n,_.concat(g),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,i,o,s){let a=Q5(n,r,i,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Ue(l=>l.matched?(t=r._injector??t,this.getChildConfig(t,r,i).pipe(Ue(({routes:c})=>{let u=r._loadedInjector??t,{parameters:d,consumedSegments:g,remainingSegments:f}=l,m=new li(g,d,Object.freeze(w({},this.urlTree.queryParams)),this.urlTree.fragment,Ky(r),bt(r),r.component??r._loadedComponent??null,r,Xy(r)),v=Il(m,s,this.paramsInheritanceStrategy);m.params=Object.freeze(v.params),m.data=Object.freeze(v.data);let{segmentGroup:_,slicedSegments:S}=Yy(n,g,f,c);if(S.length===0&&_.hasChildren())return this.processChildren(u,c,_,m).pipe(R(N=>new et(m,N)));if(c.length===0&&S.length===0)return A(new et(m,[]));let V=bt(r)===o;return this.processSegment(u,c,_,S,V?L:o,!0,m).pipe(R(N=>new et(m,N instanceof et?[N]:[])))}))):oi(n)))}getChildConfig(t,n,r){return n.children?A({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?A({routes:n._loadedRoutes,injector:n._loadedInjector}):G5(t,n,r,this.urlSerializer).pipe(De(i=>i?this.configLoader.loadChildren(t,n).pipe(xe(o=>{n._loadedRoutes=o.routes,n._loadedInjector=o.injector})):Z5(n))):A({routes:[],injector:t})}};function iI(e){e.sort((t,n)=>t.value.outlet===L?-1:n.value.outlet===L?1:t.value.outlet.localeCompare(n.value.outlet))}function oI(e){let t=e.value.routeConfig;return t&&t.path===""}function E1(e){let t=[],n=new Set;for(let r of e){if(!oI(r)){t.push(r);continue}let i=t.find(o=>r.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...r.children),n.add(i)):t.push(r)}for(let r of n){let i=E1(r.children);t.push(new et(r.value,i))}return t.filter(r=>!n.has(r))}function Ky(e){return e.data||{}}function Xy(e){return e.resolve||{}}function sI(e,t,n,r,i,o){return De(s=>nI(e,t,n,r,s.extractedUrl,i,o).pipe(R(({state:a,tree:l})=>q(w({},s),{targetSnapshot:a,urlAfterRedirects:l}))))}function aI(e,t){return De(n=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=n;if(!i.length)return A(n);let o=new Set(i.map(l=>l.route)),s=new Set;for(let l of o)if(!s.has(l))for(let c of M1(l))s.add(c);let a=0;return ce(s).pipe($t(l=>o.has(l)?lI(l,r,e,t):(l.data=Il(l,l.parent,e).resolve,A(void 0))),xe(()=>a++),Tr(1),De(l=>a===s.size?A(n):Ze))})}function M1(e){let t=e.children.map(n=>M1(n)).flat();return[e,...t]}function lI(e,t,n,r){let i=e.routeConfig,o=e._resolve;return i?.title!==void 0&&!y1(i)&&(o[_o]=i.title),cI(o,e,t,r).pipe(R(s=>(e._resolvedData=s,e.data=Il(e,e.parent,n).resolve,null)))}function cI(e,t,n,r){let i=Sf(e);if(i.length===0)return A({});let o={};return ce(i).pipe(De(s=>uI(e[s],t,n,r).pipe(St(),xe(a=>{if(a instanceof vo)throw xl(new di,a);o[s]=a}))),Tr(1),ic(o),hn(s=>_1(s)?Ze:Ir(s)))}function uI(e,t,n,r){let i=bo(t)??r,o=gi(e,i),s=o.resolve?o.resolve(t,n):ot(i,()=>o(t,n));return Vn(s)}function Mf(e){return Ue(t=>{let n=e(t);return n?ce(n).pipe(R(()=>t)):A(t)})}var I1=(()=>{class e{buildTitle(n){let r,i=n.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(o=>o.outlet===L);return r}getResolvedTitleForRoute(n){return n.data[_o]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>C(dI),providedIn:"root"})}}return e})(),dI=(()=>{class e extends I1{constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(M(X0))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Io=new E("",{providedIn:"root",factory:()=>({})}),fI=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=de({type:e,selectors:[["ng-component"]],standalone:!0,features:[e0],decls:1,vars:0,template:function(r,i){r&1&&y(0,"router-outlet")},dependencies:[Jf],encapsulation:2})}}return e})();function eh(e){let t=e.children&&e.children.map(eh),n=t?q(w({},e),{children:t}):w({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==L&&(n.component=fI),n}var Al=new E(""),th=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=C(va)}loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return A(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Vn(n.loadComponent()).pipe(R(S1),xe(o=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=o}),gn(()=>{this.componentLoaders.delete(n)})),i=new Mr(r,()=>new Ae).pipe(Er());return this.componentLoaders.set(n,i),i}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return A({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let o=hI(r,this.compiler,n,this.onLoadEndListener).pipe(gn(()=>{this.childrenLoaders.delete(r)})),s=new Mr(o,()=>new Ae).pipe(Er());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function hI(e,t,n,r){return Vn(e.loadChildren()).pipe(R(S1),De(i=>i instanceof xi||Array.isArray(i)?A(i):ce(t.compileModuleAsync(i))),R(i=>{r&&r(e);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(n).injector,s=o.get(Al,[],{optional:!0,self:!0}).flat()),{routes:s.map(eh),injector:o}}))}function pI(e){return e&&typeof e=="object"&&"default"in e}function S1(e){return pI(e)?e.default:e}var nh=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>C(gI),providedIn:"root"})}}return e})(),gI=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),x1=new E(""),T1=new E("");function mI(e,t,n){let r=e.get(T1),i=e.get(Me);return e.get(K).runOutsideAngular(()=>{if(!i.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(c=>setTimeout(c));let o,s=new Promise(c=>{o=c}),a=i.startViewTransition(()=>(o(),yI(e))),{onViewTransitionCreated:l}=r;return l&&ot(e,()=>l({transition:a,from:t,to:n})),s})}function yI(e){return new Promise(t=>{nd({read:()=>setTimeout(t)},{injector:e})})}var vI=new E(""),rh=(()=>{class e{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new Ae,this.transitionAbortSubject=new Ae,this.configLoader=C(th),this.environmentInjector=C(Le),this.urlSerializer=C(wo),this.rootContexts=C(Eo),this.location=C(Kr),this.inputBindingEnabled=C(Ol,{optional:!0})!==null,this.titleStrategy=C(I1),this.options=C(Io,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=C(nh),this.createViewTransition=C(x1,{optional:!0}),this.navigationErrorHandler=C(vI,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>A(void 0),this.rootComponentType=null;let n=i=>this.events.next(new Rf(i)),r=i=>this.events.next(new Ff(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(q(w(w({},this.transitions.value),n),{id:r}))}setupNavigations(n,r,i){return this.transitions=new pe({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:fo,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Be(o=>o.id!==0),R(o=>q(w({},o),{extractedUrl:this.urlHandlingStrategy.extract(o.rawUrl)})),Ue(o=>{let s=!1,a=!1;return A(o).pipe(Ue(l=>{if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",tt.SupersededByNewNavigation),Ze;this.currentTransition=o,this.currentNavigation={id:l.id,initialUrl:l.rawUrl,extractedUrl:l.extractedUrl,targetBrowserUrl:typeof l.extras.browserUrl=="string"?this.urlSerializer.parse(l.extras.browserUrl):l.extras.browserUrl,trigger:l.source,extras:l.extras,previousNavigation:this.lastSuccessfulNavigation?q(w({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let c=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),u=l.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!c&&u!=="reload"){let d="";return this.events.next(new Ln(l.id,this.urlSerializer.serialize(l.rawUrl),d,_l.IgnoredSameUrlNavigation)),l.resolve(!1),Ze}if(this.urlHandlingStrategy.shouldProcessUrl(l.rawUrl))return A(l).pipe(Ue(d=>{let g=this.transitions?.getValue();return this.events.next(new Fn(d.id,this.urlSerializer.serialize(d.extractedUrl),d.source,d.restoredState)),g!==this.transitions?.getValue()?Ze:Promise.resolve(d)}),sI(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),xe(d=>{o.targetSnapshot=d.targetSnapshot,o.urlAfterRedirects=d.urlAfterRedirects,this.currentNavigation=q(w({},this.currentNavigation),{finalUrl:d.urlAfterRedirects});let g=new wl(d.id,this.urlSerializer.serialize(d.extractedUrl),this.urlSerializer.serialize(d.urlAfterRedirects),d.targetSnapshot);this.events.next(g)}));if(c&&this.urlHandlingStrategy.shouldProcessUrl(l.currentRawUrl)){let{id:d,extractedUrl:g,source:f,restoredState:m,extras:v}=l,_=new Fn(d,this.urlSerializer.serialize(g),f,m);this.events.next(_);let S=g1(this.rootComponentType).snapshot;return this.currentTransition=o=q(w({},l),{targetSnapshot:S,urlAfterRedirects:g,extras:q(w({},v),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=g,A(o)}else{let d="";return this.events.next(new Ln(l.id,this.urlSerializer.serialize(l.extractedUrl),d,_l.IgnoredByUrlHandlingStrategy)),l.resolve(!1),Ze}}),xe(l=>{let c=new Nf(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(c)}),R(l=>(this.currentTransition=o=q(w({},l),{guards:I5(l.targetSnapshot,l.currentSnapshot,this.rootContexts)}),o)),L5(this.environmentInjector,l=>this.events.next(l)),xe(l=>{if(o.guardsResult=l.guardsResult,l.guardsResult&&typeof l.guardsResult!="boolean")throw xl(this.urlSerializer,l.guardsResult);let c=new Of(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot,!!l.guardsResult);this.events.next(c)}),Be(l=>l.guardsResult?!0:(this.cancelNavigationTransition(l,"",tt.GuardRejected),!1)),Mf(l=>{if(l.guards.canActivateChecks.length)return A(l).pipe(xe(c=>{let u=new Pf(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}),Ue(c=>{let u=!1;return A(c).pipe(aI(this.paramsInheritanceStrategy,this.environmentInjector),xe({next:()=>u=!0,complete:()=>{u||this.cancelNavigationTransition(c,"",tt.NoDataFromResolver)}}))}),xe(c=>{let u=new kf(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}))}),Mf(l=>{let c=u=>{let d=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(u.routeConfig).pipe(xe(g=>{u.component=g}),R(()=>{})));for(let g of u.children)d.push(...c(g));return d};return Di(c(l.targetSnapshot.root)).pipe(pn(null),Ht(1))}),Mf(()=>this.afterPreactivation()),Ue(()=>{let{currentSnapshot:l,targetSnapshot:c}=o,u=this.createViewTransition?.(this.environmentInjector,l.root,c.root);return u?ce(u).pipe(R(()=>o)):A(o)}),R(l=>{let c=_5(n.routeReuseStrategy,l.targetSnapshot,l.currentRouterState);return this.currentTransition=o=q(w({},l),{targetRouterState:c}),this.currentNavigation.targetRouterState=c,o}),xe(()=>{this.events.next(new mo)}),M5(this.rootContexts,n.routeReuseStrategy,l=>this.events.next(l),this.inputBindingEnabled),Ht(1),xe({next:l=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Mt(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects))),this.titleStrategy?.updateTitle(l.targetRouterState.snapshot),l.resolve(!0)},complete:()=>{s=!0}}),lc(this.transitionAbortSubject.pipe(xe(l=>{throw l}))),gn(()=>{!s&&!a&&this.cancelNavigationTransition(o,"",tt.SupersededByNewNavigation),this.currentTransition?.id===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),hn(l=>{if(a=!0,D1(l))this.events.next(new Et(o.id,this.urlSerializer.serialize(o.extractedUrl),l.message,l.cancellationCode)),E5(l)?this.events.next(new fi(l.url,l.navigationBehaviorOptions)):o.resolve(!1);else{let c=new yr(o.id,this.urlSerializer.serialize(o.extractedUrl),l,o.targetSnapshot??void 0);try{let u=ot(this.environmentInjector,()=>this.navigationErrorHandler?.(c));if(u instanceof vo){let{message:d,cancellationCode:g}=xl(this.urlSerializer,u);this.events.next(new Et(o.id,this.urlSerializer.serialize(o.extractedUrl),d,g)),this.events.next(new fi(u.redirectTo,u.navigationBehaviorOptions))}else{this.events.next(c);let d=n.errorHandler(l);o.resolve(!!d)}}catch(u){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(u)}}return Ze}))}))}cancelNavigationTransition(n,r,i){let o=new Et(n.id,this.urlSerializer.serialize(n.extractedUrl),r,i);this.events.next(o),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function CI(e){return e!==fo}var DI=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>C(_I),providedIn:"root"})}}return e})(),Kf=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},_I=(()=>{class e extends Kf{static{this.\u0275fac=(()=>{let n;return function(i){return(n||(n=ia(e)))(i||e)}})()}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),A1=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:()=>C(wI),providedIn:"root"})}}return e})(),wI=(()=>{class e extends A1{constructor(){super(...arguments),this.location=C(Kr),this.urlSerializer=C(wo),this.options=C(Io,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=C(nh),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new ln,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=g1(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&n(r.url,r.state)})}handleRouterEvent(n,r){if(n instanceof Fn)this.stateMemento=this.createStateMemento();else if(n instanceof Ln)this.rawUrlTree=r.initialUrl;else if(n instanceof wl){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??i,r)}}else n instanceof mo?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):n instanceof Et&&(n.code===tt.GuardRejected||n.code===tt.NoDataFromResolver)?this.restoreHistory(r):n instanceof yr?this.restoreHistory(r,!0):n instanceof Mt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,r){let i=n instanceof ln?this.urlSerializer.serialize(n):n;if(this.location.isCurrentPathEqualTo(i)||r.extras.replaceUrl){let o=this.browserPageId,s=w(w({},r.extras.state),this.generateNgRouterState(r.id,o));this.location.replaceState(i,"",s)}else{let o=w(w({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(i,"",o)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.currentUrlTree===n.finalUrl&&o===0&&(this.resetState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static{this.\u0275fac=(()=>{let n;return function(i){return(n||(n=ia(e)))(i||e)}})()}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),co=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(co||{});function N1(e,t){e.events.pipe(Be(n=>n instanceof Mt||n instanceof Et||n instanceof yr||n instanceof Ln),R(n=>n instanceof Mt||n instanceof Ln?co.COMPLETE:(n instanceof Et?n.code===tt.Redirect||n.code===tt.SupersededByNewNavigation:!1)?co.REDIRECTING:co.FAILED),Be(n=>n!==co.REDIRECTING),Ht(1)).subscribe(()=>{t()})}function bI(e){throw e}var EI={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},MI={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},cn=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=C(ha),this.stateManager=C(A1),this.options=C(Io,{optional:!0})||{},this.pendingTasks=C(Qt),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=C(rh),this.urlSerializer=C(wo),this.location=C(Kr),this.urlHandlingStrategy=C(nh),this._events=new Ae,this.errorHandler=this.options.errorHandler||bI,this.navigated=!1,this.routeReuseStrategy=C(DI),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=C(Al,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!C(Ol,{optional:!0}),this.eventsSubscription=new ue,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(r,o),r instanceof Et&&r.code!==tt.Redirect&&r.code!==tt.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Mt)this.navigated=!0;else if(r instanceof fi){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),l=w({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||CI(i.source)},s);this.scheduleNavigation(a,fo,null,l,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}SI(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),fo,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(n,"popstate",r)},0)})}navigateToSyncWithBrowser(n,r,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let l=w({},i);delete l.navigationId,delete l.\u0275routerPageId,Object.keys(l).length!==0&&(o.state=l)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,o)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(eh),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:l}=r,c=l?this.currentUrlTree.fragment:s,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=w(w({},this.currentUrlTree.queryParams),o);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=o||null}u!==null&&(u=this.removeEmptyProps(u));let d;try{let g=i?i.snapshot:this.routerState.snapshot.root;d=d1(g)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return f1(d,n,u,c??null)}navigateByUrl(n,r={skipLocationChange:!1}){let i=po(n)?n:this.parseUrl(n),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,fo,null,r)}navigate(n,r={skipLocationChange:!1}){return II(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let i;if(r===!0?i=w({},EI):r===!1?i=w({},MI):i=r,po(n))return Gy(this.currentUrlTree,n,i);let o=this.parseUrl(n);return Gy(this.currentUrlTree,o,i)}removeEmptyProps(n){return Object.entries(n).reduce((r,[i,o])=>(o!=null&&(r[i]=o),r),{})}scheduleNavigation(n,r,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,l,c;s?(a=s.resolve,l=s.reject,c=s.promise):c=new Promise((d,g)=>{a=d,l=g});let u=this.pendingTasks.add();return N1(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:o,resolve:a,reject:l,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(d=>Promise.reject(d))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function II(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new D(4008,!1)}function SI(e){return!(e instanceof mo)&&!(e instanceof fi)}var Nl=class{};var xI=(()=>{class e{constructor(n,r,i,o,s){this.router=n,this.injector=i,this.preloadingStrategy=o,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(Be(n=>n instanceof Mt),$t(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let i=[];for(let o of r){o.providers&&!o._injector&&(o._injector=da(o.providers,n,`Route: ${o.path}`));let s=o._injector??n,a=o._loadedInjector??s;(o.loadChildren&&!o._loadedRoutes&&o.canLoad===void 0||o.loadComponent&&!o._loadedComponent)&&i.push(this.preloadConfig(s,o)),(o.children||o._loadedRoutes)&&i.push(this.processRoutes(a,o.children??o._loadedRoutes))}return ce(i).pipe(Sr())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let i;r.loadChildren&&r.canLoad===void 0?i=this.loader.loadChildren(n,r):i=A(null);let o=i.pipe(De(s=>s===null?A(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return ce([o,s]).pipe(Sr())}else return o})}static{this.\u0275fac=function(r){return new(r||e)(M(cn),M(va),M(Le),M(Nl),M(th))}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),O1=new E(""),TI=(()=>{class e{constructor(n,r,i,o,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=i,this.zone=o,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Fn?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof Mt?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof Ln&&n.code===_l.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof bl&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new bl(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(r){Cm()}}static{this.\u0275prov=b({token:e,factory:e.\u0275fac})}}return e})();function AI(e){return e.routerState.root}function So(e,t){return{\u0275kind:e,\u0275providers:t}}function NI(){let e=C(He);return t=>{let n=e.get(Jt);if(t!==n.components[0])return;let r=e.get(cn),i=e.get(P1);e.get(ih)===1&&r.initialNavigation(),e.get(k1,null,j.Optional)?.setUpPreloading(),e.get(O1,null,j.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var P1=new E("",{factory:()=>new Ae}),ih=new E("",{providedIn:"root",factory:()=>1});function OI(){return So(2,[{provide:ih,useValue:0},{provide:ma,multi:!0,deps:[He],useFactory:t=>{let n=t.get(b0,Promise.resolve());return()=>n.then(()=>new Promise(r=>{let i=t.get(cn),o=t.get(P1);N1(i,()=>{r(!0)}),t.get(rh).afterPreactivation=()=>(r(!0),o.closed?A(void 0):o),i.initialNavigation()}))}}])}function PI(){return So(3,[{provide:ma,multi:!0,useFactory:()=>{let t=C(cn);return()=>{t.setUpLocationChangeListener()}}},{provide:ih,useValue:2}])}var k1=new E("");function kI(e){return So(0,[{provide:k1,useExisting:xI},{provide:Nl,useExisting:e}])}function RI(){return So(8,[Qy,{provide:Ol,useExisting:Qy}])}function FI(e){let t=[{provide:x1,useValue:mI},{provide:T1,useValue:w({skipNextTransition:!!e?.skipInitialTransition},e)}];return So(9,t)}var Jy=new E("ROUTER_FORROOT_GUARD"),LI=[Kr,{provide:wo,useClass:di},cn,Eo,{provide:hi,useFactory:AI,deps:[cn]},th,[]],xo=(()=>{class e{constructor(n){}static forRoot(n,r){return{ngModule:e,providers:[LI,[],{provide:Al,multi:!0,useValue:n},{provide:Jy,useFactory:UI,deps:[[cn,new Ys,new vu]]},{provide:Io,useValue:r||{}},r?.useHash?jI():BI(),VI(),r?.preloadingStrategy?kI(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?$I(r):[],r?.bindToComponentInputs?RI().\u0275providers:[],r?.enableViewTransitions?FI().\u0275providers:[],HI()]}}static forChild(n){return{ngModule:e,providers:[{provide:Al,multi:!0,useValue:n}]}}static{this.\u0275fac=function(r){return new(r||e)(M(Jy,8))}}static{this.\u0275mod=me({type:e})}static{this.\u0275inj=ge({})}}return e})();function VI(){return{provide:O1,useFactory:()=>{let e=C(A0),t=C(K),n=C(Io),r=C(rh),i=C(wo);return n.scrollOffset&&e.setOffset(n.scrollOffset),new TI(i,r,e,t,n)}}}function jI(){return{provide:cr,useClass:M0}}function BI(){return{provide:cr,useClass:Dd}}function UI(e){return"guarded"}function $I(e){return[e.initialNavigation==="disabled"?PI().\u0275providers:[],e.initialNavigation==="enabledBlocking"?OI().\u0275providers:[]]}var e1=new E("");function HI(){return[{provide:e1,useFactory:NI},{provide:ya,multi:!0,useExisting:e1}]}var kl=(()=>{class e{STORAGE_KEY="darkMode";darkModeSubject=new pe(!1);constructor(){this.initializeDarkMode()}get isDarkMode$(){return this.darkModeSubject.asObservable()}get isDarkMode(){return this.darkModeSubject.value}initializeDarkMode(){let n=localStorage.getItem(this.STORAGE_KEY);n!==null?this.setDarkMode(n==="true"):this.setDarkMode(!1),window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",r=>{localStorage.getItem(this.STORAGE_KEY)===null&&this.setDarkMode(r.matches)})}toggleDarkMode(){this.setDarkMode(!this.isDarkMode)}setDarkMode(n){this.darkModeSubject.next(n),this.updateDocumentClass(n),this.savePreference(n)}updateDocumentClass(n){let r=document.documentElement;n?r.classList.add("dark"):r.classList.remove("dark")}savePreference(n){localStorage.setItem(this.STORAGE_KEY,n.toString())}clearPreference(){localStorage.removeItem(this.STORAGE_KEY),this.setDarkMode(!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var F1=(()=>{class e{elementRef;darkModeService;vantaEffect;subscription=new ue;isDarkMode=!1;originalSettings={speedLimit:9,separation:58,cohesion:40};hoverSettings={speedLimit:25,separation:250,cohesion:10};lightThemeColors={backgroundColor:16171257,color1:11275825,color2:6750463,backgroundAlpha:0};darkThemeColors={backgroundColor:0,color1:16027569,color2:16758465,backgroundAlpha:.9};constructor(n,r){this.elementRef=n,this.darkModeService=r}ngOnInit(){this.subscription.add(this.darkModeService.isDarkMode$.subscribe(n=>{this.isDarkMode=n,this.updateVantaTheme()})),this.initializeVanta(),this.setupHoverEffects()}initializeVanta(){let n=this.isDarkMode?this.darkThemeColors:this.lightThemeColors;this.vantaEffect=VANTA.BIRDS({el:"#vanta-birds",mouseControls:!0,touchControls:!0,gyroControls:!1,minHeight:200,minWidth:200,scale:1,scaleMobile:1,backgroundColor:n.backgroundColor,color1:n.color1,color2:n.color2,birdSize:1.2,wingSpan:32,speedLimit:this.originalSettings.speedLimit,separation:this.originalSettings.separation,alignment:77,cohesion:this.originalSettings.cohesion,quantity:2,backgroundAlpha:n.backgroundAlpha})}updateVantaTheme(){if(this.vantaEffect){let n=this.isDarkMode?this.darkThemeColors:this.lightThemeColors;this.vantaEffect.setOptions({backgroundColor:n.backgroundColor,color1:n.color1,color2:n.color2,backgroundAlpha:n.backgroundAlpha})}}setupHoverEffects(){let n=this.elementRef.nativeElement.querySelector("#vanta-birds");n&&(n.addEventListener("mouseenter",()=>{this.onHoverStart()}),n.addEventListener("mouseleave",()=>{this.onHoverEnd()}))}onHoverStart(){this.vantaEffect&&this.vantaEffect.setOptions({speedLimit:this.hoverSettings.speedLimit,separation:this.hoverSettings.separation,cohesion:this.hoverSettings.cohesion})}onHoverEnd(){this.vantaEffect&&this.vantaEffect.setOptions({speedLimit:this.originalSettings.speedLimit,separation:this.originalSettings.separation,cohesion:this.originalSettings.cohesion})}ngOnDestroy(){this.subscription.unsubscribe(),this.vantaEffect&&this.vantaEffect.destroy()}static \u0275fac=function(r){return new(r||e)(U(at),U(kl))};static \u0275cmp=de({type:e,selectors:[["app-first"]],decls:7,vars:0,consts:[["id","vanta-birds",1,"hero-section"],[1,"profile-pic"],["ngSrc","images/roro.jpg","width","256","height","256","priority","","alt","Roaa Ayman"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"div",1),y(2,"img",2),p(),h(3,"h1"),O(4,"Roaa Ayman"),p(),h(5,"p"),O(6,"Front-End Developer | React & Angular | 3 Months of Hands-on Experience"),p()())},dependencies:[Jr],styles:[".hero-section[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;width:100vw;max-width:100%;color:var(--text-primary);overflow:hidden;cursor:pointer;transition:all .3s ease;background:transparent}.dark[_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]{background:transparent;color:var(--text-on-bg)}.hero-section[_ngcontent-%COMP%]:hover{filter:brightness(1.05)}.profile-pic[_ngcontent-%COMP%]{border-radius:50%;overflow:hidden;width:150px;height:150px;margin-bottom:20px;border:4px solid var(--border-pink);box-shadow:0 0 20px var(--shadow-pink);transition:all .3s ease}.profile-pic[_ngcontent-%COMP%]:hover{transform:scale(1.05);box-shadow:0 0 30px var(--shadow-pink)}.dark[_nghost-%COMP%]   .profile-pic[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .profile-pic[_ngcontent-%COMP%]{border-color:var(--primary-pink);box-shadow:0 0 25px var(--shadow-pink)}.dark[_nghost-%COMP%]   .profile-pic[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .profile-pic[_ngcontent-%COMP%]:hover{box-shadow:0 0 35px var(--shadow-pink);border-color:var(--primary-pink-light)}.profile-pic[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:10px;color:var(--text-primary);text-shadow:2px 2px 4px var(--shadow-pink);transition:all .3s ease;font-weight:700;text-align:center}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:20px;color:var(--text-secondary);transition:all .3s ease;text-align:center;max-width:600px;line-height:1.6}.dark[_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:var(--primary-pink);text-shadow:2px 2px 8px var(--shadow-pink)}.dark[_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--text-on-bg)}@media (max-width: 768px){.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;padding:0 20px}}.social-icons[_ngcontent-%COMP%]{display:flex;gap:15px}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--text-primary);font-size:1.5rem;transition:color .3s}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--accent-pink)}.dark[_nghost-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:var(--text-primary)}.dark[_nghost-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--accent-pink);filter:drop-shadow(0 0 8px var(--shadow-pink))}.hero-section[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{transition:color .3s ease,background-color .3s ease,border-color .3s ease,box-shadow .3s ease,text-shadow .3s ease}.hero-section[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .8s ease-out forwards;opacity:0;transform:translateY(30px)}.hero-section[_ngcontent-%COMP%]   .profile-pic[_ngcontent-%COMP%]{animation-delay:.2s}.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{animation-delay:.4s}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{animation-delay:.6s}.hero-section[_ngcontent-%COMP%]   .social-icons[_ngcontent-%COMP%]{animation-delay:.8s}@keyframes _ngcontent-%COMP%_fadeInUp{to{opacity:1;transform:translateY(0)}}@media (prefers-reduced-motion: reduce){.hero-section[_ngcontent-%COMP%]   *[_ngcontent-%COMP%], .hero-section[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{animation:none;transition:none}.hero-section[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{opacity:1;transform:none}}"]})}return e})();var GI=[{path:"",component:F1},{path:"**",redirectTo:""}],L1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=me({type:e});static \u0275inj=ge({imports:[xo.forRoot(GI,{scrollPositionRestoration:"enabled",anchorScrolling:"enabled"}),xo]})}return e})();var Rl=(()=>{class e{indexSubject=new pe(0);index$=this.indexSubject.asObservable();setIndex(n){this.indexSubject.next(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Fl=(()=>{class e{http;about={name:"",description:"",photo:"",email:"",portfolioUrl:""};constructor(n){this.http=n}ngOnInit(){this.getAboutData()}getAboutData(){this.http.get("https://portflio-backend-uiv7.onrender.com/api/about").subscribe(n=>{this.about=n},n=>{console.error("Error fetching About data:",n)})}static \u0275fac=function(r){return new(r||e)(U(sn))};static \u0275cmp=de({type:e,selectors:[["app-about"]],decls:20,vars:1,consts:[["id","about-me",1,"about-me","transition-colors","duration-300"],[1,"about-me-header"],[1,"about-me-title","theme-text-primary","transition-colors","duration-300"],[1,"about-me-layout"],[1,"spline-left-wrapper"],["src",sm`https://my.spline.design/molang3dcopy-WdvJb5OqkYqVTP1prRjNjqcv/`,"frameborder","0","allowfullscreen",""],[1,"content-right"],[1,"about-me-container"],[1,"about-me-flex-container"],[1,"about-me-content"],[1,"text"],["href","https://flowcv.com/resume/8hm00kwrls","target","_blank"],[1,"cta"],["width","15px","height","10px","viewBox","0 0 13 10"],["d","M1,5 L11,5"],["points","8 1 12 5 8 9"]],template:function(r,i){r&1&&(h(0,"section",0)(1,"div",1)(2,"div",2),O(3," About "),p()(),h(4,"div",3)(5,"div",4),y(6,"iframe",5),p(),h(7,"div",6)(8,"div",7)(9,"div",8)(10,"div",9)(11,"div",10),O(12),p(),h(13,"a",11)(14,"button",12)(15,"span"),O(16,"Resume"),p(),vt(),h(17,"svg",13),y(18,"path",14)(19,"polyline",15),p()()()()()()()()()),r&2&&(H(12),xn(" ",i.about.description||"Loading description..."," "))},styles:['.about-me[_ngcontent-%COMP%]{position:relative;overflow:hidden;min-height:100vh;background:transparent;transition:background .3s ease;padding-top:0}.dark[_nghost-%COMP%]   .about-me[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .about-me[_ngcontent-%COMP%]{background:transparent}.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]{position:relative;z-index:2}@media (max-width: 960px){.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]{padding-bottom:60px}}.about-me-header[_ngcontent-%COMP%]{position:relative;z-index:10;padding:40px 0 10px;text-align:center}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:4rem;font-weight:800;background:linear-gradient(135deg,var(--primary-pink),var(--accent-pink));-webkit-background-clip:text;background-clip:text;-webkit-text-fill-color:transparent;line-height:1.1;margin:30px 0 0;letter-spacing:-.02em;position:relative;transition:all .3s ease}.dark[_nghost-%COMP%]   .about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-pink),var(--accent-pink));-webkit-background-clip:text;background-clip:text;-webkit-text-fill-color:transparent}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-10px;left:50%;transform:translate(-50%);width:80px;height:4px;background:linear-gradient(90deg,var(--primary-pink),var(--accent-pink));border-radius:2px}@media (max-width: 768px){.about-me-header[_ngcontent-%COMP%]{padding:30px 0}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:3rem}}@media (max-width: 500px){.about-me-header[_ngcontent-%COMP%]{padding:25px 0 30px}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:2.5rem;padding-top:20px}}.about-me-flex-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;gap:40px;text-align:left;padding:20px}@media (max-width: 768px){.about-me-flex-container[_ngcontent-%COMP%]{gap:30px;padding:15px}}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]{gap:25px;padding:10px}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start;gap:40px;width:100%;max-width:900px;background:#ffffffb3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:20px;padding:40px;box-shadow:0 8px 32px #db27771a,0 0 0 1px #fff3;border:1px solid var(--border-pink);transition:all .3s ease}.dark[_nghost-%COMP%]   .about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{background:#1e1e1ee6;border:1px solid var(--border-default);box-shadow:0 8px 32px #00000080,0 0 0 1px #f472b61a}@media (max-width: 768px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{gap:30px;max-width:100%;padding:30px;border-radius:15px}}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{padding:20px;gap:25px}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:var(--text-secondary);font-weight:500;font-size:1.25rem;line-height:1.8;text-align:left;max-width:100%;margin:0;letter-spacing:.01em;position:relative;transition:color .3s ease}.dark[_nghost-%COMP%]   .about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:var(--text-on-bg);opacity:.9}@media (max-width: 768px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-size:1.125rem;line-height:1.7}}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-size:1rem;line-height:1.6}}.cta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:relative;font-size:1.125rem;font-weight:600;letter-spacing:.02em;color:#fff;text-shadow:0 1px 2px rgba(0,0,0,.1);z-index:2}.cta[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{position:relative;margin-left:12px;fill:none;stroke-linecap:round;stroke-linejoin:round;stroke:#fff;stroke-width:2.5;transform:translate(-3px);transition:all .4s cubic-bezier(.4,0,.2,1);z-index:2}.cta[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%]{transform:translate(3px);stroke-width:3}@media (max-width: 768px){.cta[_ngcontent-%COMP%]{padding:16px 35px}.cta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 500px){.cta[_ngcontent-%COMP%]{padding:14px 30px}.cta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:.9rem}}.cta[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center;padding:18px 40px;background:linear-gradient(135deg,var(--primary-pink),var(--accent-pink));border:none;border-radius:50px;cursor:pointer;transition:all .4s cubic-bezier(.4,0,.2,1);text-decoration:none;box-shadow:0 8px 25px #db27774d,0 0 0 1px #ffffff1a;overflow:hidden}.dark[_nghost-%COMP%]   .cta[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .cta[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-pink),var(--accent-pink));box-shadow:0 8px 25px #f472b666,0 0 0 1px #f472b633}.dark[_nghost-%COMP%]   .cta[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .cta[_ngcontent-%COMP%]:hover{box-shadow:0 12px 35px #f472b680,0 0 0 1px #f472b64d}.cta[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .6s ease}.cta[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,var(--accent-pink),var(--primary-pink));transform:translateY(-3px) scale(1.02);box-shadow:0 12px 35px #db277766,0 0 0 1px #fff3}.cta[_ngcontent-%COMP%]:hover:before{left:100%}.cta[_ngcontent-%COMP%]:active{transform:translateY(-1px) scale(.98)}.about-me-layout[_ngcontent-%COMP%]{display:flex;min-height:100vh;align-items:center;position:relative}.content-right[_ngcontent-%COMP%]{flex:1;padding:40px;display:flex;align-items:center;justify-content:flex-start;min-height:100vh;order:1;z-index:2}.spline-left-wrapper[_ngcontent-%COMP%]{flex:0 0 50%;height:100vh;position:relative;order:2;z-index:1}.spline-left-wrapper[_ngcontent-%COMP%]   iframe[_ngcontent-%COMP%]{width:100%;height:100%;border:none;display:block;border-radius:0 20px 20px 0}@media (max-width: 1024px){.about-me-layout[_ngcontent-%COMP%]{flex-direction:column;min-height:100vh}.spline-left-wrapper[_ngcontent-%COMP%]{flex:none;width:100%;height:400px;order:1}.spline-left-wrapper[_ngcontent-%COMP%]   iframe[_ngcontent-%COMP%]{border-radius:0}.content-right[_ngcontent-%COMP%]{padding:40px 30px;min-height:60vh;order:2}}@media (max-width: 768px){.spline-left-wrapper[_ngcontent-%COMP%]{height:350px}.content-right[_ngcontent-%COMP%]{padding:30px 20px;min-height:50vh}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{padding:25px}}@media (max-width: 480px){.spline-left-wrapper[_ngcontent-%COMP%]{height:300px}.content-right[_ngcontent-%COMP%]{padding:20px 15px;min-height:40vh}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{padding:20px;border-radius:12px}.about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:2rem}}@media (prefers-reduced-motion: reduce){.cta[_ngcontent-%COMP%], .cta[_ngcontent-%COMP%]:before, .cta[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .about-me-header[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{transition:none}}.about-me[_ngcontent-%COMP%]{scroll-behavior:smooth}']})}return e})();function WI(e,t){e&1&&(vt(),h(0,"svg",4),y(1,"path",5),p())}function ZI(e,t){e&1&&(vt(),h(0,"svg",6),y(1,"path",7),p())}var j1=(()=>{class e{darkModeService;isDarkMode=!1;subscription=new ue;constructor(n){this.darkModeService=n}ngOnInit(){this.subscription.add(this.darkModeService.isDarkMode$.subscribe(n=>{this.isDarkMode=n}))}ngOnDestroy(){this.subscription.unsubscribe()}onToggle(){this.darkModeService.toggleDarkMode()}static \u0275fac=function(r){return new(r||e)(U(kl))};static \u0275cmp=de({type:e,selectors:[["app-dark-mode-toggle"]],decls:4,vars:12,consts:[[1,"relative","inline-flex","items-center","justify-center","w-12","h-6","rounded-full","transition-all","duration-300","ease-in-out","focus:outline-none","focus:ring-2","focus:ring-pink-500","focus:ring-offset-2","dark:focus:ring-offset-white",3,"click"],[1,"absolute","left-1","top-1","w-4","h-4","bg-white","rounded-full","shadow-md","transform","transition-transform","duration-300","ease-in-out","flex","items-center","justify-center"],["class","w-3 h-3 text-yellow-500 transition-opacity duration-200","fill","currentColor","viewBox","0 0 20 20",4,"ngIf"],["class","w-3 h-3 text-gray-700 transition-opacity duration-200","fill","currentColor","viewBox","0 0 20 20",4,"ngIf"],["fill","currentColor","viewBox","0 0 20 20",1,"w-3","h-3","text-yellow-500","transition-opacity","duration-200"],["fill-rule","evenodd","d","M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z","clip-rule","evenodd"],["fill","currentColor","viewBox","0 0 20 20",1,"w-3","h-3","text-gray-700","transition-opacity","duration-200"],["d","M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"]],template:function(r,i){r&1&&(h(0,"button",0),ve("click",function(){return i.onToggle()}),h(1,"span",1),Ee(2,WI,2,0,"svg",2)(3,ZI,2,0,"svg",3),p()()),r&2&&(Dt("bg-pink-600",i.isDarkMode)("bg-pink-100",!i.isDarkMode),Ri("aria-label",i.isDarkMode?"Switch to light mode":"Switch to dark mode")("aria-pressed",i.isDarkMode),H(),Dt("translate-x-6",i.isDarkMode)("translate-x-0",!i.isDarkMode),H(),X("ngIf",!i.isDarkMode),H(),X("ngIf",i.isDarkMode))},dependencies:[Ft],styles:[".dark-mode-toggle[_ngcontent-%COMP%]{position:relative;display:inline-flex;align-items:center;justify-content:center}button[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}button[_ngcontent-%COMP%]:hover{transform:scale(1.05)}button[_ngcontent-%COMP%]:active{transform:scale(.95)}button[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 0 0 3px #bb02624d}svg[_ngcontent-%COMP%]{transition:all .2s ease-in-out}span[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%]{transform:scale(1.1)}@keyframes _ngcontent-%COMP%_slide-right{0%{transform:translate(0)}to{transform:translate(1.5rem)}}@keyframes _ngcontent-%COMP%_slide-left{0%{transform:translate(1.5rem)}to{transform:translate(0)}}button[_ngcontent-%COMP%]{z-index:10}"]})}return e})();var YI=["mobileMenuCheckbox"],B1=(()=>{class e{sliderService;mobileMenuCheckbox;isMobileMenuOpen=!1;constructor(n){this.sliderService=n}closeMobileMenu(){this.isMobileMenuOpen=!1,this.mobileMenuCheckbox&&(this.mobileMenuCheckbox.nativeElement.checked=!1)}toggleMobileMenu(){this.isMobileMenuOpen=!this.isMobileMenuOpen}goTo(n){this.sliderService.setIndex(n)}static \u0275fac=function(r){return new(r||e)(U(Rl))};static \u0275cmp=de({type:e,selectors:[["app-header"]],viewQuery:function(r,i){if(r&1&&Sn(YI,5),r&2){let o;Kt(o=Xt())&&(i.mobileMenuCheckbox=o.first)}},decls:30,vars:0,consts:[[1,"h-[70px]","sticky","top-0","z-50","border-b-1","border-pink-300","dark:border-dark-pink-border","lg:bg-transparent","bg-gradient-to-r","from-primary-pink-lightest","via-[rgba(218,149,234,0.95)]","to-rose-100","dark:bg-gradient-to-r","dark:from-dark-bg-primary","dark:via-dark-bg-secondary","dark:to-dark-bg-tertiary","px-8","flex","items-center","justify-between","transition-all","duration-300"],["type","checkbox","id","check",1,"hidden","peer"],["for","check",1,"menu","block","lg:hidden","sticky","cursor-pointer","z-50","text-gray-700","dark:text-gray-200"],["xmlns","http://www.w3.org/2000/svg","width","30","height","30","fill","currentColor",1,"bi","bi-list","transition-colors","duration-300"],["fill-rule","evenodd","d","M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"],[1,"logo"],[1,"theme-text-primary","font-bold","cursor-pointer","text-xl","transition-colors","duration-300",3,"click"],[1,"nav-items","peer-checked:right-0","fixed","lg:static","top-0","right-[-250px]","h-screen","lg:h-auto","w-[250px]","lg:w-auto","flex","flex-col","lg:flex-row","justify-evenly","lg:justify-end","items-start","lg:items-center","bg-red-400","dark:bg-dark-bg-secondary","lg:bg-transparent","dark:lg:bg-transparent","transition-all","duration-500","p-8","lg:p-0","gap-y-6","lg:gap-x-6"],[1,"flex","flex-col","lg:flex-row","gap-y-4","lg:gap-x-4","theme-text-secondary","dark:text-gray-200","text-[18px]","font-medium"],["href","# ",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300",3,"click"],["href","#about",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300",3,"click"],["href","#projects",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300",3,"click"],["href","#skills",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300",3,"click"],["href","#experience",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300",3,"click"],["href","#contact",1,"hover:text-pink-700","dark:hover:text-pink-400","relative","after:block","after:h-[3px]","after:bg-pink-600","dark:after:bg-pink-400","after:w-0","hover:after:w-full","after:transition-all","after:duration-300","transition-colors","duration-300",3,"click"],[1,"mt-4","lg:mt-0","lg:ml-4"]],template:function(r,i){r&1&&(h(0,"nav",0),y(1,"input",1),h(2,"label",2),vt(),h(3,"svg",3),y(4,"path",4),p()(),ra(),h(5,"div",5)(6,"h2",6),ve("click",function(){return i.goTo(0),i.closeMobileMenu()}),O(7,"RA"),p()(),h(8,"div",7)(9,"ul",8)(10,"li")(11,"a",9),ve("click",function(){return i.goTo(0),i.closeMobileMenu()}),O(12,"Home"),p()(),h(13,"li")(14,"a",10),ve("click",function(){return i.goTo(1),i.closeMobileMenu()}),O(15,"About"),p()(),h(16,"li")(17,"a",11),ve("click",function(){return i.goTo(2),i.closeMobileMenu()}),O(18,"Projects"),p()(),h(19,"li")(20,"a",12),ve("click",function(){return i.goTo(3),i.closeMobileMenu()}),O(21,"Skills"),p()(),h(22,"li")(23,"a",13),ve("click",function(){return i.goTo(4),i.closeMobileMenu()}),O(24,"Experience"),p()(),h(25,"li")(26,"a",14),ve("click",function(){return i.goTo(5),i.closeMobileMenu()}),O(27,"Contact"),p()()(),h(28,"div",15),y(29,"app-dark-mode-toggle"),p()()())},dependencies:[j1],styles:['*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box}nav[_ngcontent-%COMP%]{height:70px;background:transparent;padding:0 2rem;display:flex;justify-content:space-between;align-items:center;position:fixed;top:0;z-index:1000;width:100%;border-bottom:1px solid var(--border-pink-dark)}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:none}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-weight:700;font-size:2rem;color:var(--text-primary);cursor:pointer;margin:0 .5rem;text-shadow:2px 2px 4px rgba(0,0,0,.3);transition:all .3s ease;position:relative;z-index:1}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:before{content:"";position:absolute;width:100%;height:2px;bottom:-5px;left:0;background:linear-gradient(to right,var(--primary-pink),var(--accent-pink));transform:scaleX(0);transform-origin:left;transition:transform .3s ease}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:hover:before{transform:scaleX(1)}.nav-items[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{display:flex}.overview[_ngcontent-%COMP%]{margin-right:4rem}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:none}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style:none;margin:0 .5rem}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:var(--text-secondary);font-size:18px}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:var(--primary-pink-dark)}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after{content:"";display:block;height:3px;background:var(--primary-pink);width:0%;transition:all ease-in-out .3s}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after{width:100%}#check[_ngcontent-%COMP%], .menu[_ngcontent-%COMP%]{display:none}@media (max-width: 750px){.nav-items[_ngcontent-%COMP%]{z-index:1000;position:fixed;top:0;height:100vh;width:250px;flex-direction:column;justify-content:space-evenly;background:var(--border-pink);padding:2rem;right:-250px;transition:all ease-in-out .5s}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{flex-direction:column;width:auto}.overview[_ngcontent-%COMP%]{margin:0}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:inline-block;font-weight:400;text-transform:uppercase;font-size:13px;margin-bottom:1rem}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:inline-block;cursor:pointer;vertical-align:top}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:1rem 0}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:inline-block}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{margin-left:2px;transition:all ease-in-out .3s}.menu[_ngcontent-%COMP%]{display:inline-block;position:fixed;right:2.5rem;z-index:1001}#check[_ngcontent-%COMP%]:checked ~ .nav-items[_ngcontent-%COMP%]{right:0}}']})}return e})();var U1=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/projects";constructor(n){this.http=n}getProjects(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(M(sn))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var JI=["cardRef"],e4=e=>({"fade-in-up":e});function t4(e,t){if(e&1&&(h(0,"span",19),O(1),p()),e&2){let n=t.$implicit;H(),xn(" ",n," ")}}function n4(e,t){if(e&1&&(h(0,"a",22),O(1," GitHub "),p()),e&2){let n=Ge(2).$implicit;X("href",n.githubLink,Hu)}}function r4(e,t){if(e&1&&(h(0,"a",22),O(1," Live Demo "),p()),e&2){let n=Ge(2).$implicit;X("href",n.link,Hu)}}function i4(e,t){if(e&1&&(h(0,"div",20),Ee(1,n4,2,1,"a",21)(2,r4,2,1,"a",21),p()),e&2){let n=Ge().$implicit;H(),X("ngIf",n.githubLink),H(),X("ngIf",n.link)}}function o4(e,t){e&1&&(h(0,"p",23),O(1," This project is private due to company confidentiality. "),p())}function s4(e,t){if(e&1&&(h(0,"div",6,0)(2,"div",7),y(3,"img",8),p(),h(4,"div",9)(5,"h3",10),O(6),p(),h(7,"p",11),O(8),p(),h(9,"p",12),O(10),p(),h(11,"div",13)(12,"strong",14),O(13,"Skills:"),p(),h(14,"div",15),Ee(15,t4,2,1,"span",16),p()()(),Ee(16,i4,3,2,"div",17)(17,o4,2,0,"p",18),p()),e&2){let n=t.$implicit,r=t.index,i=Ge();X("ngClass",t0(8,e4,i.isVisible[r])),H(3),X("ngSrc",n.photo),H(3),ct(n.name),H(2),ct(n.title),H(2),ct(n.description),H(5),X("ngForOf",n.skills),H(),X("ngIf",n.githubLink||n.link),H(),X("ngIf",!n.githubLink&&!n.link)}}var $1=(()=>{class e{apiService;projects=[];isVisible=[];cardElements;constructor(n){this.apiService=n}ngOnInit(){this.apiService.getProjects().subscribe(n=>{this.projects=n.map(r=>({photo:r.photo,name:r.name,title:r.title,description:r.description,link:r.link,githubLink:r.githubLink,skills:r.skills})),this.isVisible=new Array(this.projects.length).fill(!1)},n=>{console.error("Error fetching projects:",n)})}ngAfterViewInit(){let n=new IntersectionObserver(r=>{r.forEach(i=>{if(i.isIntersecting){let o=this.cardElements.toArray().findIndex(s=>s.nativeElement===i.target);o!==-1&&(this.isVisible[o]=!0)}})},{threshold:.2});setTimeout(()=>{this.cardElements.forEach(r=>n.observe(r.nativeElement))},500)}static \u0275fac=function(r){return new(r||e)(U(U1))};static \u0275cmp=de({type:e,selectors:[["app-projects"]],viewQuery:function(r,i){if(r&1&&Sn(JI,5),r&2){let o;Kt(o=Xt())&&(i.cardElements=o)}},decls:6,vars:1,consts:[["cardRef",""],[1,"text-center","pb-8","pt-20"],[1,"text-5xl","font-righteous","text-pink-600","dark:text-pink-400","mb-10","relative","transition-colors","duration-300"],[1,"block","w-16","h-1","bg-pink-400","dark:bg-pink-500","mx-auto","mt-2","rounded-full","transition-colors","duration-300"],[1,"grid","grid-cols-1","sm:grid-cols-2","lg:grid-cols-3","gap-10","px-6"],["class","project-card bg-white dark:bg-dark-bg-card border border-gray-200 dark:border-dark-border rounded-2xl p-6 shadow-lg dark:shadow-dark-card flex flex-col justify-between transition-all duration-300",3,"ngClass",4,"ngFor","ngForOf"],[1,"project-card","bg-white","dark:bg-dark-bg-card","border","border-gray-200","dark:border-dark-border","rounded-2xl","p-6","shadow-lg","dark:shadow-dark-card","flex","flex-col","justify-between","transition-all","duration-300",3,"ngClass"],[1,"overflow-hidden","rounded-xl","mb-4","h-52","relative"],["width","400","height","210","alt","Project Image",1,"project-image","w-full","h-full","object-cover",3,"ngSrc"],[1,"text-gray-800","dark:text-gray-200","transition-colors","duration-300"],[1,"title-underline","text-2xl","font-bold","font-righteous","uppercase","text-gray-900","dark:text-gray-100","transition-colors","duration-300"],[1,"text-sm","font-lato","tracking-wide","text-pink-600","dark:text-pink-400","mt-1","transition-colors","duration-300"],[1,"text-sm","mt-2","text-gray-700","dark:text-gray-300","transition-colors","duration-300","normal-case"],[1,"mt-4"],[1,"text-pink-600","dark:text-pink-400","transition-colors","duration-300"],[1,"flex","flex-wrap","gap-2","mt-1"],["class","skill-tag bg-pink-100 dark:bg-dark-bg-hover text-pink-700 dark:text-pink-300 text-xs font-semibold px-2 py-1 rounded-full cursor-default transition-all duration-300",4,"ngFor","ngForOf"],["class","mt-6 flex justify-between gap-3",4,"ngIf"],["class","text-sm italic text-gray-500 dark:text-gray-400 mt-4 text-center",4,"ngIf"],[1,"skill-tag","bg-pink-100","dark:bg-dark-bg-hover","text-pink-700","dark:text-pink-300","text-xs","font-semibold","px-2","py-1","rounded-full","cursor-default","transition-all","duration-300"],[1,"mt-6","flex","justify-between","gap-3"],["target","_blank","class","project-button text-xs px-4 py-2 border border-pink-500 dark:border-pink-400 text-pink-500 dark:text-pink-400 rounded-full hover:bg-pink-500 dark:hover:bg-pink-400 hover:text-white dark:hover:text-gray-900 flex-1 text-center transition-all duration-300",3,"href",4,"ngIf"],["target","_blank",1,"project-button","text-xs","px-4","py-2","border","border-pink-500","dark:border-pink-400","text-pink-500","dark:text-pink-400","rounded-full","hover:bg-pink-500","dark:hover:bg-pink-400","hover:text-white","dark:hover:text-gray-900","flex-1","text-center","transition-all","duration-300",3,"href"],[1,"text-sm","italic","text-gray-500","dark:text-gray-400","mt-4","text-center"]],template:function(r,i){r&1&&(h(0,"div",1)(1,"h2",2),O(2," Projects "),y(3,"span",3),p()(),h(4,"div",4),Ee(5,s4,18,10,"div",5),p()),r&2&&(H(5),X("ngForOf",i.projects))},dependencies:[Jr,Oa,Xr,Ft],styles:['.project-card[_ngcontent-%COMP%]{transition:all .5s cubic-bezier(.4,0,.2,1);will-change:transform,box-shadow}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px) scale(1.02);box-shadow:0 25px 50px -12px #00000040,0 0 0 1px #ec48991a}.project-image[_ngcontent-%COMP%]{transition:transform .7s cubic-bezier(.4,0,.2,1);will-change:transform}.project-card[_ngcontent-%COMP%]:hover   .project-image[_ngcontent-%COMP%]{transform:scale(1.1)}.skill-tag[_ngcontent-%COMP%]{transition:all .2s cubic-bezier(.4,0,.2,1);will-change:transform}.skill-tag[_ngcontent-%COMP%]:hover{transform:scale(1.05) translateY(-1px);box-shadow:0 4px 8px #ec489933}.project-button[_ngcontent-%COMP%]{position:relative;overflow:hidden;transition:all .3s cubic-bezier(.4,0,.2,1);will-change:transform,box-shadow}.project-button[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.project-button[_ngcontent-%COMP%]:hover:before{left:100%}.project-button[_ngcontent-%COMP%]:hover{transform:scale(1.05) translateY(-2px);box-shadow:0 8px 16px #ec48994d}.title-underline[_ngcontent-%COMP%]{position:relative;overflow:hidden}.title-underline[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:-2px;left:0;width:0;height:2px;background:linear-gradient(90deg,#ec4899,#f472b6);transition:width .3s ease-out}.project-card[_ngcontent-%COMP%]:hover   .title-underline[_ngcontent-%COMP%]:after{width:100%}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out forwards}.project-card[_ngcontent-%COMP%]:nth-child(1){animation-delay:.1s}.project-card[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.project-card[_ngcontent-%COMP%]:nth-child(3){animation-delay:.3s}.project-card[_ngcontent-%COMP%]:nth-child(4){animation-delay:.4s}.project-card[_ngcontent-%COMP%]:nth-child(5){animation-delay:.5s}.project-card[_ngcontent-%COMP%]:nth-child(6){animation-delay:.6s}']})}return e})();var l4=(e,t)=>({"submission-message success-message":e,"submission-message error-message":t});function c4(e,t){e&1&&(vt(),h(0,"svg",221),y(1,"path",222),p())}function u4(e,t){e&1&&(vt(),h(0,"svg",221),y(1,"path",223),p())}function d4(e,t){if(e&1&&(h(0,"div",219),Ee(1,c4,2,0,"svg",220)(2,u4,2,0,"svg",220),h(3,"span"),O(4),p()()),e&2){let n=Ge();X("ngClass",n0(4,l4,n.submissionStatus==="success",n.submissionStatus==="error")),H(),X("ngIf",n.submissionStatus==="success"),H(),X("ngIf",n.submissionStatus==="error"),H(2),ct(n.submissionMessage)}}var H1=(()=>{class e{submissionStatus=null;submissionMessage="";onSubmit(n){n.preventDefault();let r=n.target;fetch(r.action,{method:r.method,body:new FormData(r),headers:{Accept:"application/json"}}).then(i=>{i.ok?(this.submissionStatus="success",this.submissionMessage="Message sent successfully!",r.reset()):(this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later.")}).catch(()=>{this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later."})}onInputChange(){this.submissionStatus=null,this.submissionMessage=""}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=de({type:e,selectors:[["app-contact"]],decls:223,vars:1,consts:[[1,"contact-header","transition-colors","duration-300"],[1,"text-5xl","font-righteous","text-pink-600","dark:text-pink-400","mt-20","relative","transition-colors","duration-300"],[1,"container","d-flex","justify-content-center","align-items-center","transition-colors","duration-300"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 790 563","fill","none"],["id","Image"],["id","g14"],["id","g16"],["id","g22"],["id","path24","d","M578.06 12.9772C592.384 8.33142 607.668 7.43103 622.682 8.31278C644.252 9.57946 666.668 15.0178 682.527 29.8837C692.521 39.2526 699.149 51.6277 707.182 62.7655C730.486 95.0785 766.513 118.198 782.236 154.912C795.674 186.289 790.623 225.749 767.498 250.666C744.37 275.583 703.649 282.658 675.018 264.535C647.531 247.136 635.383 212.503 610.273 191.742C592.326 176.901 569.144 170.28 549.646 157.607C529.69 144.636 513.457 124.248 509.79 100.515C506.745 80.8173 513.744 59.4156 528.903 46.4558C543.731 33.7796 559.331 19.0536 578.06 12.9772Z","fill","#D0F6FF"],["id","g26"],["id","path28","d","M702.629 254.14C677.841 258.169 653.602 251.674 628.841 247.05C605.059 242.608 581.372 234.267 562.49 218.522C553.842 211.31 546.177 202.344 542.784 191.529C537.944 176.097 542.362 159.436 542.319 143.243C542.267 124.241 537.593 105.929 524.57 91.9138C516.642 83.3826 507.429 75.9038 501.21 66.026C488.249 45.4368 498.285 17.8695 518.578 6.24557C537.067 -4.34208 560.588 -0.151769 579.793 9.03335C598.996 18.2198 615.855 31.9082 635.139 40.9228C656.28 50.8045 679.407 54.6779 702.724 56.9022C720.556 58.6044 738.716 56.5333 753.266 67.1156C763.675 74.6877 771.032 86.0519 775.307 98.2911C783.396 121.448 781.768 148.673 778.037 172.583C775.54 188.601 770.517 204.461 761.348 217.755C750.094 234.074 732.89 245.819 714.058 251.504C710.234 252.66 706.426 253.523 702.629 254.14Z","fill","#ADE0EC"],["id","g30"],["id","path32","d","M663.601 562.578H87.0689C43.5385 528.913 13.2922 480.886 5.1219 426.023C1.72497 403.207 3.65744 376.191 22.008 362.528C50.2285 341.516 92.5784 368.009 124.46 353.325C144.998 343.869 155.119 319.297 155.332 296.439C155.544 273.583 147.922 251.523 142.217 229.409C136.51 207.295 132.749 183.417 140.459 161.935C148.169 140.454 170.87 123.025 192.716 128.727C211.437 133.614 223.318 152.833 241.257 160.133C259.931 167.732 281.608 160.819 298.184 149.256C314.758 137.694 327.949 121.87 343.441 108.858C370.638 86.0156 406.562 72.0169 441.495 77.35C476.426 82.6831 508.747 110.108 514.202 145.471C518.662 174.4 506.652 207.826 524.152 231.129C543.883 257.401 585.152 250.025 613.676 265.983C636.899 278.972 649.286 309.077 642.052 334.934C634.666 361.336 609.565 383.494 613.653 410.622C616.583 430.071 633.6 443.505 645.587 458.982C668.627 488.727 679.049 528.158 663.601 562.578Z","fill","#D0F6FF"],["id","g34"],["id","path36","d","M636.536 562.578H142.588C127.567 548.706 110.711 535.931 102.179 517.242C93.6475 498.553 93.6698 474.269 107.702 459.372C124.638 441.394 152.947 443.847 176.763 437.899C204.228 431.038 229.205 408.689 232.723 380.251C237.265 343.537 206.911 309.992 208.804 273.041C210.296 243.911 234.698 217.737 263.314 214.567C282.66 212.424 302.727 219.607 321.415 214.109C338.741 209.012 351.237 194.119 366.296 184.052C383.968 172.235 406.528 167.099 426.891 172.974C447.257 178.85 464.492 196.695 467.235 217.968C470.152 240.588 458.004 266.283 470.888 284.991C480.485 298.927 499.63 301.618 516.392 301.075C533.155 300.531 551.03 298.252 565.763 306.372C579.463 313.921 587.611 329.548 589.138 345.273C590.664 360.996 586.334 376.788 579.943 391.199C574.357 403.794 567.162 415.706 562.961 428.843C558.759 441.979 557.893 457.066 564.737 469.006C571.941 481.577 585.915 488.105 597.307 496.94C617.442 512.552 635.027 536.936 636.536 562.578Z","fill","#ADE0EC"],["id","g38"],["id","path40","d","M595.195 76.2172L623.725 149.709L684.511 114.948L595.195 76.2172Z","fill","#FAFAFA"],["id","g42"],["id","path44","d","M595.195 76.2172L651.26 133.962L666.528 125.232L595.195 76.2172Z","fill","#DADADA"],["id","g46"],["id","path48","d","M666.528 125.231L655.896 151.885L651.262 133.962L666.528 125.231Z","fill","#DADADA"],["id","g50"],["id","path52","d","M655.896 151.885L642.776 138.814L651.262 133.962L655.896 151.885Z","fill","#B2B2B2"],["id","g54"],["id","path56","d","M222.015 539.778C157.683 522.604 101.579 476.087 72.2367 415.592C60.1279 390.628 52.3612 362.908 54.182 335.155C56.0014 307.4 68.2732 279.663 90.2639 263.011C112.253 246.359 144.303 242.756 167.56 257.538C190.03 271.821 200.733 299.209 220.204 317.461C243.475 339.274 280.404 345.641 308.459 330.683C336.514 315.723 352.288 279.369 342.05 248.968C332.575 220.834 305.793 203.339 282.527 185.228C259.261 167.115 236.126 141.651 239.454 112.116C242.315 86.7319 264.382 67.653 287.628 57.7513C332.132 38.7951 389.516 47.2223 419.844 85.2787C452.476 126.224 446.202 185.954 431.486 236.425C416.769 286.896 395.069 337.985 402.391 390.086C408.475 433.375 434.97 472.304 470.109 497.688C505.247 523.075 548.365 535.649 591.441 538.326C634.426 540.999 680.569 532.908 712.364 503.476C744.158 474.044 754.899 419.157 726.78 386.108C712.226 369.003 690.497 360.328 669.604 352.466C648.708 344.604 626.907 336.377 611.765 319.807C596.621 303.236 590.753 275.553 604.995 258.181C621.492 238.058 665.44 235.858 680.982 214.969C692.069 200.069 679.116 171.364 666.529 157.269","stroke","#00C0E0","stroke-width","2.541","stroke-miterlimit","10","stroke-dasharray","7.62 7.62"],["id","g58"],["id","path60","d","M186.221 462.671C158.423 444.172 133.639 421.035 113.173 394.475C104.595 383.341 96.7115 371.5 91.5083 358.398C86.3038 345.294 83.8862 330.794 86.4431 316.909C88.2757 306.953 93.6209 296.589 103.112 293.404C110.525 290.917 118.902 293.505 125.077 298.35C131.253 303.195 135.584 310.023 139.418 316.916C154.207 343.52 163.287 372.9 174.224 401.352C179.474 415.006 185.205 428.511 192.17 441.366C195.631 447.754 199.387 453.984 203.532 459.939C207.289 465.334 214.117 471.144 216.477 476.969C211.073 481.321 191.263 466.026 186.221 462.671Z","fill","#009D9C"],["id","g62"],["id","path64","d","M107.952 308.508C121.544 366.877 153.477 420.713 197.968 460.267","stroke","#00BBBF","stroke-width","2.02","stroke-miterlimit","10"],["id","g66"],["id","path68","d","M556.282 462.962C580.155 451.221 602.114 435.493 621.004 416.609C628.922 408.693 636.362 400.145 641.81 390.319C647.257 380.493 650.64 369.27 650.028 358.018C649.587 349.946 646.41 341.19 639.223 337.682C633.608 334.942 626.717 336.117 621.339 339.307C615.961 342.497 611.841 347.447 608.109 352.504C593.705 372.014 583.539 394.316 571.997 415.691C566.459 425.947 560.553 436.037 553.736 445.484C550.349 450.177 546.746 454.716 542.861 458.995C539.341 462.875 533.349 466.761 530.891 471.124C534.727 475.129 551.952 465.092 556.282 462.962Z","fill","#009D9C"],["id","g70"],["id","path72","d","M633.861 349.129C617.182 393.899 586.452 433.173 547.233 459.836","stroke","#00BBBF","stroke-width","1.612","stroke-miterlimit","10"],["id","g74"],["id","path76","d","M198.233 424.458C213.177 349.774 197.247 269.251 155.048 206.17","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g78"],["id","path80","d","M159.471 213.554C147.424 209.56 136.887 201.07 130.331 190.079C123.775 179.087 121.256 165.687 123.366 153.024C136.148 156.495 148.154 164.541 154.962 176.037C161.771 187.536 162.465 200.493 159.471 213.554Z","fill","#11ABBA"],["id","g82"],["id","path84","d","M172.923 237.731C170.163 228.217 170.886 217.71 174.922 208.676C178.958 199.643 186.273 192.157 195.149 187.981C198.557 197.74 198.756 208.999 194.512 218.417C190.269 227.834 182.434 233.949 172.923 237.731Z","fill","#11ABBA"],["id","g86"],["id","path88","d","M173.775 236.831C166.404 230.308 156.684 226.574 146.897 226.504C137.11 226.434 127.338 230.03 119.876 236.447C127.196 243.672 137.206 248.568 147.423 248.608C157.641 248.647 166.403 243.999 173.775 236.831Z","fill","#11ABBA"],["id","g90"],["id","path92","d","M188.104 276.094C187.024 266.239 189.542 256.02 195.07 247.837C200.597 239.655 209.088 233.576 218.546 231.029C220.225 241.241 218.483 252.363 212.686 260.887C206.887 269.41 198.122 274.049 188.104 276.094Z","fill","#11ABBA"],["id","g94"],["id","path96","d","M189.099 275.358C182.962 267.634 174.033 262.24 164.408 260.443C154.782 258.647 144.542 260.463 136.091 265.464C142.057 273.87 151.07 280.459 161.124 282.301C171.179 284.145 180.606 281.115 189.099 275.358Z","fill","#11ABBA"],["id","g98"],["id","path100","d","M198.154 314.469C197.924 304.556 201.31 294.598 207.521 286.933C213.729 279.267 222.71 273.961 232.351 272.257C233.146 282.578 230.456 293.504 223.948 301.485C217.439 309.467 208.308 313.315 198.154 314.469Z","fill","#11ABBA"],["id","g102"],["id","path104","d","M199.208 313.823C193.758 305.586 185.324 299.426 175.891 296.789C166.457 294.15 156.099 295.057 147.252 299.294C152.471 308.194 160.885 315.553 170.744 318.274C180.602 320.997 190.253 318.808 199.208 313.823Z","fill","#11ABBA"],["id","g106"],["id","path108","d","M203.971 356.696C205.264 346.866 210.136 337.563 217.445 330.968C224.754 324.372 234.439 320.543 244.225 320.378C243.428 330.699 239.095 341.071 231.443 347.929C223.789 354.789 214.179 357.154 203.971 356.696Z","fill","#11ABBA"],["id","g110"],["id","path112","d","M205.112 356.224C200.99 347.23 193.605 339.817 184.689 335.725C175.775 331.635 165.404 330.9 156.012 333.694C159.806 343.307 166.988 351.901 176.31 356.142C185.632 360.381 195.5 359.74 205.112 356.224Z","fill","#11ABBA"],["id","g114"],["id","path116","d","M546.285 450.207C530.11 375.786 544.71 295.004 585.861 231.219","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g118"],["id","path120","d","M581.562 238.676C593.54 234.478 603.937 225.811 610.312 214.71C616.685 203.608 618.983 190.168 616.663 177.542C603.94 181.23 592.069 189.476 585.452 201.088C578.835 212.7 578.354 225.668 581.562 238.676Z","fill","#11ABBA"],["id","g122"],["id","path124","d","M568.512 263.078C571.114 253.518 570.219 243.024 566.033 234.06C561.85 225.096 554.412 217.737 545.469 213.71C542.22 223.525 542.208 234.787 546.607 244.131C551.006 253.476 558.939 259.457 568.512 263.078Z","fill","#11ABBA"],["id","g126"],["id","path128","d","M567.646 262.192C574.907 255.545 584.566 251.647 594.349 251.411C604.134 251.175 613.963 254.605 621.528 260.895C614.331 268.242 604.403 273.308 594.187 273.52C583.972 273.732 575.135 269.234 567.646 262.192Z","fill","#11ABBA"],["id","g130"],["id","path132","d","M553.965 301.692C554.883 291.82 552.196 281.645 546.534 273.556C540.872 265.469 532.283 259.535 522.783 257.148C521.274 267.388 523.198 278.478 529.136 286.902C535.074 295.328 543.915 299.817 553.965 301.692Z","fill","#11ABBA"],["id","g134"],["id","path136","d","M552.959 300.973C558.968 293.147 567.807 287.6 577.401 285.642C586.995 283.683 597.263 285.324 605.795 290.182C599.97 298.687 591.066 305.428 581.044 307.441C571.021 309.454 561.546 306.585 552.959 300.973Z","fill","#11ABBA"],["id","g138"],["id","path140","d","M544.55 340.232C544.617 330.317 541.066 320.416 534.731 312.857C528.396 305.299 519.329 300.144 509.661 298.606C509.036 308.939 511.905 319.818 518.546 327.687C525.186 335.556 534.379 339.25 544.55 340.232Z","fill","#11ABBA"],["id","g142"],["id","path144","d","M543.486 339.603C548.799 331.276 557.13 324.975 566.519 322.176C575.908 319.378 586.279 320.109 595.196 324.198C590.124 333.185 581.833 340.685 572.021 343.571C562.207 346.46 552.522 344.437 543.486 339.603Z","fill","#11ABBA"],["id","g146"],["id","path148","d","M539.431 382.551C537.978 372.745 532.951 363.525 525.535 357.055C518.117 350.586 508.371 346.92 498.585 346.921C499.551 357.227 504.053 367.523 511.819 374.253C519.583 380.981 529.232 383.182 539.431 382.551Z","fill","#11ABBA"],["id","g150"],["id","path152","d","M538.282 382.098C542.255 373.036 549.518 365.498 558.363 361.259C567.21 357.016 577.568 356.105 587.003 358.74C583.369 368.417 576.328 377.13 567.079 381.527C557.828 385.925 547.95 385.452 538.282 382.098Z","fill","#11ABBA"],["id","g154"],["id","path156","d","M186.615 500.321C190.696 492.791 196.119 485.823 199.682 478.076C190.178 465.849 178.777 454.862 166.819 445.23C159.004 438.931 150.847 433.032 142.419 427.531C134.688 433.762 126.957 439.994 119.225 446.225C120.579 435.351 121.356 425.888 122.482 415.574C105.313 406.143 87.2411 398.331 68.6211 392.377C64.3289 399.386 60.6691 406.825 54.8967 412.829C54.9847 404.798 54.2249 396.412 53.1469 387.893C35.9349 383.405 18.3639 380.482 0.707452 379.308C0.649609 386.531 1.06635 393.746 1.88798 400.912C6.50223 399.507 10.074 395.563 14.9604 394.821C11.7383 402.728 8.82513 411.421 4.99044 419.449C9.19717 438.521 16.3959 456.93 26.2186 473.763C34.3468 468.915 41.9636 462.248 51.7627 458.125C50.0576 473.301 50.0274 489.179 48.7351 504.527C53.8963 510.215 59.4097 515.573 65.2741 520.527C75.5977 529.245 86.9217 536.691 98.9201 542.791C101.353 533.385 103.872 524.016 109.898 516.114C116.996 529.781 124.688 541.96 131.128 555.467C157.986 563.194 186.571 564.779 214.002 559.454C218.542 558.574 222.349 551.211 223.76 546.749C225.172 542.289 224.898 537.468 224.262 532.827C222.26 518.237 216.907 504.646 209.377 492.145C201.36 494.069 193.248 496.332 186.615 500.321Z","fill","#11ABBA"],["id","g158"],["id","path160","d","M194.298 545.299C131.158 507.676 73.43 460.749 23.4922 406.451","stroke","#55CDE2","stroke-width","2.541","stroke-miterlimit","10"],["id","g162"],["id","path164","d","M559.699 515.384C555.868 510.221 551.098 505.622 547.63 500.242C553.415 490.113 560.744 480.704 568.626 472.241C573.781 466.709 579.23 461.436 584.922 456.429C591.334 460.232 597.744 464.032 604.155 467.835C602.002 459.887 600.425 452.929 598.502 445.374C610.285 436.498 622.913 428.73 636.143 422.286C640.078 427.037 643.584 432.176 648.514 436.023C647.601 430.055 647.283 423.73 647.188 417.273C659.526 412.073 672.295 407.997 685.312 405.212C686.116 410.582 686.566 415.998 686.708 421.42C683.127 420.873 680.053 418.324 676.338 418.3C679.571 423.837 682.654 429.991 686.353 435.55C685.232 450.201 681.815 464.681 676.277 478.269C669.715 475.541 663.346 471.403 655.618 469.394C658.486 480.504 660.182 492.319 662.759 503.602C659.518 508.393 655.978 512.977 652.135 517.298C645.372 524.903 637.727 531.67 629.441 537.508C626.639 530.772 623.778 524.07 618.459 518.842C614.616 529.781 610.176 539.676 606.805 550.427C587.63 559.082 566.522 563.353 545.546 562.358C542.075 562.193 538.466 557.123 536.945 553.957C535.425 550.79 535.121 547.171 535.105 543.651C535.058 532.573 537.61 521.88 541.897 511.761C548.065 512.326 554.342 513.133 559.699 515.384Z","fill","#11ABBA"],["id","g166"],["id","path168","d","M558.719 549.691C601.746 514.86 639.767 473.689 671.212 427.878","stroke","#55CDE2","stroke-width","1.91","stroke-miterlimit","10"],["id","g170"],["id","path172","d","M554.113 562.578H187.856C180.008 562.578 173.645 556.132 173.645 548.18V310.114C173.645 302.163 180.008 295.717 187.856 295.717H554.113C561.963 295.717 568.324 302.163 568.324 310.114V548.18C568.324 556.132 561.963 562.578 554.113 562.578Z","fill","#060C37"],["id","g174"],["id","path176","d","M563.719 429.147C563.719 435.866 558.342 441.314 551.71 441.314C545.078 441.314 539.701 435.866 539.701 429.147C539.701 422.427 545.078 416.981 551.71 416.981C558.342 416.981 563.719 422.427 563.719 429.147Z","fill","#111E65"],["id","g178"],["id","path180","d","M182.05 474.266C179.95 474.266 178.247 472.542 178.247 470.413V387.882C178.247 385.753 179.95 384.028 182.05 384.028C184.151 384.028 185.854 385.753 185.854 387.882V470.413C185.854 472.542 184.151 474.266 182.05 474.266Z","fill","#111E65"],["id","path182","d","M535.104 552.722H191.254V305.564H535.104V552.722Z","fill","#D8E9F5"],["id","path184","d","M535.1 322.18H191.256V305.568H535.1V322.18Z","fill","#A4B1BA"],["id","path186","d","M201.252 320.17H196.898V314.56H201.252V320.17Z","fill","#FF6044"],["id","path188","d","M206.906 320.17H202.552V310.653H206.906V320.17Z","fill","#FF6044"],["id","path190","d","M212.886 320.17H208.532V307.952H212.886V320.17Z","fill","#FF6044"],["id","g192"],["id","path194","d","M507.781 308.957V309.767C507.781 310.411 507.264 310.933 506.629 310.933H505.346C504.711 310.933 504.196 311.455 504.196 312.099V315.647C504.196 316.293 504.711 316.814 505.346 316.814H506.629C507.264 316.814 507.781 317.336 507.781 317.979V318.792C507.781 319.435 508.296 319.957 508.931 319.957H526.844C527.479 319.957 527.995 319.435 527.995 318.792V308.957C527.995 308.313 527.479 307.791 526.844 307.791H508.931C508.296 307.791 507.781 308.313 507.781 308.957Z","fill","#D8E9F5"],["id","g196"],["id","path198","d","M526.894 319.341H523.692C523.458 319.341 523.267 319.148 523.267 318.909V308.824C523.267 308.584 523.458 308.391 523.692 308.391H526.894C527.13 308.391 527.32 308.584 527.32 308.824V318.909C527.32 319.148 527.13 319.341 526.894 319.341Z","fill","#92FC28"],["id","g200"],["id","path202","d","M521.94 319.341H518.739C518.505 319.341 518.313 319.148 518.313 318.909V308.824C518.313 308.584 518.505 308.391 518.739 308.391H521.94C522.175 308.391 522.366 308.584 522.366 308.824V318.909C522.366 319.148 522.175 319.341 521.94 319.341Z","fill","#92FC28"],["id","g204"],["id","path206","d","M516.987 319.341H513.785C513.551 319.341 513.36 319.148 513.36 318.909V308.824C513.36 308.584 513.551 308.391 513.785 308.391H516.987C517.223 308.391 517.413 308.584 517.413 308.824V318.909C517.413 319.148 517.223 319.341 516.987 319.341Z","fill","#92FC28"],["id","g208"],["id","path210","d","M498.8 313.874C498.8 316.456 496.733 318.551 494.183 318.551C491.635 318.551 489.569 316.456 489.569 313.874C489.569 311.292 491.635 309.197 494.183 309.197C496.733 309.197 498.8 311.292 498.8 313.874Z","fill","#D8E9F5"],["id","path212","d","M513.36 533.681H212.999V340.836H513.36V533.681Z","fill","#C0CFDA"],["id","path214","d","M513.36 357.464H212.999V340.838H513.36V357.464Z","fill","#A4B3BC"],["id","path216","d","M507.28 373.991H310.642V366.083H507.28V373.991Z","fill","#DCEEFB"],["id","path218","d","M419.169 389.046H310.642V381.138H419.169V389.046Z","fill","#DCEEFB"],["id","path220","d","M369.032 404.104H310.642V396.196H369.032V404.104Z","fill","#DCEEFB"],["id","path222","d","M507.28 430.213H310.642V422.305H507.28V430.213Z","fill","#DCEEFB"],["id","path224","d","M419.169 445.268H310.642V437.36H419.169V445.268Z","fill","#DCEEFB"],["id","path226","d","M369.032 460.325H310.642V452.418H369.032V460.325Z","fill","#DCEEFB"],["id","path228","d","M507.28 485.114H310.642V477.206H507.28V485.114Z","fill","#DCEEFB"],["id","path230","d","M419.169 500.172H310.642V492.264H419.169V500.172Z","fill","#DCEEFB"],["id","path232","d","M369.032 515.228H310.642V507.32H369.032V515.228Z","fill","#DCEEFB"],["id","path234","d","M301.035 409.578H224.781V366.082H301.035V409.578Z","fill","#DCEEFB"],["id","g236"],["id","path238","d","M224.781 409.579L262.908 387.831L301.034 409.579H224.781Z","fill","#CADBE7"],["id","g240"],["id","path242","d","M301.034 366.082L262.908 387.83L224.781 366.082H301.034Z","fill","#CADBE7"],["id","path244","d","M301.035 465.546H224.781V422.05H301.035V465.546Z","fill","#DCEEFB"],["id","g246"],["id","path248","d","M224.781 465.546L262.908 443.798L301.034 465.546H224.781Z","fill","#CADBE7"],["id","g250"],["id","path252","d","M301.034 422.05L262.908 443.798L224.781 422.05H301.034Z","fill","#CADBE7"],["id","path254","d","M301.035 521.515H224.781V478.019H301.035V521.515Z","fill","#DCEEFB"],["id","g256"],["id","path258","d","M224.781 521.514L262.908 499.766L301.034 521.514H224.781Z","fill","#CADBE7"],["id","g260"],["id","path262","d","M301.034 478.018L262.908 499.766L224.781 478.018H301.034Z","fill","#CADBE7"],["id","g264"],["id","g282"],["id","g280","opacity","0.440002"],["id","g274","opacity","0.440002"],["id","path272","opacity","0.440002","d","M314.124 305.565L191.254 430.069V321.271L206.769 305.565H314.124Z","fill","white"],["id","g278","opacity","0.440002"],["id","path276","opacity","0.440002","d","M388.697 305.565L191.254 505.613V449.961L333.77 305.565H388.697Z","fill","white"],["id","g284"],["id","g302"],["id","g300","opacity","0.440002"],["id","g294","opacity","0.440002"],["id","path292","opacity","0.440002","d","M535.104 332.465V441.249L425.071 552.723H317.715L535.104 332.465Z","fill","white"],["id","g298","opacity","0.440002"],["id","path296","opacity","0.440002","d","M535.104 461.142V516.794L499.632 552.723H444.716L535.104 461.142Z","fill","white"],["id","envelope"],["id","g304"],["id","path306","d","M249.266 298.798L351.208 218.764C357.652 213.705 366.657 213.705 373.102 218.764L475.045 298.798V432.924H249.266V298.798Z","fill","#FF9004"],["id","path308","d","M448.926 227.706H275.382V421.076H448.926V227.706Z","fill","#FAFAFA"],["id","path310","d","M438.481 239.346H285.831V245.241H438.481V239.346Z","fill","#DCDCDC"],["id","path312","d","M415.561 251.195H285.831V257.09H415.561V251.195Z","fill","#DCDCDC"],["id","path314","d","M394.51 263.044H285.831V268.939H394.51V263.044Z","fill","#DCDCDC"],["id","path316","d","M394.51 285.792H285.831V291.688H394.51V285.792Z","fill","#DCDCDC"],["id","path318","d","M366.443 297.167H285.831V303.062H366.443V297.167Z","fill","#DCDCDC"],["id","path320","d","M442.769 321H362.156V326.896H442.769V321Z","fill","#DCDCDC"],["id","path322","d","M442.768 332.609H377.201V338.504H442.768V332.609Z","fill","#DCDCDC"],["id","g324"],["id","path326","d","M362.155 365.9L249.265 298.877V432.924L362.155 365.9Z","fill","#FFAE35"],["id","g328"],["id","path330","d","M362.156 365.9L475.045 298.877V432.924L362.156 365.9Z","fill","#FFAE35"],["id","g332"],["id","path334","d","M351.209 352.89L249.267 432.924H475.044L373.102 352.89C366.658 347.831 357.652 347.831 351.209 352.89Z","fill","#FFBF69"],["id","g348"],["id","path350","d","M185.705 159.357C185.994 158.402 185.854 157.315 185.28 156.095C184.719 154.898 183.98 154.112 183.067 153.736C182.152 153.361 181.213 153.405 180.251 153.868C179.287 154.333 178.667 155.04 178.388 155.99C178.109 156.941 178.251 158.015 178.813 159.212C179.375 160.409 180.11 161.203 181.02 161.595C181.927 161.986 182.863 161.951 183.826 161.487C184.789 161.022 185.415 160.312 185.705 159.357ZM184.018 139.899C186.987 140.019 189.648 140.858 192.003 142.415C194.358 143.972 196.169 146.103 197.439 148.81C198.376 150.805 198.868 152.668 198.915 154.398C198.964 156.13 198.62 157.627 197.886 158.892C197.151 160.158 196.083 161.127 194.682 161.803C193.522 162.361 192.412 162.597 191.351 162.51C190.29 162.423 189.34 161.997 188.499 161.234C188.332 163.679 187.01 165.499 184.538 166.691C183.247 167.313 181.88 167.543 180.435 167.382C178.991 167.222 177.639 166.671 176.378 165.728C175.116 164.786 174.101 163.494 173.331 161.853C172.56 160.212 172.207 158.602 172.27 157.021C172.334 155.441 172.761 154.037 173.555 152.812C174.35 151.588 175.402 150.658 176.716 150.026C178.642 149.097 180.458 148.996 182.169 149.723L181.404 148.093L186.755 145.514L191.517 155.66C191.851 156.368 192.222 156.816 192.63 157C193.038 157.183 193.46 157.17 193.898 156.96C195.278 156.295 195.16 154.244 193.547 150.807C192.558 148.7 191.191 147.062 189.448 145.89C187.703 144.718 185.729 144.098 183.524 144.033C181.32 143.967 179.056 144.493 176.737 145.61C174.438 146.718 172.631 148.201 171.317 150.058C170.001 151.916 169.265 153.963 169.108 156.2C168.949 158.438 169.386 160.655 170.416 162.85C171.468 165.09 172.892 166.864 174.687 168.175C176.483 169.485 178.493 170.207 180.719 170.346C182.943 170.483 185.205 169.998 187.503 168.891C189.845 167.763 191.793 166.226 193.349 164.28L196.235 167.254C195.479 168.216 194.473 169.176 193.219 170.134C191.964 171.092 190.636 171.908 189.237 172.583C186.063 174.113 182.955 174.794 179.912 174.629C176.868 174.464 174.138 173.537 171.722 171.846C169.304 170.157 167.414 167.859 166.05 164.954C164.698 162.072 164.144 159.149 164.393 156.189C164.639 153.228 165.671 150.494 167.487 147.988C169.301 145.481 171.807 143.458 175.003 141.918C178.045 140.453 181.05 139.779 184.018 139.899Z","fill","#ADE0EC"],["id","g352"],["id","path354","d","M478.281 145.979L473.499 145.088L471.809 150.637L476.591 151.528L478.281 145.979ZM483.567 146.965L481.877 152.514L486.737 153.418L485.812 158.499L480.333 157.478L478.528 163.209L473.241 162.224L475.046 156.492L470.263 155.601L468.46 161.331L463.174 160.347L464.977 154.616L460.079 153.702L461.001 148.622L466.522 149.65L468.214 144.102L463.314 143.19L464.237 138.109L469.759 139.138L471.562 133.407L476.848 134.393L475.043 140.124L479.826 141.015L481.629 135.284L486.917 136.269L485.112 142.001L490.01 142.913L489.088 147.994L483.567 146.965Z","fill","#ADE0EC"],["id","g356"],["id","path358","d","M230.094 489.727H164.645C144.782 489.727 128.679 473.412 128.679 453.286C128.679 433.159 144.782 416.844 164.645 416.844H194.128C213.99 416.844 230.094 433.159 230.094 453.286V489.727Z","fill","#FFBF69"],["id","g360"],["id","path362","d","M190.288 474.567C192.225 471.057 193.491 467.457 194.24 463.884C197.265 463.216 199.718 462.418 201.535 461.712C199.468 467.269 195.439 471.849 190.288 474.567ZM173.549 476.516C170.414 472.301 168.399 468.049 167.204 463.913C172.228 464.889 176.849 465.295 180.987 465.295C184.501 465.295 187.666 465.013 190.478 464.585C189.44 468.665 187.643 472.75 184.795 476.628C183.054 477.042 181.249 477.283 179.386 477.283C177.368 477.283 175.42 476.999 173.549 476.516ZM157.077 461.27C159.25 461.983 161.355 462.573 163.406 463.075C164.255 466.725 165.672 470.467 167.822 474.207C162.852 471.377 159.006 466.783 157.077 461.27ZM166.919 432.92C165.905 435.193 164.777 438.165 163.89 441.631C161.455 442.199 159.416 442.847 157.807 443.446C159.751 439.087 162.942 435.428 166.919 432.92ZM185.694 430.179C186.289 431.348 188.269 435.45 189.79 441.13C180.926 439.619 173.434 439.938 167.6 440.897C169.168 435.61 171.267 431.824 172.077 430.47C174.382 429.71 176.835 429.288 179.386 429.288C181.572 429.288 183.682 429.614 185.694 430.179ZM201.203 443.946C198.569 443.098 196.02 442.407 193.568 441.864C192.612 437.856 191.394 434.47 190.4 432.058C195.218 434.635 199.063 438.835 201.203 443.946ZM194.354 445.71C196.968 446.339 199.688 447.138 202.507 448.133C202.868 449.796 203.071 451.515 203.071 453.285C203.071 454.669 202.929 456.014 202.707 457.334C201.441 457.942 198.765 459.081 194.862 460.045C195.44 454.989 195.108 450.091 194.354 445.71ZM166.64 444.734C172.634 443.581 180.793 443.047 190.668 444.909C191.612 449.668 192.068 455.159 191.237 460.804C184.963 461.903 176.497 462.275 166.311 460.097C165.321 454.509 165.701 449.252 166.64 444.734ZM155.701 453.285C155.701 451.44 155.927 449.649 156.319 447.921C157.561 447.343 159.839 446.402 163.05 445.549C162.325 449.694 162.056 454.341 162.712 459.24C160.557 458.67 158.328 457.976 156.039 457.161C155.835 455.896 155.701 454.608 155.701 453.285ZM179.386 425.733C164.391 425.733 152.192 438.093 152.192 453.285C152.192 468.479 164.391 480.838 179.386 480.838C194.381 480.838 206.58 468.479 206.58 453.285C206.58 438.093 194.381 425.733 179.386 425.733Z","fill","#FAFAFA"],["id","g364"],["id","path366","d","M487.575 534.716H553.024C572.888 534.716 588.99 518.4 588.99 498.275C588.99 478.149 572.888 461.834 553.024 461.834H523.541C503.679 461.834 487.575 478.149 487.575 498.275V534.716Z","fill","#FFBF69"],["id","g368"],["id","path370","d","M565.214 487.805C565.214 477.497 549.034 468.633 538.283 477.531C527.532 468.633 511.352 477.497 511.352 487.805C511.352 487.805 507.872 508.014 538.283 522.676C568.694 508.014 565.214 487.805 565.214 487.805Z","stroke","#FAFAFA","stroke-width","3.811","stroke-miterlimit","10","stroke-linejoin","round"],["id","g372"],["id","path374","d","M466.093 53.4869C465.677 53.3258 465.259 53.1899 464.843 53.074C464.729 52.6558 464.594 52.2389 464.437 51.8207C463.767 50.1411 462.888 48.4615 461.12 46.7819C459.352 48.4615 458.474 50.1411 457.804 51.8207C457.645 52.2415 457.51 52.6638 457.395 53.0847C456.978 53.2019 456.563 53.3391 456.147 53.4989C454.489 54.1782 452.832 55.0679 451.174 56.8594C452.832 58.6509 454.489 59.5406 456.147 60.2199C456.56 60.3797 456.973 60.5156 457.384 60.6315C457.499 61.0537 457.633 61.4759 457.792 61.8982C458.46 63.5777 459.342 65.2573 461.12 66.9369C462.899 65.2573 463.781 63.5777 464.449 61.8982C464.605 61.4799 464.741 61.0617 464.855 60.6421C465.267 60.5276 465.681 60.3917 466.093 60.2319C467.751 59.5553 469.409 58.6615 471.067 56.8594C469.409 55.0573 467.751 54.1635 466.093 53.4869Z","fill","#ADE0EC"],["id","star1"],["id","path378","d","M18.666 335.315C18.2493 335.154 17.8325 335.016 17.4145 334.901C17.3001 334.484 17.166 334.067 17.0096 333.649C16.3392 331.968 15.461 330.289 13.6929 328.61C11.9247 330.289 11.0466 331.968 10.3761 333.649C10.2171 334.069 10.0816 334.492 9.96728 334.913C9.55186 335.028 9.13514 335.167 8.71972 335.327C7.06201 336.006 5.4043 336.896 3.74658 338.687C5.4043 340.479 7.06201 341.369 8.71972 342.048C9.13251 342.206 9.54398 342.342 9.95676 342.458C10.0698 342.882 10.2052 343.304 10.3643 343.725C11.0321 345.406 11.9142 347.085 13.6929 348.765C15.4715 347.085 16.3536 345.406 17.0214 343.725C17.1779 343.308 17.3133 342.89 17.4263 342.47C17.8391 342.354 18.2532 342.22 18.666 342.058C20.3237 341.383 21.9814 340.489 23.6391 338.687C21.9814 336.885 20.3237 335.991 18.666 335.315Z","fill","#ADE0EC"],["id","g380"],["id","path382","d","M500.378 253.717C499.962 253.558 499.545 253.42 499.128 253.305C499.014 252.886 498.878 252.469 498.722 252.052C498.052 250.372 497.173 248.692 495.405 247.012C493.637 248.692 492.759 250.372 492.089 252.052C491.931 252.472 491.795 252.894 491.681 253.317C491.264 253.432 490.849 253.57 490.433 253.729C488.774 254.409 487.117 255.298 485.459 257.09C487.117 258.881 488.774 259.772 490.433 260.45C490.845 260.61 491.258 260.746 491.669 260.862C491.784 261.284 491.918 261.706 492.078 262.129C492.745 263.808 493.627 265.488 495.405 267.167C497.184 265.488 498.066 263.808 498.734 262.129C498.892 261.71 499.026 261.292 499.14 260.874C499.553 260.758 499.966 260.622 500.378 260.462C502.037 259.786 503.694 258.892 505.352 257.09C503.694 255.289 502.037 254.395 500.378 253.717Z","fill","#ADE0EC"],["id","g384"],["id","path386","d","M673.413 79.5778C673.204 79.4978 672.995 79.4286 672.785 79.3713C672.729 79.1622 672.662 78.9517 672.583 78.7426C672.246 77.9008 671.806 77.059 670.921 76.2172C670.035 77.059 669.595 77.9008 669.258 78.7426C669.178 78.9544 669.112 79.1648 669.054 79.3766C668.844 79.4352 668.636 79.5032 668.429 79.5844C667.596 79.9241 666.766 80.3703 665.936 81.2693C666.766 82.1657 667.596 82.6119 668.429 82.9529C668.635 83.0328 668.84 83.1008 669.048 83.158C669.106 83.3698 669.173 83.5816 669.253 83.7947C669.587 84.6352 670.03 85.4769 670.921 86.3201C671.811 85.4769 672.254 84.6352 672.589 83.7947C672.668 83.5842 672.734 83.3738 672.792 83.1647C672.999 83.1061 673.206 83.0382 673.413 82.9596C674.244 82.6199 675.075 82.1711 675.906 81.2693C675.075 80.3649 674.244 79.9174 673.413 79.5778Z","fill","#D0F6FF"],["id","g388"],["id","path390","d","M724.621 229.528C724.413 229.448 724.204 229.379 723.994 229.321C723.936 229.112 723.87 228.902 723.791 228.694C723.455 227.851 723.014 227.009 722.128 226.167C721.244 227.009 720.803 227.851 720.467 228.694C720.387 228.904 720.32 229.115 720.262 229.327C720.053 229.385 719.845 229.453 719.636 229.534C718.805 229.874 717.974 230.32 717.145 231.219C717.974 232.116 718.805 232.562 719.636 232.903C719.842 232.983 720.049 233.051 720.256 233.108C720.314 233.32 720.38 233.532 720.46 233.745C720.795 234.585 721.238 235.427 722.128 236.27C723.02 235.427 723.461 234.585 723.797 233.745C723.877 233.534 723.943 233.324 723.999 233.115C724.208 233.056 724.415 232.988 724.621 232.91C725.453 232.57 726.284 232.121 727.113 231.219C726.284 230.315 725.453 229.867 724.621 229.528Z","fill","#D0F6FF"],["id","g392"],["id","path394","d","M722.669 226.015C722.46 225.935 722.251 225.866 722.042 225.809C721.984 225.6 721.918 225.389 721.838 225.18C721.503 224.338 721.063 223.497 720.177 222.655C719.291 223.497 718.85 224.338 718.515 225.18C718.435 225.392 718.368 225.602 718.31 225.814C718.101 225.873 717.892 225.941 717.684 226.022C716.853 226.362 716.022 226.808 715.192 227.707C716.022 228.603 716.853 229.049 717.684 229.39C717.891 229.47 718.097 229.538 718.305 229.595C718.361 229.807 718.428 230.019 718.508 230.232C718.844 231.073 719.285 231.914 720.177 232.758C721.068 231.914 721.51 231.073 721.845 230.232C721.924 230.022 721.991 229.811 722.047 229.602C722.255 229.544 722.463 229.476 722.669 229.397C723.5 229.057 724.331 228.609 725.162 227.707C724.331 226.802 723.5 226.355 722.669 226.015Z","fill","#D0F6FF"],["id","g396"],["id","path398","d","M122.37 271.837C122.161 271.756 121.952 271.688 121.742 271.63C121.686 271.421 121.619 271.211 121.54 271.002C121.203 270.16 120.763 269.318 119.877 268.476C118.991 269.318 118.551 270.16 118.215 271.002C118.135 271.213 118.068 271.424 118.01 271.636C117.801 271.694 117.594 271.762 117.385 271.842C116.554 272.183 115.723 272.629 114.892 273.527C115.723 274.425 116.554 274.871 117.385 275.212C117.591 275.291 117.797 275.36 118.005 275.417C118.062 275.629 118.129 275.841 118.209 276.052C118.544 276.894 118.986 277.736 119.877 278.578C120.768 277.736 121.211 276.894 121.545 276.052C121.624 275.843 121.691 275.633 121.748 275.422C121.955 275.365 122.163 275.297 122.37 275.217C123.2 274.878 124.031 274.43 124.862 273.527C124.031 272.624 123.2 272.176 122.37 271.837Z","fill","#ADE0EC"],["id","g400"],["id","path402","d","M30.9696 538.087C30.7606 538.007 30.5516 537.939 30.3426 537.881C30.2847 537.671 30.219 537.461 30.1401 537.252C29.8036 536.41 29.3632 535.568 28.4772 534.728C27.5911 535.568 27.1507 536.41 26.8155 537.252C26.7353 537.464 26.6683 537.674 26.6104 537.887C26.4014 537.945 26.1937 538.012 25.9847 538.094C25.1538 538.435 24.323 538.881 23.4922 539.779C24.323 540.675 25.1538 541.121 25.9847 541.462C26.1911 541.542 26.3975 541.611 26.6052 541.667C26.6617 541.88 26.7301 542.092 26.8089 542.303C27.1442 543.146 27.5859 543.988 28.4772 544.829C29.3685 543.988 29.8115 543.146 30.1454 542.303C30.2243 542.094 30.2913 541.884 30.3478 541.674C30.5555 541.615 30.7633 541.549 30.9696 541.468C31.8005 541.128 32.6313 540.68 33.4621 539.779C32.6313 538.876 31.8005 538.427 30.9696 538.087Z","fill","#ADE0EC"],["id","g404"],["id","path406","d","M384.68 138.195C384.471 138.114 384.262 138.046 384.053 137.989C383.995 137.78 383.928 137.569 383.849 137.36C383.514 136.518 383.073 135.676 382.187 134.835C381.301 135.676 380.861 136.518 380.524 137.36C380.445 137.572 380.377 137.782 380.32 137.994C380.111 138.053 379.904 138.121 379.695 138.202C378.864 138.541 378.033 138.988 377.202 139.885C378.033 140.783 378.864 141.229 379.695 141.57C379.901 141.65 380.107 141.718 380.314 141.775C380.372 141.987 380.439 142.199 380.519 142.411C380.854 143.253 381.296 144.094 382.187 144.936C383.078 144.094 383.52 143.253 383.855 142.411C383.934 142.202 384.001 141.991 384.058 141.781C384.266 141.723 384.472 141.656 384.68 141.576C385.51 141.236 386.341 140.788 387.172 139.885C386.341 138.982 385.51 138.535 384.68 138.195Z","fill","#ADE0EC"],["id","g408"],["id","path410","d","M143.253 52.4684C143.044 52.3885 142.835 52.3192 142.626 52.262C142.568 52.0528 142.501 51.8424 142.423 51.6333C142.087 50.7915 141.646 49.9497 140.76 49.1079C139.874 49.9497 139.434 50.7915 139.097 51.6333C139.019 51.8451 138.951 52.0555 138.894 52.2673C138.685 52.3259 138.477 52.3938 138.268 52.4751C137.437 52.8147 136.606 53.2609 135.775 54.1586C136.606 55.0564 137.437 55.5026 138.268 55.8436C138.474 55.9235 138.681 55.9914 138.888 56.0487C138.945 56.2605 139.012 56.4722 139.092 56.6854C139.427 57.5258 139.869 58.3676 140.76 59.2107C141.652 58.3676 142.093 57.5258 142.429 56.6854C142.507 56.4749 142.575 56.2645 142.631 56.0553C142.839 55.9967 143.045 55.9288 143.253 55.8502C144.084 55.5106 144.915 55.0617 145.745 54.1586C144.915 53.2556 144.084 52.8081 143.253 52.4684Z","fill","#ADE0EC"],["id","star4"],["id","path414","d","M659.175 279.551C658.966 279.47 658.757 279.402 658.546 279.344C658.49 279.135 658.423 278.925 658.344 278.716C658.009 277.874 657.567 277.032 656.682 276.19C655.796 277.032 655.356 277.874 655.019 278.716C654.939 278.926 654.873 279.138 654.816 279.35C654.605 279.408 654.397 279.476 654.19 279.556C653.359 279.897 652.527 280.343 651.697 281.241C652.527 282.139 653.359 282.585 654.19 282.926C654.396 283.005 654.603 283.074 654.81 283.131C654.867 283.343 654.934 283.555 655.014 283.766C655.349 284.608 655.791 285.45 656.682 286.292C657.574 285.45 658.015 284.608 658.35 283.766C658.429 283.557 658.495 283.347 658.553 283.136C658.761 283.079 658.968 283.011 659.175 282.931C660.006 282.592 660.836 282.144 661.667 281.241C660.836 280.338 660.006 279.89 659.175 279.551Z","fill","#ADE0EC"],["id","star5"],["id","path418","d","M412.477 191.341C412.268 191.26 412.059 191.192 411.85 191.133C411.793 190.924 411.727 190.715 411.647 190.506C411.311 189.664 410.871 188.822 409.985 187.98C409.099 188.822 408.659 189.664 408.323 190.506C408.243 190.718 408.176 190.928 408.118 191.14C407.909 191.197 407.7 191.266 407.492 191.346C406.662 191.687 405.831 192.133 405 193.031C405.831 193.929 406.662 194.375 407.492 194.715C407.699 194.795 407.905 194.864 408.113 194.921C408.17 195.133 408.237 195.345 408.317 195.556C408.652 196.398 409.094 197.24 409.985 198.082C410.876 197.24 411.318 196.398 411.653 195.556C411.732 195.346 411.799 195.137 411.856 194.926C412.063 194.869 412.271 194.801 412.477 194.721C413.308 194.382 414.139 193.934 414.97 193.031C414.139 192.128 413.308 191.681 412.477 191.341Z","fill","#D0F6FF"],["id","star2"],["id","path422","d","M318.495 91.4014C318.129 91.2602 317.762 91.1403 317.396 91.0391C317.295 90.6715 317.178 90.3039 317.04 89.9363C316.45 88.4605 315.678 86.9847 314.124 85.5075C312.57 86.9847 311.797 88.4605 311.208 89.9363C311.069 90.3079 310.95 90.6782 310.85 91.0484C310.483 91.151 310.117 91.2709 309.752 91.4121C308.295 92.0088 306.837 92.792 305.381 94.3663C306.837 95.9407 308.295 96.7239 309.752 97.3206C310.115 97.4604 310.476 97.5803 310.839 97.6815C310.939 98.0531 311.059 98.4234 311.198 98.795C311.786 100.272 312.56 101.749 314.124 103.225C315.687 101.749 316.463 100.272 317.049 98.795C317.187 98.4274 317.306 98.0598 317.406 97.6922C317.77 97.5896 318.132 97.4711 318.495 97.3312C319.953 96.7358 321.41 95.95 322.868 94.3663C321.41 92.7826 319.953 91.9968 318.495 91.4014Z","fill","#ADE0EC"],["id","g424"],["id","path426","d","M95.3161 198.94C94.9494 198.801 94.5826 198.679 94.2171 198.578C94.1159 198.21 93.9989 197.843 93.8609 197.475C93.2706 195.999 92.4989 194.524 90.9451 193.047C89.3912 194.524 88.6182 195.999 88.0293 197.475C87.8899 197.847 87.7703 198.217 87.6704 198.587C87.3036 198.69 86.9382 198.81 86.5727 198.951C85.1161 199.548 83.6582 200.331 82.2017 201.905C83.6582 203.48 85.1161 204.263 86.5727 204.86C86.9355 204.999 87.2971 205.119 87.6599 205.221C87.7598 205.592 87.8794 205.962 88.0188 206.334C88.6064 207.811 89.3807 209.288 90.9451 210.764C92.5081 209.288 93.2838 207.811 93.8701 206.334C94.0081 205.966 94.1264 205.599 94.2263 205.231C94.5892 205.129 94.9533 205.01 95.3161 204.87C96.774 204.275 98.2306 203.49 99.6885 201.905C98.2306 200.322 96.774 199.536 95.3161 198.94Z","fill","#ADE0EC"],["id","star3"],["id","path430","d","M567.016 163.164C566.649 163.023 566.282 162.903 565.915 162.8C565.815 162.434 565.697 162.066 565.559 161.699C564.97 160.223 564.197 158.746 562.643 157.27C561.089 158.746 560.316 160.223 559.728 161.699C559.59 162.069 559.47 162.441 559.369 162.81C559.003 162.912 558.638 163.033 558.272 163.175C556.814 163.771 555.358 164.553 553.9 166.129C555.358 167.703 556.814 168.486 558.272 169.082C558.634 169.222 558.997 169.343 559.359 169.444C559.459 169.816 559.579 170.186 559.717 170.558C560.306 172.035 561.08 173.51 562.643 174.986C564.206 173.51 564.982 172.035 565.57 170.558C565.708 170.19 565.826 169.822 565.926 169.453C566.289 169.352 566.653 169.234 567.016 169.094C568.472 168.498 569.93 167.713 571.387 166.129C569.93 164.545 568.472 163.759 567.016 163.164Z","fill","#D0F6FF"],["id","star6"],["id","path434","d","M785.486 113.408C785.119 113.267 784.752 113.147 784.385 113.045C784.285 112.678 784.167 112.31 784.03 111.943C783.44 110.467 782.667 108.99 781.113 107.514C779.559 108.99 778.786 110.467 778.198 111.943C778.059 112.314 777.94 112.685 777.839 113.055C777.473 113.157 777.108 113.277 776.742 113.418C775.284 114.015 773.828 114.798 772.37 116.373C773.828 117.947 775.284 118.73 776.742 119.327C777.104 119.467 777.467 119.587 777.829 119.688C777.929 120.06 778.049 120.43 778.187 120.801C778.776 122.279 779.55 123.756 781.113 125.231C782.676 123.756 783.452 122.279 784.04 120.801C784.178 120.434 784.296 120.066 784.396 119.697C784.759 119.596 785.123 119.477 785.486 119.338C786.942 118.742 788.4 117.956 789.857 116.373C788.4 114.789 786.942 114.003 785.486 113.408Z","fill","#D0F6FF"],["id","g436"],["id","path438","d","M556.27 45.0362C555.903 44.895 555.536 44.7752 555.169 44.6739C555.069 44.3063 554.951 43.9387 554.813 43.5711C554.224 42.0953 553.451 40.6182 551.897 39.1424C550.343 40.6182 549.57 42.0953 548.983 43.5711C548.843 43.9427 548.724 44.313 548.624 44.6833C548.257 44.7858 547.892 44.9057 547.526 45.0469C546.068 45.6436 544.612 46.4268 543.154 48.0011C544.612 49.5755 546.068 50.3587 547.526 50.9554C547.888 51.0953 548.251 51.2151 548.613 51.3164C548.713 51.688 548.833 52.0583 548.971 52.4299C549.56 53.907 550.334 55.3841 551.897 56.8599C553.46 55.3841 554.236 53.907 554.824 52.4299C554.962 52.0622 555.08 51.6946 555.18 51.327C555.543 51.2245 555.907 51.1059 556.27 50.9661C557.726 50.3707 559.184 49.5848 560.641 48.0011C559.184 46.4175 557.726 45.6316 556.27 45.0362Z","fill","#D0F6FF"],["id","contact-form","action","https://formspree.io/f/mldrlygg","method","POST",3,"submit"],[1,"title","text-center","mb-4","theme-text-primary","transition-colors","duration-300"],[1,"form-group","position-relative"],["for","name",1,"d-block"],["data-feather","user",1,"icon"],["type","text","id","name","name","name","placeholder","Name","required","",1,"form-control","form-control-lg","thick",3,"input"],["for","email",1,"d-block"],["data-feather","mail",1,"icon"],["type","email","id","email","name","email","placeholder","E-mail","required","",1,"form-control","form-control-lg","thick",3,"input"],[1,"form-group","message"],["id","message","name","message","rows","7","placeholder","Message","required","",1,"form-control","form-control-lg",3,"input"],[1,"text-center"],["type","submit",1,"btn","btn-primary"],[3,"ngClass",4,"ngIf"],[3,"ngClass"],["class","w-4 h-4","fill","none","stroke","currentColor","stroke-width","2","viewBox","0 0 24 24",4,"ngIf"],["fill","none","stroke","currentColor","stroke-width","2","viewBox","0 0 24 24",1,"w-4","h-4"],["stroke-linecap","round","stroke-linejoin","round","d","M5 13l4 4L19 7"],["stroke-linecap","round","stroke-linejoin","round","d","M6 18L18 6M6 6l12 12"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"h2",1),O(2,"Contact me "),p()(),h(3,"div",2),vt(),h(4,"svg",3)(5,"g",4)(6,"g",5)(7,"g",6)(8,"g",7),y(9,"path",8),p(),h(10,"g",9),y(11,"path",10),p(),h(12,"g",11),y(13,"path",12),p(),h(14,"g",13),y(15,"path",14),p(),h(16,"g",15),y(17,"path",16),p(),h(18,"g",17),y(19,"path",18),p(),h(20,"g",19),y(21,"path",20),p(),h(22,"g",21),y(23,"path",22),p(),h(24,"g",23),y(25,"path",24),p(),h(26,"g",25),y(27,"path",26),p(),h(28,"g",27),y(29,"path",28),p(),h(30,"g",29),y(31,"path",30),p(),h(32,"g",31),y(33,"path",32),p(),h(34,"g",33),y(35,"path",34),p(),h(36,"g",35),y(37,"path",36),p(),h(38,"g",37),y(39,"path",38),p(),h(40,"g",39),y(41,"path",40),p(),h(42,"g",41),y(43,"path",42),p(),h(44,"g",43),y(45,"path",44),p(),h(46,"g",45),y(47,"path",46),p(),h(48,"g",47),y(49,"path",48),p(),h(50,"g",49),y(51,"path",50),p(),h(52,"g",51),y(53,"path",52),p(),h(54,"g",53),y(55,"path",54),p(),h(56,"g",55),y(57,"path",56),p(),h(58,"g",57),y(59,"path",58),p(),h(60,"g",59),y(61,"path",60),p(),h(62,"g",61),y(63,"path",62),p(),h(64,"g",63),y(65,"path",64),p(),h(66,"g",65),y(67,"path",66),p(),h(68,"g",67),y(69,"path",68),p(),h(70,"g",69),y(71,"path",70),p(),h(72,"g",71),y(73,"path",72),p(),h(74,"g",73),y(75,"path",74),p(),h(76,"g",75),y(77,"path",76),p(),h(78,"g",77),y(79,"path",78),p(),h(80,"g",79),y(81,"path",80),p(),h(82,"g",81),y(83,"path",82),p(),h(84,"g",83),y(85,"path",84),p(),h(86,"g",85),y(87,"path",86),p(),y(88,"path",87)(89,"path",88)(90,"path",89)(91,"path",90)(92,"path",91),h(93,"g",92),y(94,"path",93),p(),h(95,"g",94),y(96,"path",95),p(),h(97,"g",96),y(98,"path",97),p(),h(99,"g",98),y(100,"path",99),p(),h(101,"g",100),y(102,"path",101),p(),y(103,"path",102)(104,"path",103)(105,"path",104)(106,"path",105)(107,"path",106)(108,"path",107)(109,"path",108)(110,"path",109)(111,"path",110)(112,"path",111)(113,"path",112)(114,"path",113),h(115,"g",114),y(116,"path",115),p(),h(117,"g",116),y(118,"path",117),p(),y(119,"path",118),h(120,"g",119),y(121,"path",120),p(),h(122,"g",121),y(123,"path",122),p(),y(124,"path",123),h(125,"g",124),y(126,"path",125),p(),h(127,"g",126),y(128,"path",127),p(),h(129,"g",128)(130,"g",129)(131,"g",130)(132,"g",131),y(133,"path",132),p(),h(134,"g",133),y(135,"path",134),p()()()(),h(136,"g",135)(137,"g",136)(138,"g",137)(139,"g",138),y(140,"path",139),p(),h(141,"g",140),y(142,"path",141),p()()()(),h(143,"g",142)(144,"g",143),y(145,"path",144),p(),y(146,"path",145)(147,"path",146)(148,"path",147)(149,"path",148)(150,"path",149)(151,"path",150)(152,"path",151)(153,"path",152),h(154,"g",153),y(155,"path",154),p(),h(156,"g",155),y(157,"path",156),p(),h(158,"g",157),y(159,"path",158),p()(),h(160,"g",159),y(161,"path",160),p(),h(162,"g",161),y(163,"path",162),p(),h(164,"g",163),y(165,"path",164),p(),h(166,"g",165),y(167,"path",166),p(),h(168,"g",167),y(169,"path",168),p(),h(170,"g",169),y(171,"path",170),p(),h(172,"g",171),y(173,"path",172),p(),h(174,"g",173),y(175,"path",174),p(),h(176,"g",175),y(177,"path",176),p(),h(178,"g",177),y(179,"path",178),p(),h(180,"g",179),y(181,"path",180),p(),h(182,"g",181),y(183,"path",182),p(),h(184,"g",183),y(185,"path",184),p(),h(186,"g",185),y(187,"path",186),p(),h(188,"g",187),y(189,"path",188),p(),h(190,"g",189),y(191,"path",190),p(),h(192,"g",191),y(193,"path",192),p(),h(194,"g",193),y(195,"path",194),p(),h(196,"g",195),y(197,"path",196),p(),h(198,"g",197),y(199,"path",198),p(),h(200,"g",199),y(201,"path",200),p(),h(202,"g",201),y(203,"path",202),p(),h(204,"g",203),y(205,"path",204),p()()()()(),ra(),h(206,"form",205),ve("submit",function(s){return i.onSubmit(s)}),h(207,"h1",206),O(208,"Talk to me"),p(),h(209,"div",207)(210,"label",208),y(211,"i",209),p(),h(212,"input",210),ve("input",function(){return i.onInputChange()}),p()(),h(213,"div",207)(214,"label",211),y(215,"i",212),p(),h(216,"input",213),ve("input",function(){return i.onInputChange()}),p()(),h(217,"div",214)(218,"textarea",215),ve("input",function(){return i.onInputChange()}),p()(),h(219,"div",216)(220,"button",217),O(221,"Send message"),p()()(),Ee(222,d4,5,7,"div",218),p()),r&2&&(H(222),X("ngIf",i.submissionStatus))},dependencies:[Oa,Ft,Hy,By,_f],styles:['*[_ngcontent-%COMP%]{margin-bottom:1rem}.contact-header[_ngcontent-%COMP%]{text-align:center;color:var(--text-primary);padding:1rem;position:relative;font-weight:600;font-size:2rem}.contact-header[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:150px;height:4px;background-color:var(--accent-pink);border-radius:2px}.container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(2rem,8vw,9rem);max-width:100vw;padding:0 1rem}svg[_ngcontent-%COMP%]{width:30%;height:auto;animation:_ngcontent-%COMP%_float 2s ease-in-out infinite;margin-left:10%}form[_ngcontent-%COMP%]{width:35%;background-color:#fff;padding:2rem;border-radius:3rem;box-shadow:0 10px 20px #0000001a;margin-right:10%;transition:all .3s ease}.dark[_nghost-%COMP%]   form[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   form[_ngcontent-%COMP%]{background-color:var(--bg-card);box-shadow:0 10px 30px #000000b3}.title[_ngcontent-%COMP%]{font-weight:600;color:var(--text-primary);font-size:2.5rem;text-align:center;margin-bottom:1.5rem;transition:color .3s ease}.dark[_nghost-%COMP%]   .title[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .title[_ngcontent-%COMP%]{color:var(--text-primary)}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:1rem 1rem 1rem 3rem;font-size:1.1rem;color:var(--text-secondary);background-color:var(--primary-pink-lightest);border:none;border-radius:2rem;box-shadow:0 7px 5px var(--shadow-pink);transition:all .3s ease}.dark[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .dark[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{background-color:var(--bg-tertiary);color:var(--text-on-bg);border:1px solid var(--border-default);box-shadow:0 7px 15px #00000080}.dark[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .dark[_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus, .dark   [_nghost-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{border-color:var(--primary-pink, #f472b6);box-shadow:0 0 0 3px #f472b64d}.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;height:7rem}.form-group[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:var(--primary-pink)}[_ngcontent-%COMP%]::placeholder{color:var(--primary-pink-light);font-weight:600;transition:color .3s ease}.dark[_nghost-%COMP%]   [_ngcontent-%COMP%]::placeholder, .dark   [_nghost-%COMP%]   [_ngcontent-%COMP%]::placeholder{color:var(--text-muted, #94a3b8)}.btn.btn-primary[_ngcontent-%COMP%]{display:inline-block;width:100%;padding:.8rem;font-size:1.1rem;font-weight:700;border:none;border-radius:3rem;background:linear-gradient(131deg,var(--primary-pink),var(--accent-pink),var(--primary-pink-light),var(--primary-pink-dark));background-size:300% 100%;transition:all .3s ease-in-out;color:#fff;cursor:pointer}.btn.btn-primary[_ngcontent-%COMP%]:hover{box-shadow:0 7px 5px var(--shadow-pink);background-size:100% 100%;transform:translateY(-.15em)}@keyframes _ngcontent-%COMP%_float{0%{transform:translateY(0)}50%{transform:translateY(-20px)}to{transform:translateY(0)}}@keyframes _ngcontent-%COMP%_blink{0%{opacity:0}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.container[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:2rem}svg[_ngcontent-%COMP%]{width:60%;margin-left:0}form[_ngcontent-%COMP%]{width:90%;margin-right:0}.title[_ngcontent-%COMP%]{font-size:2rem}.btn.btn-primary[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 480px){.title[_ngcontent-%COMP%]{font-size:1.8rem}.contact-header[_ngcontent-%COMP%]{font-size:1.5rem}form[_ngcontent-%COMP%]{padding:1.5rem}.btn.btn-primary[_ngcontent-%COMP%]{padding:.6rem}}input[_ngcontent-%COMP%]:placeholder-shown::placeholder, textarea[_ngcontent-%COMP%]:placeholder-shown::placeholder{color:#fff}.success-message[_ngcontent-%COMP%], .error-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;padding:1rem 1.25rem;border-radius:1rem;margin-top:1rem;font-size:1rem;font-weight:500;box-shadow:0 5px 15px #0000000d;animation:_ngcontent-%COMP%_fadeIn .4s ease-out;max-width:100%;transition:all .3s ease-in-out}.submission-message[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.4rem;padding:.25rem .5rem;margin-top:.5rem;font-size:.75rem;font-weight:500;border-radius:.75rem;max-width:100%;white-space:normal;word-break:break-word;animation:_ngcontent-%COMP%_fadeIn .4s ease-out;transition:all .3s ease-in-out}.success-message[_ngcontent-%COMP%]{background-color:#d1fae5;color:#065f46;border:1px solid #6ee7b7}.error-message[_ngcontent-%COMP%]{background-color:#fee2e2;color:#991b1b;border:1px solid #fca5a5}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}']})}return e})();var z1=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/skills";constructor(n){this.http=n}getSkills(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(M(sn))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var p4=["skillCard"];function g4(e,t){if(e&1&&(h(0,"div",7,0)(2,"div",8),y(3,"img",9),p(),h(4,"h3",10),O(5),p()()),e&2){let n=t.$implicit,r=t.index,i=Ge();Dt("visible",i.visible[r]),H(3),X("ngSrc",n.icon)("alt",n.name),H(2),xn(" ",n.name," ")}}var G1=(()=>{class e{skillsService;skills=[];visible=[];cards;constructor(n){this.skillsService=n}ngOnInit(){this.skillsService.getSkills().subscribe(n=>{this.skills=n.map(r=>({icon:r.photo,name:r.name})),this.visible=new Array(this.skills.length).fill(!1)},n=>console.error("Error fetching skills:",n))}ngAfterViewInit(){let n=new IntersectionObserver(r=>{r.forEach(i=>{let o=this.cards.toArray().findIndex(s=>s.nativeElement===i.target);i.isIntersecting&&o!==-1&&(this.visible[o]=!0,n.unobserve(i.target))})},{threshold:.15});this.cards.changes.subscribe(()=>{this.cards.forEach(r=>n.observe(r.nativeElement))})}static \u0275fac=function(r){return new(r||e)(U(z1))};static \u0275cmp=de({type:e,selectors:[["app-skills"]],viewQuery:function(r,i){if(r&1&&Sn(p4,5),r&2){let o;Kt(o=Xt())&&(i.cards=o)}},decls:7,vars:1,consts:[["skillCard",""],["id","skills",1,"pt-5","pb-12","contact-header","transition-colors","duration-300"],[1,"text-center","pt-20"],[1,"text-5xl","font-righteous","text-pink-600","dark:text-pink-400","mb-10","relative","transition-colors","duration-300"],[1,"block","w-16","h-1","bg-pink-400","dark:bg-pink-500","mx-auto","mt-2","rounded-full","transition-colors","duration-300"],[1,"grid","grid-cols-2","sm:grid-cols-3","md:grid-cols-4","lg:grid-cols-5","gap-6","px-6","sm:px-12"],["class",`skill-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border rounded-3xl p-6 shadow-md
             hover:shadow-pink-200 dark:hover:shadow-dark-pink
             transition-all duration-300 hover:scale-105 flex flex-col items-center text-center`,3,"visible",4,"ngFor","ngForOf"],[1,"skill-card","bg-white","dark:bg-dark-bg-card","border","border-pink-200","dark:border-dark-border","rounded-3xl","p-6","shadow-md","hover:shadow-pink-200","dark:hover:shadow-dark-pink","transition-all","duration-300","hover:scale-105","flex","flex-col","items-center","text-center"],[1,"w-20","h-20","flex","items-center","justify-center","rounded-full","bg-pink-100","dark:bg-dark-bg-hover","border","border-pink-300","dark:border-dark-pink-border","shadow-inner","mb-3","transition-all","duration-300"],["width","64","height","64",1,"w-10","h-10","object-contain",3,"ngSrc","alt"],[1,"text-pink-700","dark:text-pink-400","font-semibold","text-sm","tracking-wide","transition-colors","duration-300"]],template:function(r,i){r&1&&(h(0,"div",1)(1,"div",2)(2,"h2",3),O(3," Skills "),y(4,"span",4),p()(),h(5,"div",5),Ee(6,g4,6,5,"div",6),p()()),r&2&&(H(6),X("ngForOf",i.skills))},dependencies:[Jr,Xr],styles:[".skill-card[_ngcontent-%COMP%]{opacity:0;transform:translateY(30px);transition:all .6s ease-out}.skill-card.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.skill-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px) scale(1.05);box-shadow:0 8px 16px #ec489933}"]})}return e})();var q1=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/experiences";constructor(n){this.http=n}getExperiences(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(M(sn))};static \u0275prov=b({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var v4=["card"];function C4(e,t){if(e&1&&(h(0,"div",14,0)(2,"h3",15),O(3),p(),h(4,"p",16),O(5),p(),h(6,"p",17),O(7),Fi(8,"date"),Fi(9,"date"),p()()),e&2){let n=Ge(),r=n.$implicit,i=n.index,o=Ge();Dt("visible",o.cardVisible[i]),H(3),ct(r.title),H(2),ct(r.company),H(2),fa(" ",Li(8,6,r.from,"MMM yyyy")," - ",r.to?Li(9,9,r.to,"MMM yyyy"):"Present"," ")}}function D4(e,t){if(e&1&&(h(0,"div",18,0)(2,"h3",15),O(3),p(),h(4,"p",16),O(5),p(),h(6,"p",17),O(7),Fi(8,"date"),Fi(9,"date"),p()()),e&2){let n=Ge(),r=n.$implicit,i=n.index,o=Ge();Dt("visible",o.cardVisible[i]),H(3),ct(r.title),H(2),ct(r.company),H(2),fa(" ",Li(8,6,r.from,"MMM yyyy")," - ",r.to?Li(9,9,r.to,"MMM yyyy"):"Present"," ")}}function _4(e,t){if(e&1&&(h(0,"div",8)(1,"div",9),Ee(2,C4,10,12,"div",10),p(),y(3,"div",11),h(4,"div",12),Ee(5,D4,10,12,"div",13),p()()),e&2){let n=t.index;H(2),X("ngIf",n%2===0),H(3),X("ngIf",n%2!==0)}}var W1=(()=>{class e{experiencesService;experiences=[];cardVisible=[];cards;constructor(n){this.experiencesService=n}ngOnInit(){this.experiencesService.getExperiences().subscribe(n=>{this.experiences=n.map(r=>({title:r.title,company:r.company,from:r.from,to:r.to})),this.cardVisible=new Array(this.experiences.length).fill(!1)},n=>{console.error("Error fetching experiences:",n)})}ngAfterViewInit(){let n=new IntersectionObserver(r=>{r.forEach(i=>{let o=this.cards.toArray().findIndex(s=>s.nativeElement===i.target);i.isIntersecting&&o!==-1&&(this.cardVisible[o]=!0,n.unobserve(i.target))})},{threshold:.2});this.cards.changes.subscribe(()=>{this.cards.forEach(r=>n.observe(r.nativeElement))})}static \u0275fac=function(r){return new(r||e)(U(q1))};static \u0275cmp=de({type:e,selectors:[["app-experiences"]],viewQuery:function(r,i){if(r&1&&Sn(v4,5),r&2){let o;Kt(o=Xt())&&(i.cards=o)}},decls:8,vars:1,consts:[["card",""],["id","experience",1,"pt-5","transition-colors","duration-300"],[1,"text-center"],[1,"text-5xl","font-righteous","text-pink-600","dark:text-pink-400","mt-20","relative","transition-colors","duration-300"],[1,"block","w-16","h-1","bg-pink-400","dark:bg-pink-500","mx-auto","mt-8","mb-5","rounded-full","transition-colors","duration-300"],[1,"relative","max-w-5xl","mx-auto","px-4"],[1,"absolute","left-1/2","transform","-translate-x-1/2","h-full","border-l-2","border-pink-400","dark:border-pink-500","transition-colors","duration-300"],["class","mb-16 flex flex-col sm:flex-row items-center w-full relative",4,"ngFor","ngForOf"],[1,"mb-16","flex","flex-col","sm:flex-row","items-center","w-full","relative"],[1,"w-full","sm:w-1/2","pr-4","sm:pr-8","flex","justify-end","z-10"],["class",`timeline-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border p-6 rounded-lg shadow dark:shadow-dark-card w-full max-w-md text-right
         transition-transform transition-shadow duration-300 ease-in-out hover:scale-105 hover:shadow-xl`,3,"visible",4,"ngIf"],[1,"absolute","left-1/2","transform","-translate-x-1/2","w-5","h-5","bg-pink-400","dark:bg-pink-500","rounded-full","border-4","border-white","dark:border-dark-bg-primary","shadow","z-20","transition-all","duration-300"],[1,"w-full","sm:w-1/2","pl-4","sm:pl-8","flex","justify-start","z-10"],["class",`timeline-card bg-white dark:bg-dark-bg-card border border-pink-200 dark:border-dark-border p-6 rounded-lg shadow dark:shadow-dark-card w-full max-w-md text-left
         transition-transform transition-shadow duration-300 ease-in-out hover:scale-105 hover:shadow-xl`,3,"visible",4,"ngIf"],[1,"timeline-card","bg-white","dark:bg-dark-bg-card","border","border-pink-200","dark:border-dark-border","p-6","rounded-lg","shadow","dark:shadow-dark-card","w-full","max-w-md","text-right","transition-transform","transition-shadow","duration-300","ease-in-out","hover:scale-105","hover:shadow-xl"],[1,"text-xl","font-semibold","theme-text-secondary","transition-colors","duration-300"],[1,"text-pink-600","dark:text-pink-400","transition-colors","duration-300"],[1,"text-pink-500","dark:text-pink-300","text-sm","transition-colors","duration-300"],[1,"timeline-card","bg-white","dark:bg-dark-bg-card","border","border-pink-200","dark:border-dark-border","p-6","rounded-lg","shadow","dark:shadow-dark-card","w-full","max-w-md","text-left","transition-transform","transition-shadow","duration-300","ease-in-out","hover:scale-105","hover:shadow-xl"]],template:function(r,i){r&1&&(h(0,"div",1)(1,"div",2)(2,"h2",3),O(3," Education & Experience "),y(4,"span",4),p()(),h(5,"div",5),y(6,"div",6),Ee(7,_4,6,2,"div",7),p()()),r&2&&(H(7),X("ngForOf",i.experiences))},dependencies:[Xr,Ft,T0],styles:[".timeline-card[_ngcontent-%COMP%]{opacity:0;transform:translateY(30px);transition:all .6s ease-out}.timeline-card.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.timeline-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px) scale(1.03);box-shadow:0 12px 24px #ec489933}"]})}return e})();var Z1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=de({type:e,selectors:[["app-footer"]],decls:17,vars:0,consts:[[1,"theme-bg-gradient","dark:bg-gradient-to-r","dark:from-dark-bg-secondary","dark:to-dark-bg-tertiary","text-white","py-1","transition-all","duration-300"],[1,"flex","justify-center","gap-4"],[1,"text-sm","hover:text-pink-200","dark:hover:text-pink-400","transition-colors"],["href","mailto:<EMAIL>",1,"theme-text-secondary","dark:text-gray-300","hover:text-pink-200","dark:hover:text-pink-400","transition-colors","duration-300"],["href","https://www.instagram.com/roaaayman_10/",1,"theme-text-secondary","dark:text-gray-300","hover:text-pink-200","dark:hover:text-pink-400","transition-colors","duration-300"],["href","https://github.com/roaaayman21",1,"theme-text-secondary","dark:text-gray-300","hover:text-pink-200","dark:hover:text-pink-400","transition-colors","duration-300"],["href","https://wa.me/+2001151310078",1,"theme-text-secondary","dark:text-gray-300","hover:text-pink-200","dark:hover:text-pink-400","transition-colors","duration-300"],[1,"text-xl"]],template:function(r,i){r&1&&(h(0,"div",0)(1,"ul",1)(2,"li",2)(3,"a",3),O(4,"Email"),p()(),h(5,"li",2)(6,"a",4),O(7,"Instagram"),p()(),h(8,"li",2)(9,"a",5),O(10,"Github"),p()(),h(11,"li",2)(12,"a",6),O(13,"WhatsApp"),p()(),h(14,"li",7)(15,"p"),O(16,"\u{1F44B}"),p()()()())},styles:["div[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:100%;width:100%;background:transparent;background-size:cover;flex-direction:column}ul[_ngcontent-%COMP%]{display:inline-grid;grid-auto-flow:row;grid-gap:12px;justify-items:center;margin:auto;padding:10px;list-style:none;text-align:center}@media (min-width: 500px){ul[_ngcontent-%COMP%]{grid-auto-flow:column}}li[_ngcontent-%COMP%]{padding:5px}a[_ngcontent-%COMP%]{color:var(--text-secondary);text-decoration:none;font-size:1.2rem;box-shadow:inset 0 -1px 0 var(--border-pink)}a[_ngcontent-%COMP%]:hover{box-shadow:inset 0 -1.2em 0 var(--primary-pink-lighter)}li[_ngcontent-%COMP%]:last-child{grid-column:1 / 2;grid-row:1 / 2}li[_ngcontent-%COMP%]:hover ~ li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_wave-animation .3s infinite}@keyframes _ngcontent-%COMP%_wave-animation{0%,to{transform:rotate(0)}25%{transform:rotate(20deg)}75%{transform:rotate(-15deg)}}@media (max-width: 500px){div[_ngcontent-%COMP%]{background-size:200% 100%;background-position:center}}"]})}return e})();function E4(e,t){e&1&&(h(0,"div",1),y(1,"div",2),p())}var Q1=(()=>{class e{loading=!1;static \u0275fac=function(r){return new(r||e)};static \u0275cmp=de({type:e,selectors:[["app-loader"]],inputs:{loading:"loading"},decls:1,vars:1,consts:[["class","loader-overlay",4,"ngIf"],[1,"loader-overlay"],[1,"loader"]],template:function(r,i){r&1&&Ee(0,E4,2,0,"div",0),r&2&&X("ngIf",i.loading)},dependencies:[Ft],styles:['.loader-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#fff9;display:flex;align-items:center;justify-content:center;z-index:2000}.loader[_ngcontent-%COMP%]{width:28px;aspect-ratio:1;border-radius:50%;background:#e3aad6;transform-origin:top;display:grid;animation:_ngcontent-%COMP%_l3-0 1s infinite linear}.loader[_ngcontent-%COMP%]:before, .loader[_ngcontent-%COMP%]:after{content:"";grid-area:1/1;background:#f4dd51;border-radius:50%;transform-origin:top;animation:inherit;animation-name:_ngcontent-%COMP%_l3-1}.loader[_ngcontent-%COMP%]:after{background:#f10c49;--s:180deg}@keyframes _ngcontent-%COMP%_l3-0{0%,20%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_l3-1{50%{transform:rotate(var(--s,90deg))}to{transform:rotate(0)}}']})}return e})();var Y1=(()=>{class e{router;sliderService;loading=!0;currentIndex=0;slides=["first","about","projects","skills","experiences","contact"];get maxIndex(){return this.slides.length-1}constructor(n,r){this.router=n,this.sliderService=r,this.router.events.subscribe(i=>{i instanceof Fn?this.loading=!0:(i instanceof Mt||i instanceof Et||i instanceof yr)&&(this.loading=!1)}),this.sliderService.index$.subscribe(i=>{i!==this.currentIndex&&(this.currentIndex=i)})}title="portflio";prev(){this.currentIndex>0&&(this.currentIndex--,this.sliderService.setIndex(this.currentIndex))}next(){this.currentIndex<this.maxIndex&&(this.currentIndex++,this.sliderService.setIndex(this.currentIndex))}handleKey(n){n.key==="ArrowLeft"?this.prev():n.key==="ArrowRight"&&this.next()}static \u0275fac=function(r){return new(r||e)(U(cn),U(Rl))};static \u0275cmp=de({type:e,selectors:[["app-root"]],hostBindings:function(r,i){r&1&&ve("keydown",function(s){return i.handleKey(s)},!1,am)},decls:9,vars:1,consts:[[3,"loading"],["id","home"],["id","about"],["id","projects"],["id","skills"],["id","experiences"],["id","contact"]],template:function(r,i){r&1&&y(0,"app-loader",0)(1,"app-header",1)(2,"router-outlet")(3,"app-about",2)(4,"app-projects",3)(5,"app-skills",4)(6,"app-experiences",5)(7,"app-contact",6)(8,"app-footer"),r&2&&X("loading",i.loading)},dependencies:[Jf,Fl,B1,$1,H1,G1,W1,Z1,Q1],styles:["[_nghost-%COMP%]{display:block;width:100%;max-width:100vw;overflow-x:hidden}.slider-container[_ngcontent-%COMP%]{position:relative;width:100vw;height:100vh;overflow:hidden}.slider-track[_ngcontent-%COMP%]{display:flex;width:600vw;height:100%;transition:transform .6s ease-in-out}.slide[_ngcontent-%COMP%]{flex:0 0 100vw;height:100vh;overflow:hidden;position:relative;opacity:0;transform:translateY(20px) scale(.96);transition:opacity .6s ease-out,transform .6s ease-out;will-change:opacity,transform}.slide.active[_ngcontent-%COMP%]{opacity:1;transform:translateY(0) scale(1)}.slide.scrollable[_ngcontent-%COMP%]{overflow-y:auto}@media (max-width: 768px){.slide.mobile-scroll[_ngcontent-%COMP%]{overflow-y:auto}}.nav-btn[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);background:#0006;color:#fff;border:none;padding:.5rem 1rem;font-size:2rem;cursor:pointer;z-index:1000;-webkit-user-select:none;user-select:none}.nav-btn.left[_ngcontent-%COMP%]{left:10px}.nav-btn.right[_ngcontent-%COMP%]{right:10px}.nav-btn[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:default}@media (hover: none){.nav-btn[_ngcontent-%COMP%]{font-size:1.5rem}}.nav-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:48px;height:48px;border-radius:50%;background:#ffffffbf;-webkit-backdrop-filter:blur(6px);backdrop-filter:blur(6px);color:#e91e63;border:2px solid #e91e63;font-size:1.8rem;box-shadow:0 4px 12px #00000026;transition:transform .2s ease,background .2s ease}.nav-btn[_ngcontent-%COMP%]:hover{transform:translateY(-50%) scale(1.1);background:#e91e63;color:#fff}.nav-icon[_ngcontent-%COMP%]{line-height:1}"]})}return e})();var I4=[{path:"",component:Fl}],K1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=me({type:e});static \u0275inj=ge({imports:[Pa,Va,fl,hl,xo.forChild(I4)]})}return e})();var X1=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=me({type:e,bootstrap:[Y1]});static \u0275inj=ge({imports:[Ha,Ay,L1,Va,fl,hl,K1]})}return e})();K0().bootstrapModule(X1,{ngZoneEventCoalescing:!0}).catch(e=>console.error(e));
