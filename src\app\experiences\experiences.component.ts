import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChildren, AfterViewInit } from '@angular/core';

import { ExperiencesService } from '../experiences.service';
import {
  trigger,
  transition,
  style,
  animate,
  query,
  stagger,
} from '@angular/animations';

@Component({
  selector: 'app-experiences',
  templateUrl: './experiences.component.html',
  styleUrls: ['./experiences.component.css'],
})
export class ExperiencesComponent implements OnInit, AfterViewInit {
  experiences: any[] = [];
  cardVisible: boolean[] = [];

  @ViewChildren('card') cards!: QueryList<ElementRef>;

  constructor(private experiencesService: ExperiencesService) {}

  ngOnInit(): void {
    this.experiencesService.getExperiences().subscribe(
      (data: any[]) => {
        this.experiences = data.map((exp) => ({
          title: exp.title,
          company: exp.company,
          from: exp.from,
          to: exp.to,
        }));
        this.cardVisible = new Array(this.experiences.length).fill(false);
      },
      (error) => {
        console.error('Error fetching experiences:', error);
      }
    );
  }

  ngAfterViewInit(): void {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const index = this.cards.toArray().findIndex(card => card.nativeElement === entry.target);
          if (entry.isIntersecting && index !== -1) {
            this.cardVisible[index] = true;
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.2 }
    );

    this.cards.changes.subscribe(() => {
      this.cards.forEach(card => observer.observe(card.nativeElement));
    });
  }
}


