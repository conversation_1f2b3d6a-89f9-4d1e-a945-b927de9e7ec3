/* App component wrapper to prevent horizontal overflow */
:host {
  display: block;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

/* ----------- Horizontal Slider Styles ----------- */
.slider-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.slider-track {
  display: flex;
  width: 600vw; /* 6 slides x 100vw each */
  height: 100%;
  transition: transform 0.6s ease-in-out;
}

.slide {
  flex: 0 0 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  opacity: 0;
  transform: translateY(20px) scale(0.96);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  will-change: opacity, transform;
}

.slide.active {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* allow internal scrolling for designated sections */
.slide.scrollable {
  overflow-y: auto;
}

/* On small screens, allow scroll for specific slides */
@media (max-width: 768px) {
  .slide.mobile-scroll {
    overflow-y: auto;
  }
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 2rem;
  cursor: pointer;
  z-index: 1000;
  user-select: none;
}

.nav-btn.left {
  left: 10px;
}

.nav-btn.right {
  right: 10px;
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: default;
}

@media (hover: none) {
  .nav-btn {
    font-size: 1.5rem;
  }
}

/* --- Enhanced Arrow Buttons --- */
.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.75);
  backdrop-filter: blur(6px);
  color: #e91e63;
  border: 2px solid #e91e63;
  font-size: 1.8rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transition: transform 0.2s ease, background 0.2s ease;
}

.nav-btn:hover {
  transform: translateY(-50%) scale(1.1);
  background: #e91e63;
  color: #fff;
}

.nav-icon {
  line-height: 1;
}

