{"name": "portflio", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "deploy": "ng build --base-href https://roaaayman.github.io/portflio/ && npx angular-cli-ghpages --dir=dist/portflio"}, "private": true, "dependencies": {"@angular/animations": "^18.1.0", "@angular/common": "^18.1.0", "@angular/compiler": "^18.1.0", "@angular/core": "^18.1.0", "@angular/forms": "^18.1.0", "@angular/platform-browser": "^18.1.0", "@angular/platform-browser-dynamic": "^18.1.0", "@angular/router": "^18.1.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.1.1", "@angular/cli": "^18.2.8", "@angular/compiler-cli": "^18.1.0", "@types/jasmine": "~5.1.0", "angular-cli-ghpages": "^2.0.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.5.2"}}