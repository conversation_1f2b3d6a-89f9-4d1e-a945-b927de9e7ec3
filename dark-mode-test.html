<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Test CSS Variables */
        :root {
            --primary-pink: #db2777;
            --bg-primary: #ffffff;
            --text-on-bg: #1f2937;
            --bg-card: #ffffff;
            --border-default: #e5e7eb;
        }

        .dark {
            --bg-primary: #0f0f23;
            --text-on-bg: #f8fafc;
            --bg-card: #1e1e3f;
            --border-default: #334155;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-on-bg);
            transition: all 0.3s ease;
        }

        .test-card {
            background-color: var(--bg-card);
            border: 1px solid var(--border-default);
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold mb-8 text-pink-600 dark:text-pink-400">Dark Mode Test</h1>
        
        <button 
            onclick="toggleDarkMode()" 
            class="mb-8 px-6 py-3 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
        >
            Toggle Dark Mode
        </button>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="test-card p-6 rounded-lg">
                <h2 class="text-2xl font-semibold mb-4 text-pink-600 dark:text-pink-400">Contact Form Test</h2>
                <form class="space-y-4">
                    <input 
                        type="text" 
                        placeholder="Name" 
                        class="w-full p-3 rounded-lg bg-pink-50 dark:bg-gray-700 border border-pink-200 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                    >
                    <input 
                        type="email" 
                        placeholder="Email" 
                        class="w-full p-3 rounded-lg bg-pink-50 dark:bg-gray-700 border border-pink-200 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                    >
                    <textarea 
                        placeholder="Message" 
                        rows="4"
                        class="w-full p-3 rounded-lg bg-pink-50 dark:bg-gray-700 border border-pink-200 dark:border-gray-600 text-gray-900 dark:text-gray-100"
                    ></textarea>
                    <button 
                        type="submit" 
                        class="w-full py-3 bg-pink-600 dark:bg-pink-500 text-white rounded-lg hover:bg-pink-700 dark:hover:bg-pink-400 transition-colors"
                    >
                        Send Message
                    </button>
                </form>
            </div>

            <div class="test-card p-6 rounded-lg">
                <h2 class="text-2xl font-semibold mb-4 text-pink-600 dark:text-pink-400">About Section Test</h2>
                <p class="text-gray-700 dark:text-gray-300 mb-4">
                    This is a test of the about section text. In dark mode, this text should be light colored and easily readable against the dark background.
                </p>
                <button class="px-6 py-3 bg-gradient-to-r from-pink-600 to-pink-500 text-white rounded-full hover:from-pink-700 hover:to-pink-600 transition-all">
                    Resume
                </button>
            </div>
        </div>

        <div class="mt-8 test-card p-6 rounded-lg">
            <h2 class="text-2xl font-semibold mb-4 text-pink-600 dark:text-pink-400">Skills Grid Test</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-white dark:bg-gray-800 border border-pink-200 dark:border-gray-600 rounded-lg p-4 text-center">
                    <div class="w-12 h-12 bg-pink-100 dark:bg-pink-900 rounded-full mx-auto mb-2"></div>
                    <span class="text-pink-700 dark:text-pink-300 font-semibold">Angular</span>
                </div>
                <div class="bg-white dark:bg-gray-800 border border-pink-200 dark:border-gray-600 rounded-lg p-4 text-center">
                    <div class="w-12 h-12 bg-pink-100 dark:bg-pink-900 rounded-full mx-auto mb-2"></div>
                    <span class="text-pink-700 dark:text-pink-300 font-semibold">TypeScript</span>
                </div>
                <div class="bg-white dark:bg-gray-800 border border-pink-200 dark:border-gray-600 rounded-lg p-4 text-center">
                    <div class="w-12 h-12 bg-pink-100 dark:bg-pink-900 rounded-full mx-auto mb-2"></div>
                    <span class="text-pink-700 dark:text-pink-300 font-semibold">CSS</span>
                </div>
                <div class="bg-white dark:bg-gray-800 border border-pink-200 dark:border-gray-600 rounded-lg p-4 text-center">
                    <div class="w-12 h-12 bg-pink-100 dark:bg-pink-900 rounded-full mx-auto mb-2"></div>
                    <span class="text-pink-700 dark:text-pink-300 font-semibold">HTML</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleDarkMode() {
            document.documentElement.classList.toggle('dark');
        }

        // Initialize based on system preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
    </script>
</body>
</html>
