import { <PERSON>mponent, OnInit, ElementRef, <PERSON>ry<PERSON>ist, ViewChildren, AfterViewInit } from '@angular/core';
import { SkillsService } from '../skills.service';

@Component({
  selector: 'app-skills',
  templateUrl: './skills.component.html',
  styleUrls: ['./skills.component.css']
})
export class SkillsComponent implements OnInit, AfterViewInit {
  skills: any[] = [];
  visible: boolean[] = [];

  @ViewChildren('skillCard') cards!: QueryList<ElementRef>;

  constructor(private skillsService: SkillsService) {}

  ngOnInit(): void {
    this.skillsService.getSkills().subscribe(
      (data: any[]) => {
        this.skills = data.map(skill => ({
          icon: skill.photo,
          name: skill.name,
        }));
        this.visible = new Array(this.skills.length).fill(false);
      },
      error => console.error('Error fetching skills:', error)
    );
  }

  ngAfterViewInit(): void {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          const index = this.cards.toArray().findIndex(card => card.nativeElement === entry.target);
          if (entry.isIntersecting && index !== -1) {
            this.visible[index] = true;
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.15 }
    );

    this.cards.changes.subscribe(() => {
      this.cards.forEach(card => observer.observe(card.nativeElement));
    });
  }
}
