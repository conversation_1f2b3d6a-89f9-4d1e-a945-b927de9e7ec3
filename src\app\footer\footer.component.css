/* Ensure the container div uses global background */
div {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%; /* Full viewport height */
  width: 100%;
  /* Use transparent background to allow global gradient to show through */
  background: transparent;
  background-size: cover;
  flex-direction: column;
}

/* Styling the list (ul) */
ul {
  display: inline-grid;
  grid-auto-flow: row;
  grid-gap: 12px; /* Reduced grid gap */
  justify-items: center;
  margin: auto;
  padding: 10px; /* Added some padding for a cleaner look */
  list-style: none; /* Removed the default bullet points */
  text-align: center;
}

/* Changing list to a column on wider screens */
@media (min-width: 500px) {
  ul {
    grid-auto-flow: column;
  }
}

li {
  padding: 5px; /* Adjust padding for smaller size */
}

a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 1.2rem; /* Adjusted for readability */
  box-shadow: inset 0 -1px 0 var(--border-pink);
}

a:hover {
  box-shadow: inset 0 -1.2em 0 var(--primary-pink-lighter);
}

/* Animation for the waving emoji */
li:last-child {
  grid-column: 1 / 2;
  grid-row: 1 / 2;
}

li:hover ~ li p {
  animation: wave-animation 0.3s infinite;
}

/* Waving hand animation */
@keyframes wave-animation {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(20deg);
  }
  75% {
    transform: rotate(-15deg);
  }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 500px) {
  div {
    background-size: 200% 100%; /* Stretches the background on smaller screens */
    background-position: center;
  }
}
