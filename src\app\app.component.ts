import { Component, HostListener } from '@angular/core';
import { SliderService } from './services/slider.service';
import { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError, Event as RouterEvent } from '@angular/router';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  loading = true;

  // --- Slider State ---
  currentIndex = 0;
  readonly slides: string[] = ['first', 'about', 'projects', 'skills', 'experiences', 'contact'];
  get maxIndex(): number {
    return this.slides.length - 1;
  }


  constructor(private router: Router, private sliderService: SliderService) {
    this.router.events.subscribe((event: RouterEvent) => {
      if (event instanceof NavigationStart) {
        this.loading = true;
      } else if (
        event instanceof NavigationEnd ||
        event instanceof NavigationCancel ||
        event instanceof NavigationError
      ) {
        this.loading = false;
      }
    });

    // Sync with any outside navigation requests
    this.sliderService.index$.subscribe((i) => {
      if (i !== this.currentIndex) {
        this.currentIndex = i;
      }
    });
  }
  title = 'portflio';

  /** Navigate to previous slide */
  /** Navigate to previous slide */
  prev(): void {
    if (this.currentIndex > 0) {
      this.currentIndex--;
      this.sliderService.setIndex(this.currentIndex);
    }
  }

  /** Navigate to next slide */
  /** Navigate to next slide */
  next(): void {
    if (this.currentIndex < this.maxIndex) {
      this.currentIndex++;
      this.sliderService.setIndex(this.currentIndex);
    }
  }

  /** Optional: support keyboard navigation with arrow keys */
  @HostListener('window:keydown', ['$event'])
  handleKey(event: KeyboardEvent): void {
    if (event.key === 'ArrowLeft') {
      this.prev();
    } else if (event.key === 'ArrowRight') {
      this.next();
    }
  }

}
