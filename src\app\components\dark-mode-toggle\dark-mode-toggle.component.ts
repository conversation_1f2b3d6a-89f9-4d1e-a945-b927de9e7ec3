import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';
import { DarkModeService } from '../../services/dark-mode.service';

@Component({
  selector: 'app-dark-mode-toggle',
  templateUrl: './dark-mode-toggle.component.html',
  styleUrls: ['./dark-mode-toggle.component.css']
})
export class DarkModeToggleComponent implements OnInit, OnDestroy {
  isDarkMode = false;
  private subscription: Subscription = new Subscription();

  constructor(private darkModeService: DarkModeService) {}

  ngOnInit(): void {
    // Subscribe to dark mode changes
    this.subscription.add(
      this.darkModeService.isDarkMode$.subscribe(isDark => {
        this.isDarkMode = isDark;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  /**
   * Toggle dark mode when button is clicked
   */
  onToggle(): void {
    this.darkModeService.toggleDarkMode();
  }
}
